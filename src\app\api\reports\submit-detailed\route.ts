import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const reportData = await request.json();
    
    // Create detailed submitted report
    const submittedReport = await DataService.createReport({
      ...reportData,
      userId: request.user!.id,
      status: 'New',
      priority: reportData.urgencyLevel || 'Medium',
      isDraft: false,
      isDetailed: true,
      dateSubmitted: new Date(),
      lastUpdated: new Date()
    });
    
    return NextResponse.json({
      success: true,
      data: submittedReport,
      message: 'Detailed report submitted successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Submit detailed report API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit detailed report' },
      { status: 500 }
    );
  }
});