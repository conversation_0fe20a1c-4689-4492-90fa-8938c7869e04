import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      console.error('Recent activity API: No user in request');
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
    
    // For admin users, get company-wide activity; for whistleblowers, get user-specific activity
    const userId = request.user.role === 'whistleblower' ? request.user.id : undefined;
    const companyId = request.user.companyId;
    
    console.log('Recent activity API: User info', {
      userId: request.user.id,
      role: request.user.role,
      companyId,
      queryUserId: userId,
      limit
    });
    
    const activities = await DataService.getRecentActivity(userId, companyId, limit);
    
    console.log('Recent activity API: Activities retrieved', activities.length);
    
    return NextResponse.json({
      success: true,
      data: activities
    });
  } catch (error) {
    console.error('Recent activity API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});