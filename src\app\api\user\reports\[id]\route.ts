import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import Report from '@/lib/db/models/Report';

export const runtime = 'nodejs';



export const GET = withCompanyIsolation(async (
  request: AuthenticatedRequest
) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Extract ID from URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const id = pathSegments[pathSegments.length - 1];
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Report ID is required' },
        { status: 400 }
      );
    }
    
    // Find report by ID and ensure it belongs to the authenticated user
    const report = await Report.findOne({
      _id: id,
      userId: request.user.id // Ensure user can only access their own reports
    }).populate('assignedInvestigator', 'firstName lastName email');
    
    if (!report) {
      return NextResponse.json(
        { success: false, error: 'Report not found or access denied' },
        { status: 404 }
      );
    }
    
    // Transform report for frontend
    const transformedReport = {
      _id: report._id,
      reportId: report.reportId,
      title: report.title,
      description: report.description,
      category: report.category,
      status: report.status,
      priority: report.priority || report.urgencyLevel,
      urgencyLevel: report.urgencyLevel,
      isDraft: report.isDraft,
      isAnonymous: report.isAnonymous,
      dateSubmitted: report.dateSubmitted || report.submittedAt,
      lastUpdated: report.updatedAt,
      createdAt: report.createdAt,
      
      // Step 1 data
      dateOfOccurrence: report.dateOfOccurrence,
      location: report.location,
      
      // Step 2 data
      incidentDate: report.incidentDate,
      incidentTime: report.incidentTime,
      specificLocation: report.specificLocation,
      departmentInvolved: report.departmentInvolved,
      peopleInvolved: report.peopleInvolved,
      
      // Witness information
      hasWitnesses: report.hasWitnesses,
      witnessDetails: report.witnessDetails,
      
      // Evidence information
      hasEvidence: report.hasEvidence,
      evidenceDescription: report.evidenceDescription,
      evidenceFiles: report.evidenceFiles || report.evidence || [],
      
      // Impact assessment
      financialImpact: report.financialImpact,
      operationalImpact: report.operationalImpact,
      reputationalImpact: report.reputationalImpact,
      
      // Previous reports
      hasPreviousReports: report.hasPreviousReports,
      previousReportDetails: report.previousReportDetails,
      
      // Additional information
      additionalComments: report.additionalComments,
      
      // Communication preferences
      emailUpdates: report.emailUpdates,
      smsUpdates: report.smsUpdates,
      
      // Draft information
      draftStep: report.draftStep,
      lastSavedAt: report.lastSavedAt,
      
      // Investigation information
      assignedInvestigator: report.assignedInvestigator,
      progress: report.progress,
      estimatedCompletion: report.estimatedCompletion,
      tags: report.tags || []
    };
    
    return NextResponse.json({
      success: true,
      data: transformedReport
    });
  } catch (error) {
    console.error('Get user report by ID API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve report' },
      { status: 500 }
    );
  }
});

export const PUT = withCompanyIsolation(async (
  request: AuthenticatedRequest
) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Extract ID from URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const id = pathSegments[pathSegments.length - 1];
    const updateData = await request.json();
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Report ID is required' },
        { status: 400 }
      );
    }
    
    // Find report and ensure it belongs to the authenticated user
    const existingReport = await Report.findOne({
      _id: id,
      userId: request.user.id
    });
    
    if (!existingReport) {
      return NextResponse.json(
        { success: false, error: 'Report not found or access denied' },
        { status: 404 }
      );
    }
    
    // Only allow updates to draft reports or specific fields
    if (!existingReport.isDraft && !updateData.allowNonDraftUpdate) {
      return NextResponse.json(
        { success: false, error: 'Cannot modify submitted reports' },
        { status: 403 }
      );
    }
    
    // Update the report
    const updatedReport = await Report.findByIdAndUpdate(
      id,
      {
        ...updateData,
        lastUpdated: new Date(),
        lastSavedAt: new Date()
      },
      { new: true, runValidators: true }
    );
    
    return NextResponse.json({
      success: true,
      data: updatedReport,
      message: 'Report updated successfully'
    });
  } catch (error) {
    console.error('Update user report API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update report' },
      { status: 500 }
    );
  }
});

export const DELETE = withCompanyIsolation(async (
  request: AuthenticatedRequest
) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Extract ID from URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const id = pathSegments[pathSegments.length - 1];
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Report ID is required' },
        { status: 400 }
      );
    }
    
    // Find report and ensure it belongs to the authenticated user
    const report = await Report.findOne({
      _id: id,
      userId: request.user.id
    });
    
    if (!report) {
      return NextResponse.json(
        { success: false, error: 'Report not found or access denied' },
        { status: 404 }
      );
    }
    
    // Only allow deletion of draft reports
    if (!report.isDraft) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete submitted reports' },
        { status: 403 }
      );
    }
    
    // Delete the report
    await Report.findByIdAndDelete(id);
    
    return NextResponse.json({
      success: true,
      message: 'Report deleted successfully'
    });
  } catch (error) {
    console.error('Delete user report API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete report' },
      { status: 500 }
    );
  }
});
