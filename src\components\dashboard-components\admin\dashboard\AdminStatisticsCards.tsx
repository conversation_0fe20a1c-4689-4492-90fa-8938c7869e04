"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, AlertTriangle, Users, Shield } from "lucide-react";
import { useDashboardStats } from "@/hooks/useDashboardStats";

export default function AdminStatisticsCards() {
    const { stats, isLoading, error } = useDashboardStats();

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[...Array(4)].map((_, index) => (
                    <Card key={index} className="bg-white border-0 shadow-sm animate-pulse">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <div className="h-4 bg-gray-200 rounded w-20"></div>
                            <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="h-8 bg-gray-200 rounded mb-1"></div>
                            <div className="h-3 bg-gray-200 rounded w-16"></div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        );
    }

    if (error || !stats) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="bg-white border-0 shadow-sm col-span-full">
                    <CardContent className="p-6 text-center">
                        <p className="text-red-500">Failed to load admin statistics</p>
                        {error && <p className="text-sm text-gray-500 mt-1">{error}</p>}
                    </CardContent>
                </Card>
            </div>
        );
    }

    // Safely access nested properties with fallbacks
    const totalReports = stats.totalReports || 0;
    const newReports = stats.newReports || 0;
    const underReviewReports = stats.underReviewReports || 0;
    const resolvedReports = stats.resolvedReports || 0;
    const highPriorityReports = stats.highPriorityReports || 0;
    const awaitingResponseReports = stats.awaitingResponseReports || 0;
    const totalReportsChange = stats.periodComparison?.totalReportsChange || 0;

    const adminStats = [
        {
            title: "Total Cases",
            value: totalReports,
            icon: TrendingUp,
            color: "text-blue-600",
            bgColor: "bg-blue-50",
            details: [
                { label: "New", value: newReports, color: "bg-green-100 text-green-800" },
                { label: "Under Review", value: underReviewReports, color: "bg-yellow-100 text-yellow-800" },
                { label: "Resolved", value: resolvedReports, color: "bg-gray-100 text-gray-800" }
            ]
        },
        {
            title: "High Priority",
            value: highPriorityReports,
            icon: AlertTriangle,
            color: "text-red-600",
            bgColor: "bg-red-50",
            subtitle: `${totalReports > 0 ? Math.round((highPriorityReports / totalReports) * 100) : 0}% of cases`
        },
        {
            title: "Awaiting Response",
            value: awaitingResponseReports,
            icon: Users,
            color: "text-purple-600",
            bgColor: "bg-purple-50",
            subtitle: `Pending user response`
        },
        {
            title: "Monthly Growth",
            value: `${totalReportsChange >= 0 ? '+' : ''}${totalReportsChange}%`,
            icon: Shield,
            color: totalReportsChange >= 0 ? "text-green-600" : "text-red-600",
            bgColor: totalReportsChange >= 0 ? "bg-green-50" : "bg-red-50",
            subtitle: `Compared to last month`
        }
    ];

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {adminStats.map((stat, index) => (
                <Card key={index} className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">
                            {stat.title}
                        </CardTitle>
                        <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                            <stat.icon className={`h-4 w-4 ${stat.color}`} />
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-gray-900 mb-1">
                            {stat.value}
                        </div>
                        {stat.subtitle && (
                            <p className="text-xs text-gray-500">
                                {stat.subtitle}
                            </p>
                        )}
                        {stat.details && (
                            <div className="flex flex-wrap gap-1 mt-2">
                                {stat.details.map((detail, idx) => (
                                    <Badge 
                                        key={idx} 
                                        variant="secondary" 
                                        className={`text-xs ${detail.color}`}
                                    >
                                        {detail.label}: {detail.value}
                                    </Badge>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}