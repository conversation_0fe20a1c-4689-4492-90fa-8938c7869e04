"use client";

import React, { useState, Suspense } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { CreditCard, Building2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

// Form validation schema
const formSchema = z.object({
  cardNumber: z.string().min(15, 'Invalid card number').max(19, 'Invalid card number'),
  expiryDate: z.string().regex(/^(0[1-9]|1[0-2])\/([0-9]{2})$/, 'Invalid expiry date (MM/YY)'),
  cvv: z.string().regex(/^[0-9]{3,4}$/, 'Invalid CVV'),
  nameOnCard: z.string().min(2, 'Name is required'),
  paymentMethod: z.enum(['card', 'upi', 'netbanking']),
  billingAddress: z.object({
    fullName: z.string().min(2, 'Full name is required'),
    email: z.string().email('Invalid email address'),
    companyName: z.string().optional(),
    address: z.string().min(5, 'Address is required'),
    city: z.string().min(2, 'City is required'),
    state: z.string().min(2, 'State is required'),
    country: z.string().min(2, 'Country is required'),
    zipCode: z.string().min(5, 'ZIP code is required'),
  }),
});

function CompletionPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  // Get plan details from URL params
  const plan = searchParams.get('plan') || 'professional';
  const billing = searchParams.get('billing') || 'monthly';
  
  const basePrice = plan === 'starter' ? 99 : 169;
  const price = billing === 'yearly' ? basePrice * 12 * 0.8 : basePrice;
  const gst = price * 0.18; // 18% GST
  const total = price + gst;

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      nameOnCard: '',
      paymentMethod: 'card',
      billingAddress: {
        fullName: '',
        email: '',
        companyName: '',
        address: '',
        city: '',
        state: '',
        country: '',
        zipCode: '',
      },
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      // TODO: Implement payment processing logic
      console.log({ values, plan, billing });
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      router.push('/dashboard');
    } catch (error) {
      console.error('Payment error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCardNumber = (value: string) => {
    return value
      .replace(/\s/g, '')
      .replace(/(\d{4})/g, '$1 ')
      .trim();
  };

  const formatExpiryDate = (value: string) => {
    return value
      .replace(/\D/g, '')
      .replace(/(\d{2})(\d)/, '$1/$2');
  };

  return (<div className="min-h-screen flex">
      {/* Left side form */}
      <div className="w-1/2 p-8 flex flex-col justify-center items-center bg-white">
        <div className="w-full max-w-md">
          <div className="mb-8">
            <Link href="/">
              <Image src="/logo.svg" alt="Logo" width={120} height={40} />
            </Link>
          </div>

          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Finish & Activate Your Subscription</h1>
            <p className="text-gray-500 mt-2">
              You&apos;re just one step away from unlocking access to compliance tools.
              Please review your order and complete the payment securely.
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-medium mb-2">Order Summary</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Startup Plan ({billing})</span>
                <span>${price.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">GST (18%)</span>
                <span>${gst.toFixed(2)}</span>
              </div>
              <div className="flex justify-between pt-2 border-t border-gray-200 font-medium">
                <span>Total</span>
                <span>${total.toFixed(2)}</span>
              </div>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-4">
                <h3 className="font-medium">Payment Method</h3>
                
                <FormField
                  control={form.control}
                  name="paymentMethod"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="grid grid-cols-3 gap-4"
                        >
                          <div>
                            <RadioGroupItem
                              value="card"
                              id="card"
                              className="peer sr-only"
                            />
                            <Label
                              htmlFor="card"
                              className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 peer-data-[state=checked]:border-[#1E4841] [&:has([data-state=checked])]:border-[#1E4841]"
                            >
                              <CreditCard className="mb-3 h-6 w-6" />
                              <span className="text-sm font-medium">Card</span>
                            </Label>
                          </div>
                          
                          <div>
                            <RadioGroupItem
                              value="upi"
                              id="upi"
                              className="peer sr-only"
                            />
                            <Label
                              htmlFor="upi"
                              className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 peer-data-[state=checked]:border-[#1E4841] [&:has([data-state=checked])]:border-[#1E4841]"
                            >
                              <Image src="/upi-icon.svg" alt="UPI" width={24} height={24} className="mb-3" />
                              <span className="text-sm font-medium">UPI</span>
                            </Label>
                          </div>

                          <div>
                            <RadioGroupItem
                              value="netbanking"
                              id="netbanking"
                              className="peer sr-only"
                            />
                            <Label
                              htmlFor="netbanking"
                              className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white p-4 hover:bg-gray-50 peer-data-[state=checked]:border-[#1E4841] [&:has([data-state=checked])]:border-[#1E4841]"
                            >
                              <Building2 className="mb-3 h-6 w-6" />
                              <span className="text-sm font-medium">Net Banking</span>
                            </Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {form.watch('paymentMethod') === 'card' && (
                <div className="space-y-4">
                  <h3 className="font-medium">Card Details</h3>

                  <FormField
                    control={form.control}
                    name="cardNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Card Number</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="1234 5678 9012 3456"
                            maxLength={19}
                            onChange={e => {
                              field.onChange(formatCardNumber(e.target.value));
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="expiryDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Expiry Date</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="MM/YY"
                              maxLength={5}
                              onChange={e => {
                                field.onChange(formatExpiryDate(e.target.value));
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="cvv"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>CVV</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="password"
                              placeholder="123"
                              maxLength={4}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="nameOnCard"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name on Card</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="JOHN SMITH"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              <div className="space-y-4">
                <h3 className="font-medium">Billing Address</h3>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="billingAddress.fullName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="billingAddress.email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input {...field} type="email" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="billingAddress.companyName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name (Optional)</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="billingAddress.address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="billingAddress.city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="billingAddress.zipCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>ZIP Code</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="billingAddress.state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select state" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="CA">California</SelectItem>
                              <SelectItem value="NY">New York</SelectItem>
                              {/* Add more states */}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="billingAddress.country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select country" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="US">United States</SelectItem>
                              <SelectItem value="CA">Canada</SelectItem>
                              {/* Add more countries */}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90 text-white"
                disabled={isLoading}
              >
                {isLoading ? "Processing payment..." : "Complete Payment"}
              </Button>
            </form>
          </Form>
        </div>
      </div>

      {/* Right side content */}
      <div className="w-1/2 bg-[#F9FAFB] p-8 flex items-center justify-center">
        <div className="max-w-lg space-y-6">
          <div className="space-y-2">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 rounded-full bg-[#1E4841] text-white flex items-center justify-center font-semibold">
                1
              </div>
              <div className="h-1 flex-1 bg-[#1E4841]" />
              <div className="w-8 h-8 rounded-full bg-[#1E4841] text-white flex items-center justify-center font-semibold">
                2
              </div>
              <div className="h-1 flex-1 bg-[#1E4841]" />
              <div className="w-8 h-8 rounded-full bg-[#1E4841] text-white flex items-center justify-center font-semibold">
                3
              </div>
            </div>
            <div className="flex justify-between text-sm text-gray-600">
              <span>Admin Signup</span>
              <span>Subscription Plan</span>
              <span>Subscription Completion</span>
            </div>
          </div>

          <div className="space-y-4">
            <h2 className="text-2xl font-bold text-[#1E4841]">Complete Your Subscription</h2>
            <p className="text-gray-600">
              You&apos;re just one step away from unlocking access to compliance tools.
              Please review your order and complete the payment securely.
            </p>
          </div>

          <div className="relative aspect-video rounded-lg overflow-hidden">
            <Image
              src="/secure-payment.jpg"
              alt="Secure payment"
              fill
              className="object-cover"
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-[#ECF4E9] flex items-center justify-center mt-1">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M10 3L4.5 8.5L2 6" stroke="#1E4841" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-[#1E4841]">Secure Payments</h3>
                <p className="text-sm text-gray-600">Your payment information is encrypted and processed securely.</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-[#ECF4E9] flex items-center justify-center mt-1">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M10 3L4.5 8.5L2 6" stroke="#1E4841" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-[#1E4841]">Instant Access</h3>
                <p className="text-sm text-gray-600">Get immediate access to all features after payment confirmation.</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 rounded-full bg-[#ECF4E9] flex items-center justify-center mt-1">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M10 3L4.5 8.5L2 6" stroke="#1E4841" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-[#1E4841]">24/7 Support</h3>
                <p className="text-sm text-gray-600">Our support team is available to help you get started.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CompletionPage() {
  return (
    <Suspense fallback={<div className="animate-pulse p-4">Loading...</div>}>
      <CompletionPageContent />
    </Suspense>
  );
}