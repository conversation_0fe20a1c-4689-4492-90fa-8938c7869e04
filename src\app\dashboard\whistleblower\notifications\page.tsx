"use client";

import Header from "@/components/dashboard-components/Header";
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import { Bell, Home, CheckCheck } from "lucide-react";
import { useNotificationContext } from "@/contexts/NotificationContext";
import NotificationItem from "@/components/dashboard-components/shared/notifications/NotificationItem";

export default function NotificationsPage() {
    const { notifications, unreadCount, markAsRead, markAllAsRead } = useNotificationContext();

    const handleMarkAsRead = (notificationId: string) => {
        markAsRead(notificationId);
    };

    const handleMarkAllAsRead = () => {
        markAllAsRead();
    };

    return (
        <div className="w-full h-screen flex flex-col">
            <Header />
            <main className="flex-1 bg-gray-50 flex flex-col min-h-0">
                <header className="px-6 py-4 bg-white border-b flex-shrink-0">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard" className="flex items-center gap-1">
                                    <Home className="w-4 h-4" />
                                    Dashboard
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Notifications</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                    <div className="flex justify-between items-center mt-4">
                        <div>
                            <h1 className="text-2xl font-semibold text-[#242E2C]">Notifications</h1>
                            <p className="text-sm text-[#6B7280] mt-1">Stay updated with your case progress and system alerts</p>
                        </div>
                        {unreadCount > 0 && (
                            <Button onClick={handleMarkAllAsRead} className="bg-[#1E4841] text-white hover:bg-[#1E4841]/90">
                                <CheckCheck className="w-4 h-4 mr-2" />
                                Mark All Read
                            </Button>
                        )}
                    </div>
                </header>

                <div className="flex-1 p-6 overflow-auto min-h-0">
                    {notifications.length > 0 ? (
                        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                            {notifications.map((notification) => (
                                <NotificationItem
                                    key={notification._id}
                                    notification={notification}
                                    onMarkAsRead={handleMarkAsRead}
                                    variant="page"
                                />
                            ))}
                        </div>
                    ) : (
                        <div className="bg-white rounded-lg shadow-sm p-12 text-center">
                            <Bell className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications yet</h3>
                            <p className="text-gray-500">You&apos;ll see notifications about your reports and system updates here.</p>
                        </div>
                    )}
                </div>
            </main>
        </div>
    );
}