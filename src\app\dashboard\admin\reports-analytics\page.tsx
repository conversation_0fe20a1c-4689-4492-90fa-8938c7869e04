"use client";

import { useEffect } from "react";
import Header from "@/components/dashboard-components/Header";
import { notificationSystem } from "@/lib/utils/notificationSystem";
import { notificationsData } from "@/lib/mockData/notificationData";
import AdminCaseTrends from "@/components/dashboard-components/admin/dashboard/AdminCaseTrends";
import AdminStatisticsCards from "@/components/dashboard-components/admin/dashboard/AdminStatisticsCards";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Download, FileText, BarChart3, TrendingUp } from "lucide-react";

export default function ReportsAnalyticsPage() {
    // Initialize notification system
    useEffect(() => {
        notificationSystem.updateNotifications(notificationsData);
    }, []);

    const reportTypes = [
        {
            title: "Case Summary Report",
            description: "Comprehensive overview of all cases",
            icon: FileText,
            color: "text-blue-600 bg-blue-50"
        },
        {
            title: "SLA Compliance Report",
            description: "Service level agreement performance metrics",
            icon: BarChart3,
            color: "text-green-600 bg-green-50"
        },
        {
            title: "Trend Analysis Report",
            description: "Historical trends and patterns",
            icon: TrendingUp,
            color: "text-purple-600 bg-purple-50"
        },
        {
            title: "Department Performance",
            description: "Performance metrics by department",
            icon: BarChart3,
            color: "text-orange-600 bg-orange-50"
        }
    ];

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Reports & Analytics"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900 mb-2">Reports & Analytics</h1>
                                <p className="text-gray-600">Generate reports and analyze case data</p>
                            </div>
                            <Button className="bg-[#BBF49C] text-[#1E4841] hover:bg-[#BBF49C]/90 flex items-center gap-2">
                                <Download className="h-4 w-4" />
                                Export All Data
                            </Button>
                        </div>
                    </div>

                    {/* Statistics Overview */}
                    <section aria-labelledby="statistics-section">
                        <h2 id="statistics-section" className="sr-only">Statistics Overview</h2>
                        <AdminStatisticsCards />
                    </section>

                    {/* Charts and Analytics */}
                    <section aria-labelledby="analytics-section">
                        <h2 id="analytics-section" className="sr-only">Analytics Charts</h2>
                        <AdminCaseTrends />
                    </section>

                    {/* Report Generation */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                Generate Reports
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {reportTypes.map((report, index) => (
                                    <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div className="flex items-start gap-3">
                                            <div className={`p-2 rounded-lg ${report.color}`}>
                                                <report.icon className="h-5 w-5" />
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="font-medium text-gray-900 mb-1">{report.title}</h3>
                                                <p className="text-sm text-gray-600 mb-3">{report.description}</p>
                                                <Button 
                                                    variant="outline" 
                                                    size="sm" 
                                                    className="hover:bg-[#BBF49C] hover:border-[#BBF49C] flex items-center gap-2"
                                                >
                                                    <Download className="h-4 w-4" />
                                                    Generate
                                                </Button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Reports */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                Recent Reports
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {[
                                    { name: "Monthly Case Summary - May 2025", date: "Jun 1, 2025", size: "2.4 MB" },
                                    { name: "SLA Compliance Report - Q2 2025", date: "May 30, 2025", size: "1.8 MB" },
                                    { name: "Department Performance - May 2025", date: "May 28, 2025", size: "3.1 MB" }
                                ].map((report, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <FileText className="h-5 w-5 text-gray-400" />
                                            <div>
                                                <h4 className="font-medium text-gray-900">{report.name}</h4>
                                                <p className="text-sm text-gray-600">{report.date} • {report.size}</p>
                                            </div>
                                        </div>
                                        <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                                            <Download className="h-4 w-4" />
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </main>
            </div>
        </>
    );
}