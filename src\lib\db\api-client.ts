/**
 * API client for interacting with the backend API
 */
export class ApiClient {
  /**
   * Base URL for API requests
   */
  private static baseUrl = '/api';

  /**
   * Make a GET request to the API
   * @param endpoint - The API endpoint
   * @param params - Query parameters
   * @returns The response data
   */
  static async get<T>(endpoint: string, params?: Record<string, string | number | boolean | undefined>): Promise<T> {
    const url = new URL(`${this.baseUrl}${endpoint}`, window.location.origin);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          url.searchParams.append(key, String(value));
        }
      });
    }
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'An error occurred');
    }
    
    return response.json();
  }

  /**
   * Make a POST request to the API
   * @param endpoint - The API endpoint
   * @param data - The request body
   * @returns The response data
   */
  static async post<T>(endpoint: string, data: Record<string, unknown>): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'An error occurred');
    }
    
    return response.json();
  }

  /**
   * Make a PUT request to the API
   * @param endpoint - The API endpoint
   * @param data - The request body
   * @returns The response data
   */
  static async put<T>(endpoint: string, data: Record<string, unknown>): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'An error occurred');
    }
    
    return response.json();
  }

  /**
   * Make a DELETE request to the API
   * @param endpoint - The API endpoint
   * @returns The response data
   */
  static async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'An error occurred');
    }
    
    return response.json();
  }

  /**
   * Make a PATCH request to the API
   * @param endpoint - The API endpoint
   * @param data - The request body
   * @returns The response data
   */
  static async patch<T>(endpoint: string, data: Record<string, unknown>): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'An error occurred');
    }
    
    return response.json();
  }

  // Authentication endpoints
  static async login(email: string, password: string) {
    return this.post<{
      success: boolean;
      requireTwoFactor?: boolean;
      userId?: string;
      expiresAt?: string;
      code?: string;
      error?: string;
    }>('/auth', { email, password });
  }

  static async verifyTwoFactor(userId: string, code: string) {
    return this.put<{
      success: boolean;
      error?: string;
    }>('/auth/two-factor', { userId, code });
  }

  static async logout() {
    return this.post<Record<string, unknown>>('/auth/logout', {});
  }

  static async getSession() {
    return this.get<Record<string, unknown>>('/auth/session');
  }

  // User endpoints
  static async getUsers() {
    return this.get<Record<string, unknown>>('/users');
  }

  // Report endpoints
  static async getReports(params?: Record<string, string | number | boolean | undefined>) {
    return this.get<Record<string, unknown>>('/reports', params);
  }

  static async getReportById(id: string) {
    return this.get<Record<string, unknown>>(`/reports/${id}`);
  }

  static async createReport(data: Record<string, unknown>) {
    return this.post<Record<string, unknown>>('/reports', data);
  }

  static async updateReport(id: string, data: Record<string, unknown>) {
    return this.put<Record<string, unknown>>(`/reports/${id}`, data);
  }

  // Notification endpoints
  static async getNotifications(params?: Record<string, string | number | boolean | undefined>) {
    return this.get<Record<string, unknown>>('/notifications', params);
  }

  static async markNotificationAsRead(id: string) {
    return this.patch<Record<string, unknown>>(`/notifications/${id}`, { status: 'read' });
  }

  // Company endpoints
  static async getCompanies(params?: Record<string, string | number | boolean | undefined>) {
    return this.get<Record<string, unknown>>('/companies', params);
  }

  static async getCompanyById(id: string) {
    return this.get<Record<string, unknown>>(`/companies/${id}`);
  }

  static async createCompany(data: Record<string, unknown>) {
    return this.post<Record<string, unknown>>('/companies', data);
  }

  static async updateCompany(id: string, data: Record<string, unknown>) {
    return this.put<Record<string, unknown>>(`/companies/${id}`, data);
  }

  static async getCompanyUsers(companyId: string) {
    return this.get<Record<string, unknown>>(`/companies/${companyId}/users`);
  }

  static async addCompanyUser(companyId: string, userData: Record<string, unknown>) {
    return this.post<Record<string, unknown>>(`/companies/${companyId}/users`, userData);
  }

  // Dashboard endpoints
  static async getDashboardStats() {
    return this.get<Record<string, unknown>>('/dashboard/stats');
  }

  static async getRecentActivity(limit?: number) {
    return this.get<Record<string, unknown>>('/activity', { limit });
  }
}