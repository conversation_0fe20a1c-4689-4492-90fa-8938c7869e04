import Link from 'next/link';
import { Sen } from "next/font/google";

const sen = Sen({
    weight: ["400", "600"],
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-sen'
});

export default function NotFound() {
    return (
        <div className={`bg-[#ECF4E9] flex flex-col items-center justify-center min-h-screen p-4 text-center ${sen.variable}`}>
            <main id="main-content" role="main" aria-labelledby="not-found-title">
                <h1 id="not-found-title" className="text-4xl font-bold mb-4">404 - Page Not Found</h1>
                <p className="mb-8" aria-describedby="not-found-description">
                    The page you are looking for doesn&apos;t exist or has been moved.
                </p>
                <div id="not-found-description" className="sr-only">
                    The requested page could not be found. You can return to the homepage or explore our products and services.
                </div>
                <Link
                    href="/"
                    className="px-6 py-3 bg-[#1E4841] text-white rounded-md hover:bg-[#163530] transition-colors"
                    aria-label="Return to 7IRIS homepage"
                >
                    Return Home
                </Link>
                <nav className="mt-8 text-sm text-gray-600" aria-label="Alternative navigation options">
                    <p>
                        Looking for whistleblowing solutions? Check out our{' '}
                        <Link
                            href="/products"
                            className="text-[#1E4841] underline"
                            aria-label="View our whistleblowing products and solutions"
                        >
                            products
                        </Link>{' '}
                        or{' '}
                        <Link
                            href="/contact"
                            className="text-[#1E4841] underline"
                            aria-label="Contact our support team for assistance"
                        >
                            contact us
                        </Link>{' '}
                        for assistance.
                    </p>
                </nav>
            </main>
        </div>
    );
}
