"use client";
import Image from "next/image";
import { WORKFLOW_STEPS } from "@/lib/mockData";


export default function HowWeWork() {
  const steps = [...WORKFLOW_STEPS];

  return (
    <section className="w-full flex flex-col md:flex-row items-center lg:items-start justify-between bg-[#ECF4E9] text-[#1E4841] px-8 md:px-16 lg:px-24 xl:px-44 py-8 xl:py-16 gap-0 lg:gap-12 xl:gap-24 mt-10 xl:mt-20">
      <div className="flex items-start justify-start rounded-sm">
        <Image
          src="/desktop/pricing/how-we-work/hero.svg"
          alt="How We Work Process Illustration"
          width={435}
          height={534}
          className="hidden lg:block w-full lg:w-96 h-auto"
          priority
        />
      </div>
      <div className="flex flex-col items-start justify-between">
        <div className="flex flex-col items-start justify-between mb-4 xl:mb-8">
          <p className="text-base xl:text-lg font-semibold">How We Work!</p>
          <p className="text-xl md:text-2xl xl:text-[28px] font-bold">
            Empowering Transparency,
            <br />
            Protecting Identities
          </p>
        </div>
        <div>
          {steps.map((step, index) => {
            const isLast = index === steps.length - 1;
            const marginTopClasses = {
              0: '-mt-24 md:-mt-16 lg:-mt-20 xl:-mt-20',
              1: '-mt-16 md:-mt-12 lg:-mt-16 xl:-mt-16',
              2: '-mt-20 md:-mt-12 lg:-mt-20 xl:-mt-20',
              3: '-mt-20 md:-mt-12 lg:-mt-20 xl:-mt-20'
            }[index];

            return (
              <div key={`step-${index}`} className="ml-0 lg:ml-8 xl:ml-24 group">
                <div className="flex gap-5 xl:gap-6">
                  <div className="relative left-1.5 top-1.5">
                    <div className="w-3 xl:w-3.5 h-3 xl:h-3.5 rounded-full bg-[#1E4841] transition-all duration-300 group-hover:scale-110" />
                    <div className="absolute -top-1.5 -left-1.5 md:-top-1.5 md:-left-1.5 xl:-top-2 xl:-left-2 w-6 xl:w-7 h-6 xl:h-7 rounded-full ring-1 ring-[#1E4841]/24 transition-all duration-300 group-hover:ring-2 group-hover:ring-[#1E4841]" />
                  </div>
                  <div>
                    <p className="font-bold text-lg xl:text-xl">{step.title}</p>
                    <p className="font-normal text-sm xl:text-base xl:leading-relaxed pr-4 xl:pr-32">{step.description}</p>
                  </div>
                </div>
                {!isLast && (
                  <div className={`w-0.5 h-40 md:h-28 lg:h-32 xl:h-32 ml-3 lg:ml-3 xl:ml-3.5 relative ${marginTopClasses} bg-gradient-to-b from-[#1E4841] to-[#1E484157] transition-all duration-300`} />
                )}
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}