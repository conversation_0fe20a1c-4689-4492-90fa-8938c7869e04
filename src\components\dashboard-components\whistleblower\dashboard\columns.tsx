"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";
import { ReportData } from "@/lib/types";

export const createColumns = (onReportClick?: (reportId: string) => void): ColumnDef<ReportData>[] => [
  {
    accessorKey: "id",
    header: "Report ID",
    cell: ({ row }) => (
      <div className="font-mono font-medium text-xs sm:text-sm text-[#242E2C]">
        {row.getValue("id")}
      </div>
    ),
  },
  {
    accessorKey: "title",
    header: "Title",
    cell: ({ row }) => (
      <div className="font-normal text-xs sm:text-sm text-[#1E4841] max-w-[120px] sm:max-w-xs truncate">
        {row.getValue("title")}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const report = row.original;
      return (
        <Badge className={`${report.statusColor} text-xs`}>
          {row.getValue("status")}
        </Badge>
      );
    },
  },
  {
    accessorKey: "dateSubmitted",
    header: "Date Submitted",
    cell: ({ row }) => (
      <div className="text-xs sm:text-sm font-normal text-[#242E2C]">
        {row.getValue("dateSubmitted")}
      </div>
    ),
  },
  {
    accessorKey: "lastUpdated",
    header: "Last Updated",
    cell: ({ row }) => (
      <div className="text-xs sm:text-sm font-normal text-[#242E2C]">
        {row.getValue("lastUpdated")}
      </div>
    ),
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <Button
        aria-label="View Report"
        variant="outline"
        size="sm"
        className="hover:bg-[#ECF4E9] text-xs sm:text-sm px-2 sm:px-3"
        onClick={() => onReportClick ? onReportClick(row.original.id) : window.location.href = `/dashboard/whistleblower/my-reports?reportId=${row.original.id}`}
      >
        <Eye className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-1" />
        <span className="hidden sm:inline">View Report</span>
      </Button>
    ),
  },
];

// Export default columns for backward compatibility
export const columns = createColumns();