"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Header from "@/components/dashboard-components/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "sonner";
import { Upload, Home, Calendar, Users, FileText, AlertTriangle } from "lucide-react";
import { apiClient } from "@/lib/utils/apiClient";

interface DetailedReportData {
  // Basic info from step 1
  title: string;
  category: string;
  dateOfOccurrence: string;
  location: string;
  description: string;
  isAnonymous: boolean;
  reportingPreferences: {
    emailUpdates: boolean;
    smsUpdates: boolean;
  };
  
  // Step 2 detailed information
  incidentDate: string;
  incidentTime: string;
  specificLocation: string;
  departmentInvolved: string;
  peopleInvolved: string;
  witnessInfo: {
    hasWitnesses: boolean;
    witnessDetails: string;
  };
  evidenceInfo: {
    hasEvidence: boolean;
    evidenceDescription: string;
    evidenceFiles: File[];
  };
  impactAssessment: {
    financialImpact: string;
    operationalImpact: string;
    reputationalImpact: string;
  };
  previousReports: {
    hasPreviousReports: boolean;
    previousReportDetails: string;
  };
  urgencyLevel: string;
  additionalComments: string;
}

export default function Step2Page() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [step1Data, setStep1Data] = useState<Record<string, unknown> | null>(null);
  const [formData, setFormData] = useState<DetailedReportData>({
    // Step 1 data (will be loaded from session storage)
    title: "",
    category: "",
    dateOfOccurrence: "",
    location: "",
    description: "",
    isAnonymous: false,
    reportingPreferences: {
      emailUpdates: true,
      smsUpdates: false,
    },
    
    // Step 2 data
    incidentDate: "",
    incidentTime: "",
    specificLocation: "",
    departmentInvolved: "",
    peopleInvolved: "",
    witnessInfo: {
      hasWitnesses: false,
      witnessDetails: "",
    },
    evidenceInfo: {
      hasEvidence: false,
      evidenceDescription: "",
      evidenceFiles: [],
    },
    impactAssessment: {
      financialImpact: "",
      operationalImpact: "",
      reputationalImpact: "",
    },
    previousReports: {
      hasPreviousReports: false,
      previousReportDetails: "",
    },
    urgencyLevel: "Medium",
    additionalComments: "",
  });

  useEffect(() => {
    // Load step 1 data from session storage
    const savedData = sessionStorage.getItem('reportStep1Data');
    if (savedData) {
      const step1Info = JSON.parse(savedData);
      setStep1Data(step1Info);
      setFormData(prev => ({
        ...prev,
        ...step1Info
      }));
    } else {
      // Redirect back to step 1 if no data found
      router.push('/dashboard/whistleblower/new-report');
    }
  }, [router]);

  const departments = [
    "Human Resources",
    "Finance",
    "Operations",
    "Sales",
    "Marketing",
    "IT",
    "Legal",
    "Procurement",
    "Customer Service",
    "Management",
    "Other"
  ];

  const urgencyLevels = [
    { value: "Low", label: "Low - No immediate action required" },
    { value: "Medium", label: "Medium - Should be addressed within reasonable timeframe" },
    { value: "High", label: "High - Requires prompt attention" },
    { value: "Critical", label: "Critical - Immediate action required" }
  ];

  const handleInputChange = (field: string, value: string | boolean | File[]) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof DetailedReportData] as Record<string, unknown>),
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        evidenceInfo: {
          ...prev.evidenceInfo,
          evidenceFiles: [...prev.evidenceInfo.evidenceFiles, ...fileArray]
        }
      }));
    }
  };

  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      evidenceInfo: {
        ...prev.evidenceInfo,
        evidenceFiles: prev.evidenceInfo.evidenceFiles.filter((_, i) => i !== index)
      }
    }));
  };

  const handleBackToStep1 = () => {
    // Save current step 2 data
    sessionStorage.setItem('reportStep2Data', JSON.stringify(formData));
    router.push('/dashboard/whistleblower/new-report');
  };

  const handleContinueToStep3 = () => {
    // Validate required fields
    if (!formData.incidentDate || !formData.specificLocation) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Save step 2 data to session storage
    sessionStorage.setItem('reportStep2Data', JSON.stringify(formData));
    router.push('/dashboard/whistleblower/new-report/step-3');
  };

  const handleSaveDraft = async () => {
    setLoading(true);
    try {
      // Combine step 1 and step 2 data for draft
      const draftData = {
        ...formData,
        status: 'Draft',
        step: 2
      };

      const result = await apiClient.post('/api/reports/draft', draftData);

      if (result.success) {
        toast.success('Draft saved successfully');
      } else {
        toast.error(result.error || 'Failed to save draft');
      }
    } catch (error) {
      console.error('Save draft error:', error);
      toast.error('Failed to save draft');
    } finally {
      setLoading(false);
    }
  };

  if (!step1Data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#1E4841] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower">
                  <Home className="w-4 h-4" />
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower/my-reports">
                  My Reports
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/dashboard/whistleblower/new-report">
                  New Report
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Additional Details</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Additional Details
            </h1>
            <p className="text-gray-600">
              Provide additional information to help us better understand and investigate your report
            </p>
          </div>

          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full">
                  ✓
                </div>
                <span className="ml-2 text-sm font-medium text-green-600">Report Details</span>
              </div>
              <div className="flex-1 h-1 bg-green-600 mx-4"></div>
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-[#1E4841] text-white rounded-full">
                  2
                </div>
                <span className="ml-2 text-sm font-medium text-[#1E4841]">Additional Details</span>
              </div>
              <div className="flex-1 h-1 bg-gray-300 mx-4"></div>
              <div className="flex items-center">
                <div className="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-500 rounded-full">
                  3
                </div>
                <span className="ml-2 text-sm font-medium text-gray-500">Review & Submit</span>
              </div>
            </div>
          </div>

          {/* Incident Details Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Incident Details
              </CardTitle>
              <p className="text-sm text-gray-600">
                Provide specific details about when and where the incident occurred
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="incidentDate" className="text-sm font-medium">
                    Specific Date of Incident *
                  </Label>
                  <Input
                    id="incidentDate"
                    type="date"
                    value={formData.incidentDate}
                    onChange={(e) => handleInputChange('incidentDate', e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="incidentTime" className="text-sm font-medium">
                    Approximate Time
                  </Label>
                  <Input
                    id="incidentTime"
                    type="time"
                    value={formData.incidentTime}
                    onChange={(e) => handleInputChange('incidentTime', e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="specificLocation" className="text-sm font-medium">
                  Specific Location *
                </Label>
                <Input
                  id="specificLocation"
                  placeholder="e.g., Conference Room B, 3rd Floor, Building A"
                  value={formData.specificLocation}
                  onChange={(e) => handleInputChange('specificLocation', e.target.value)}
                  className="mt-1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Be as specific as possible about the location
                </p>
              </div>

              <div>
                <Label htmlFor="departmentInvolved" className="text-sm font-medium">
                  Department(s) Involved
                </Label>
                <Select
                  value={formData.departmentInvolved}
                  onValueChange={(value) => handleInputChange('departmentInvolved', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select the primary department involved" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept} value={dept}>
                        {dept}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* People Involved Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                People Involved
              </CardTitle>
              <p className="text-sm text-gray-600">
                Information about individuals involved in the incident
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="peopleInvolved" className="text-sm font-medium">
                  People Involved (Optional)
                </Label>
                <Textarea
                  id="peopleInvolved"
                  placeholder="Describe the roles or positions of people involved without using names (e.g., 'Senior Manager from Finance Department', 'Colleague from my team')"
                  value={formData.peopleInvolved}
                  onChange={(e) => handleInputChange('peopleInvolved', e.target.value)}
                  className="mt-1 min-h-[80px]"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Avoid using actual names. Use job titles or descriptions instead.
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium">Were there any witnesses?</Label>
                <RadioGroup
                  value={formData.witnessInfo.hasWitnesses.toString()}
                  onValueChange={(value) => handleInputChange('witnessInfo.hasWitnesses', value === 'true')}
                  className="mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="true" id="witnesses-yes" />
                    <Label htmlFor="witnesses-yes">Yes</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="false" id="witnesses-no" />
                    <Label htmlFor="witnesses-no">No</Label>
                  </div>
                </RadioGroup>
              </div>

              {formData.witnessInfo.hasWitnesses && (
                <div>
                  <Label htmlFor="witnessDetails" className="text-sm font-medium">
                    Witness Information
                  </Label>
                  <Textarea
                    id="witnessDetails"
                    placeholder="Describe the witnesses without using names (e.g., 'Two colleagues from the same department', 'A manager who was present during the meeting')"
                    value={formData.witnessInfo.witnessDetails}
                    onChange={(e) => handleInputChange('witnessInfo.witnessDetails', e.target.value)}
                    className="mt-1 min-h-[80px]"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Evidence Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Evidence & Documentation
              </CardTitle>
              <p className="text-sm text-gray-600">
                Upload any supporting documents or evidence
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label className="text-sm font-medium">Do you have any evidence or documentation?</Label>
                <RadioGroup
                  value={formData.evidenceInfo.hasEvidence.toString()}
                  onValueChange={(value) => handleInputChange('evidenceInfo.hasEvidence', value === 'true')}
                  className="mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="true" id="evidence-yes" />
                    <Label htmlFor="evidence-yes">Yes</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="false" id="evidence-no" />
                    <Label htmlFor="evidence-no">No</Label>
                  </div>
                </RadioGroup>
              </div>

              {formData.evidenceInfo.hasEvidence && (
                <>
                  <div>
                    <Label htmlFor="evidenceDescription" className="text-sm font-medium">
                      Evidence Description
                    </Label>
                    <Textarea
                      id="evidenceDescription"
                      placeholder="Describe the evidence you have (e.g., emails, documents, photos, recordings)"
                      value={formData.evidenceInfo.evidenceDescription}
                      onChange={(e) => handleInputChange('evidenceInfo.evidenceDescription', e.target.value)}
                      className="mt-1 min-h-[80px]"
                    />
                  </div>

                  <div>
                    <Label htmlFor="evidenceFiles" className="text-sm font-medium">
                      Upload Files (Optional)
                    </Label>
                    <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600 mb-2">
                        Drag and drop files here, or click to select
                      </p>
                      <Input
                        id="evidenceFiles"
                        type="file"
                        multiple
                        onChange={(e) => handleFileUpload(e.target.files)}
                        className="hidden"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('evidenceFiles')?.click()}
                      >
                        Select Files
                      </Button>
                      <p className="text-xs text-gray-500 mt-2">
                        Supported formats: PDF, DOC, DOCX, JPG, PNG, MP3, MP4 (Max 10MB per file)
                      </p>
                    </div>

                    {formData.evidenceInfo.evidenceFiles.length > 0 && (
                      <div className="mt-4">
                        <p className="text-sm font-medium mb-2">Uploaded Files:</p>
                        <div className="space-y-2">
                          {formData.evidenceInfo.evidenceFiles.map((file, index) => (
                            <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                              <span className="text-sm">{file.name}</span>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(index)}
                              >
                                Remove
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Impact Assessment Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Impact Assessment
              </CardTitle>
              <p className="text-sm text-gray-600">
                Help us understand the potential impact of this incident
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="urgencyLevel" className="text-sm font-medium">
                  Urgency Level
                </Label>
                <Select
                  value={formData.urgencyLevel}
                  onValueChange={(value) => handleInputChange('urgencyLevel', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select urgency level" />
                  </SelectTrigger>
                  <SelectContent>
                    {urgencyLevels.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="financialImpact" className="text-sm font-medium">
                  Financial Impact (Optional)
                </Label>
                <Textarea
                  id="financialImpact"
                  placeholder="Describe any financial impact or potential losses"
                  value={formData.impactAssessment.financialImpact}
                  onChange={(e) => handleInputChange('impactAssessment.financialImpact', e.target.value)}
                  className="mt-1 min-h-[60px]"
                />
              </div>

              <div>
                <Label htmlFor="operationalImpact" className="text-sm font-medium">
                  Operational Impact (Optional)
                </Label>
                <Textarea
                  id="operationalImpact"
                  placeholder="Describe any impact on operations, processes, or productivity"
                  value={formData.impactAssessment.operationalImpact}
                  onChange={(e) => handleInputChange('impactAssessment.operationalImpact', e.target.value)}
                  className="mt-1 min-h-[60px]"
                />
              </div>

              <div>
                <Label htmlFor="reputationalImpact" className="text-sm font-medium">
                  Reputational Impact (Optional)
                </Label>
                <Textarea
                  id="reputationalImpact"
                  placeholder="Describe any potential impact on company reputation or relationships"
                  value={formData.impactAssessment.reputationalImpact}
                  onChange={(e) => handleInputChange('impactAssessment.reputationalImpact', e.target.value)}
                  className="mt-1 min-h-[60px]"
                />
              </div>
            </CardContent>
          </Card>

          {/* Previous Reports Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Previous Reports</CardTitle>
              <p className="text-sm text-gray-600">
                Information about any previous reports related to this issue
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label className="text-sm font-medium">
                  Have you or anyone else reported this issue before?
                </Label>
                <RadioGroup
                  value={formData.previousReports.hasPreviousReports.toString()}
                  onValueChange={(value) => handleInputChange('previousReports.hasPreviousReports', value === 'true')}
                  className="mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="true" id="previous-yes" />
                    <Label htmlFor="previous-yes">Yes</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="false" id="previous-no" />
                    <Label htmlFor="previous-no">No</Label>
                  </div>
                </RadioGroup>
              </div>

              {formData.previousReports.hasPreviousReports && (
                <div>
                  <Label htmlFor="previousReportDetails" className="text-sm font-medium">
                    Previous Report Details
                  </Label>
                  <Textarea
                    id="previousReportDetails"
                    placeholder="Describe when and how the issue was previously reported, and what happened as a result"
                    value={formData.previousReports.previousReportDetails}
                    onChange={(e) => handleInputChange('previousReports.previousReportDetails', e.target.value)}
                    className="mt-1 min-h-[80px]"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Additional Comments Card */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Additional Comments</CardTitle>
              <p className="text-sm text-gray-600">
                Any additional information you think would be helpful
              </p>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="additionalComments" className="text-sm font-medium">
                  Additional Information (Optional)
                </Label>
                <Textarea
                  id="additionalComments"
                  placeholder="Provide any additional context, concerns, or information that might be relevant to your report"
                  value={formData.additionalComments}
                  onChange={(e) => handleInputChange('additionalComments', e.target.value)}
                  className="mt-1 min-h-[100px]"
                />
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            <Button
              variant="outline"
              onClick={handleBackToStep1}
              disabled={loading}
            >
              ← Back to Previous Step
            </Button>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                disabled={loading}
              >
                Save as Draft
              </Button>
              <Button
                onClick={handleContinueToStep3}
                disabled={loading}
                className="bg-[#1E4841] hover:bg-[#2A5D54]"
              >
                Continue to Review
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
