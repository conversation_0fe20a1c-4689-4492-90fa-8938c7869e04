import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const PUT = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    // Extract id from URL path
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const id = pathSegments[pathSegments.length - 1]; // Get the id from the path
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Notification ID is required' },
        { status: 400 }
      );
    }
    
    const result = await DataService.markNotificationAsRead(id);
    
    if (!result) {
      return NextResponse.json(
        { success: false, error: 'Notification not found or could not be updated' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: result,
      message: 'Notification marked as read successfully'
    });
  } catch (error) {
    console.error('Notifications PUT API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});