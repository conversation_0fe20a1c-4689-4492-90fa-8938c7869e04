import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { DataService } from '@/lib/db/dataService';
import connectDB from '@/lib/db/mongodb';
import logger from '@/lib/utils/logger';
import bcrypt from 'bcryptjs';

// Validation schema for reset password request
const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Password confirmation is required'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Validate the request body
    const validationResult = resetPasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { token, password } = validationResult.data;

    // Find user by reset token
    const user = await DataService.getUserByResetToken(token);
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid or expired reset token' 
        },
        { status: 400 }
      );
    }

    // Check if token has expired
    if (user.passwordResetExpires && new Date() > user.passwordResetExpires) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Reset token has expired. Please request a new password reset.' 
        },
        { status: 400 }
      );
    }

    try {
      // Hash the new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Update user password and clear reset token
      await DataService.updateUser(user._id.toString(), {
        hashedPassword: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
        securitySettings: {
          lastPasswordChange: new Date()
        }
      });

      logger.info('Password reset successful', { 
        userId: user._id.toString(),
        email: user.email 
      });

      return NextResponse.json({
        success: true,
        message: 'Your password has been reset successfully. You can now log in with your new password.'
      });

    } catch (updateError) {
      logger.error('Error updating password:', updateError);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to update password. Please try again.' 
        },
        { status: 500 }
      );
    }

  } catch (error) {
    logger.error('Reset password API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An error occurred while resetting your password. Please try again.' 
      },
      { status: 500 }
    );
  }
}
