"use client";

import { useState } from "react";
import { LogOut } from "lucide-react"
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarRail,
    SidebarTrigger,
} from "@/components/ui/sidebar"
import Image from "next/image"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { NAVIGATION_ITEMS, ADMIN_NAVIGATION_ITEMS } from "@/lib/mockData"
import { useAuth } from "@/hooks/useAuth"
import { useUnreadCounts } from "@/hooks/useUnreadCounts"

import { toast } from "sonner"

export default function DashboardSidebar() {
    const pathname = usePathname();
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const { logout } = useAuth();
    const { messages: unreadMessages, notifications: unreadNotifications } = useUnreadCounts();
    
    // Determine if we're on admin routes
    const isAdminRoute = pathname.includes('/dashboard/admin');
    const navigationItems = isAdminRoute ? ADMIN_NAVIGATION_ITEMS : NAVIGATION_ITEMS;

    return (
        <Sidebar className="bg-[#ECF4E9] text-[#6B7271]" collapsible="icon">
            <SidebarHeader className="bg-[#ECF4E9] flex flex-row items-center justify-between group-data-[collapsible=icon]:justify-center">
                <Link href="/dashboard" className="p-2 w-fit group-data-[collapsible=icon]:hidden">
                    <Image
                        src={"/logo.svg"}
                        alt="logo"
                        width={83}
                        height={37}
                        priority
                        className="w-24 h-auto"
                        aria-label="7IRIS whistleblowing platform logo"
                    />
                </Link>
                <SidebarTrigger className="hover:bg-[#BBF49C] transition-colors duration-300 ease-in-out" />
            </SidebarHeader>

            <SidebarContent className="bg-[#ECF4E9]">
                {isAdminRoute ? (
                    // Admin hierarchical navigation
                    <>
                        {navigationItems.map((section) => (
                            <SidebarGroup key={section.title}>
                                <SidebarGroupLabel className="text-[#1E4841] font-semibold text-sm px-4 py-2">
                                    {section.title}
                                </SidebarGroupLabel>
                                <SidebarGroupContent>
                                    <SidebarMenu>
                                        {section.items.map((item) => {
                                            const isActive = pathname === item.url;
                                            const isSecureMessage = item.url.includes('/secure-message');
                                            return (
                                                <SidebarMenuItem key={item.title}>
                                                    <SidebarMenuButton
                                                        asChild
                                                        isActive={isActive}
                                                        className="hover:bg-[#BBF49C] hover:text-[#1E4841] data-[active=true]:bg-[#BBF49C] data-[active=true]:text-[#1E4841] transition-colors duration-300 ease-in-out"
                                                    >
                                                        <Link href={item.url} className="gap-4 px-4 py-3 relative">
                                                            <div className="relative">
                                                                <item.icon className="w-4 h-4" />
                                                                {isSecureMessage && unreadMessages > 0 && (
                                                                    <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                                                                        {unreadMessages > 99 ? '99+' : unreadMessages}
                                                                    </div>
                                                                )}
                                                                {item.url.includes('/notifications') && unreadNotifications > 0 && (
                                                                    <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                                                                        {unreadNotifications > 99 ? '99+' : unreadNotifications}
                                                                    </div>
                                                                )}
                                                            </div>
                                                            <span className="font-medium text-sm">{item.title}</span>
                                                        </Link>
                                                    </SidebarMenuButton>
                                                </SidebarMenuItem>
                                            );
                                        })}
                                    </SidebarMenu>
                                </SidebarGroupContent>
                            </SidebarGroup>
                        ))}
                    </>
                ) : (
                    // Whistleblower flat navigation
                    <SidebarGroup>
                        <SidebarGroupContent>
                            <SidebarMenu className="mt-8">
                                {navigationItems.map((item) => {
                                    const isActive = pathname === item.url;
                                    const isSecureMessage = item.url.includes('/secure-message');
                                    return (
                                        <SidebarMenuItem key={item.title}>
                                            <SidebarMenuButton
                                                asChild
                                                isActive={isActive}
                                                className="hover:bg-[#BBF49C] hover:text-[#1E4841] data-[active=true]:bg-[#BBF49C] data-[active=true]:text-[#1E4841] transition-colors duration-300 ease-in-out"
                                            >
                                                <Link href={item.url} className="gap-4 px-4 py-6 relative">
                                                    <div className="relative">
                                                        <item.icon />
                                                        {isSecureMessage && unreadMessages > 0 && (
                                                            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                                                                {unreadMessages > 99 ? '99+' : unreadMessages}
                                                            </div>
                                                        )}
                                                        {item.url.includes('/notifications') && unreadNotifications > 0 && (
                                                            <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1">
                                                                {unreadNotifications > 99 ? '99+' : unreadNotifications}
                                                            </div>
                                                        )}
                                                    </div>
                                                    <span className="font-medium hover:font-semibold text-base">{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    );
                                })}
                            </SidebarMenu>
                        </SidebarGroupContent>
                    </SidebarGroup>
                )}
            </SidebarContent>

            <SidebarFooter className="bg-[#ECF4E9]">
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton
                            asChild
                            className="hover:bg-[#BBF49C] hover:text-[#1E4841] data-[active=true]:bg-[#BBF49C] data-[active=true]:text-[#1E4841] transition-colors duration-300 ease-in-out"
                        >
                            <button 
                                onClick={async () => {
                                    try {
                                        setIsLoggingOut(true);
                                        logout();
                                        toast.success("Logged out successfully");
                                    } catch (error) {
                                        console.error('Logout error:', error);
                                        toast.error("Logout Failed", {
                                            description: "There was a problem logging out. Please try again."
                                        });
                                    } finally {
                                        setIsLoggingOut(false);
                                    }
                                }} 
                                className="gap-4 px-4 py-6 w-full flex items-center"
                                disabled={isLoggingOut}
                            >
                                <LogOut />
                                <span className="font-medium hover:font-semibold text-base">
                                    {isLoggingOut ? "Logging out..." : "Logout"}
                                </span>
                            </button>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarFooter>

            <SidebarRail />
        </Sidebar>
    )
}