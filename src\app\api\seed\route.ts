import { NextResponse } from 'next/server';
import { reseedDatabase } from '@/lib/db/refined-seed';


export async function POST() {
  try {
    // Authentication removed - seeding now available without auth
    
    const result = await reseedDatabase();
    return NextResponse.json({ 
      success: true, 
      message: 'Database seeded successfully',
      data: result
    });
  } catch (error) {
    console.error('Seeding error:', error);
    
    // Handle authentication errors
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to seed database' },
      { status: 500 }
    );
  }
}