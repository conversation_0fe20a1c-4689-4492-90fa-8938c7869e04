"use client";
import Link from "next/link";
import { memo } from "react";

interface NavLinkProps {
    href: string;
    children: React.ReactNode;
    onClick?: () => void;
}

const NavLink = memo(({ href, children, onClick }: NavLinkProps) => (
    <Link href={href} className="hover:bg-[#1E4841] hover:text-white px-3 py-2 rounded-md transition-colors duration-300 text-[#6B7271]" onClick={onClick}>
        {children}
    </Link>
));

NavLink.displayName = 'NavLink';

export default NavLink;