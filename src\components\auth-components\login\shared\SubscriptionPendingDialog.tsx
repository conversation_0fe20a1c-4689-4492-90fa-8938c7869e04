import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Link } from "lucide-react";
import router from "next/router";

interface SubscriptionPendingDialogProps {
    showPending: boolean;
    setShowPending: (value: boolean) => void;
}

export default function SubscriptionPendingDialog({ showPending, setShowPending }: SubscriptionPendingDialogProps) {
    return (
        <Dialog open={showPending} onOpenChange={setShowPending}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Subscription Pending</DialogTitle>
                    <DialogDescription>
                        Your subscription request is currently under review.
                        You will receive an email once your plan is approved and access is activated.
                    </DialogDescription>
                </DialogHeader>
                <div className="flex flex-col gap-4">
                    <div className="flex justify-between gap-4">
                        <Button variant="outline" onClick={() => router.push('/')}>
                            Login Later
                        </Button>
                        <Button variant="outline" onClick={() => window.location.href = 'mailto:<EMAIL>'}>
                            Contact Support
                        </Button>
                        <Button onClick={() => router.push('/signup/admin')}>
                            Choose Another Plan
                        </Button>
                    </div>
                    <p className="text-sm text-muted-foreground text-center">
                        Need immediate assistance? Contact our support team at{' '}
                        <Link href="mailto:<EMAIL>" className="text-primary hover:underline">
                            <EMAIL>
                        </Link>
                    </p>
                </div>
            </DialogContent>
        </Dialog>
    )
}