"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LogOut, CheckCircle, ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";

export default function LogoutPage() {
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const [isLoggedOut, setIsLoggedOut] = useState(false);
    const router = useRouter();
    const { logout } = useAuth();

    const handleLogout = async () => {
        setIsLoggingOut(true);

        // Simulate logout process
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Use the auth logout function
        logout();

        setIsLoggingOut(false);
        setIsLoggedOut(true);

        // Redirect to home page after 2 seconds
        setTimeout(() => {
            router.push('/');
        }, 2000);
    };

    if (isLoggedOut) {
        return (
            <div className="min-h-screen bg-[#F9FAFB] flex items-center justify-center p-4">
                <Card className="w-full max-w-md" role="main" aria-labelledby="logout-success-title">
                    <CardContent className="p-8 text-center">
                        <div
                            className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
                            aria-hidden="true"
                        >
                            <CheckCircle className="w-8 h-8 text-green-600" />
                        </div>
                        <h1
                            id="logout-success-title"
                            className="text-2xl font-bold text-[#1F2937] mb-2"
                        >
                            Successfully Logged Out
                        </h1>
                        <p className="text-[#6B7280] mb-6">
                            You have been securely logged out of your account. Thank you for using our platform.
                        </p>
                        <div className="space-y-3">
                            <p
                                className="text-sm text-[#6B7280]"
                                aria-live="polite"
                            >
                                Redirecting to home page in 2 seconds...
                            </p>
                            <Link href="/">
                                <Button
                                    className="w-full bg-[#1E4841] hover:bg-[#2A5D54] text-white"
                                    aria-label="Navigate to home page"
                                >
                                    Go to Home Page
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-[#F9FAFB] flex items-center justify-center p-4">
            <Card className="w-full max-w-md" role="main" aria-labelledby="logout-confirm-title">
                <CardHeader className="text-center">
                    <div
                        className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4"
                        aria-hidden="true"
                    >
                        <LogOut className="w-8 h-8 text-red-600" />
                    </div>
                    <CardTitle
                        id="logout-confirm-title"
                        className="text-2xl font-bold text-[#1F2937]"
                    >
                        Confirm Logout
                    </CardTitle>
                    <CardDescription className="text-[#6B7280]">
                        Are you sure you want to log out of your account?
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div
                        className="bg-yellow-50 border border-yellow-200 rounded-lg p-4"
                        role="alert"
                        aria-labelledby="logout-warning"
                    >
                        <p id="logout-warning" className="text-sm text-yellow-800">
                            <strong>Important:</strong> Make sure you have saved any unsaved work before logging out.
                        </p>
                    </div>

                    <div className="space-y-3" role="group" aria-labelledby="logout-actions">
                        <h3 id="logout-actions" className="sr-only">Logout Actions</h3>
                        <Button
                            onClick={handleLogout}
                            disabled={isLoggingOut}
                            className="w-full bg-red-600 hover:bg-red-700 text-white"
                            aria-describedby="logout-button-help"
                        >
                            {isLoggingOut ? (
                                <>
                                    <div
                                        className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"
                                        aria-hidden="true"
                                    ></div>
                                    <span aria-live="polite">Logging out...</span>
                                </>
                            ) : (
                                <>
                                    <LogOut className="w-4 h-4 mr-2" aria-hidden="true" />
                                    Yes, Log Me Out
                                </>
                            )}
                        </Button>
                        <div id="logout-button-help" className="sr-only">
                            This will securely log you out of your account and redirect you to the home page
                        </div>

                        <Link href="/dashboard">
                            <Button
                                variant="outline"
                                className="w-full"
                                aria-label="Cancel logout and return to dashboard"
                            >
                                <ArrowLeft className="w-4 h-4 mr-2" aria-hidden="true" />
                                Cancel, Go Back
                            </Button>
                        </Link>
                    </div>

                    <div className="text-center pt-4 border-t">
                        <p className="text-xs text-[#6B7280]">
                            Need help? <Link
                                href="/dashboard/help"
                                className="text-[#1E4841] hover:underline"
                                aria-label="Contact support for help"
                            >
                                Contact Support
                            </Link>
                        </p>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}