import { NextResponse } from 'next/server';
import { reseedDatabase } from '@/lib/db/refined-seed';

export const runtime = 'nodejs';

export async function POST() {
  try {
    console.log('🚀 API: Starting refined database reseeding...');
    
    const result = await reseedDatabase();
    
    console.log('✅ API: Refined seeding completed successfully');
    
    return NextResponse.json({
      success: true,
      message: 'Database reseeded with refined, consistent data',
      data: result.data,
      credentials: {
        admins: [
          { email: '<EMAIL>', password: 'admin123', company: 'TechCorp Industries' },
          { email: '<EMAIL>', password: 'admin123', company: 'Global Manufacturing Ltd' }
        ],
        investigators: [
          { email: '<EMAIL>', password: 'investigator123', company: 'TechCorp Industries' },
          { email: '<EMAIL>', password: 'investigator123', company: 'Global Manufacturing Ltd' }
        ],
        whistleblowers: [
          { email: '<EMAIL>', password: 'whistleblower123', company: 'TechCorp Industries' },
          { email: '<EMAIL>', password: 'whistleblower123', company: 'TechCorp Industries' },
          { email: '<EMAIL>', password: 'whistleblower123', company: 'Global Manufacturing Ltd' },
          { email: '<EMAIL>', password: 'whistleblower123', company: 'Global Manufacturing Ltd' }
        ]
      }
    });
  } catch (error) {
    console.error('❌ API: Refined seeding failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to reseed database',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Use POST method to trigger refined database reseeding',
    info: 'This endpoint will clear all existing data and create new consistent data following the whistleblower workflow'
  });
}