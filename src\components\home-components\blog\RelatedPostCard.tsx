import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Clock } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { BlogCard } from '@/lib/types';

interface RelatedPostCardProps {
  post: BlogCard;
}

/**
 * RelatedPostCard component for displaying related blog posts in a consistent format
 */
const RelatedPostCard: React.FC<RelatedPostCardProps> = ({ post }) => (
  <Card className="hover:shadow-md transition-shadow duration-300">
    <CardContent className="p-3 sm:p-4 flex gap-2 sm:gap-4">
      <div className="w-16 h-16 sm:w-20 sm:h-20 relative flex-shrink-0 rounded-md overflow-hidden">
        <Image
          src={post.image}
          alt={post.title}
          fill
          className="object-cover"
        />
      </div>
      <div className="flex flex-col">
        <p className="text-xs font-medium text-[#1E4841] mb-1">{post.category}</p>
        <h3 className="text-sm font-semibold text-[#242E2C] line-clamp-2 mb-1 hover:text-[#1E4841] transition-colors">
          <Link href={`/blog/${post.id}`}>{post.title}</Link>
        </h3>
        <div className="flex items-center gap-2 mt-auto">
          <Clock className="h-3 w-3 text-gray-500" />
          <span className="text-xs text-gray-500">{post.readTime}</span>
        </div>
      </div>
    </CardContent>
  </Card>
);

export default RelatedPostCard;