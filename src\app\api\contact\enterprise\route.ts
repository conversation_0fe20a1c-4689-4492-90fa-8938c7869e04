import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { emailService } from '@/lib/email/emailService';
import logger from '@/lib/utils/logger';

// Validation schema for contact form
const contactFormSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  company: z.string().min(2, 'Company name must be at least 2 characters'),
  jobTitle: z.string().min(2, 'Job title must be at least 2 characters'),
  phone: z.string().optional(),
  companySize: z.string().min(1, 'Company size is required'),
  industry: z.string().min(1, 'Industry is required'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  urgency: z.enum(['low', 'medium', 'high']).default('medium'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validatedData = contactFormSchema.parse(body);

    // Create contact submission record
    const contactSubmission = {
      id: `contact_${Date.now()}`,
      ...validatedData,
      submittedAt: new Date().toISOString(),
      status: 'pending',
      source: 'enterprise_signup'
    };

    // Send email notification to sales team
    try {
      const salesEmail = process.env.SALES_EMAIL || process.env.CONTACT_FORM_RECIPIENT || process.env.EMAIL_SERVER_USER;

      if (!salesEmail) {
        logger.error('No sales email configured for enterprise contact form');
        return NextResponse.json(
          {
            success: false,
            error: 'Email service is not configured. Please contact the administrator.'
          },
          { status: 500 }
        );
      }

      // Send notification to sales team
      const salesEmailSent = await emailService.sendContactFormEmail({
        name: validatedData.fullName,
        email: validatedData.email,
        company: validatedData.company,
        phone: validatedData.phone,
        subject: `Enterprise Demo Request - ${validatedData.company}`,
        message: validatedData.message,
        department: 'sales'
      });

      if (!salesEmailSent) {
        throw new Error('Failed to send sales notification email');
      }

      // Send auto-reply to user
      try {
        await emailService.sendAutoReplyEmail({
          name: validatedData.fullName,
          email: validatedData.email,
          company: validatedData.company,
          phone: validatedData.phone,
          subject: `Enterprise Demo Request - ${validatedData.company}`,
          message: validatedData.message,
          department: 'sales'
        });
        logger.info('Enterprise contact auto-reply sent', { email: validatedData.email });
      } catch (autoReplyError) {
        logger.error('Failed to send auto-reply email:', autoReplyError);
        // Don't fail the main request if auto-reply fails
      }

      logger.info('Enterprise contact form submission processed', {
        submissionId: contactSubmission.id,
        email: validatedData.email,
        company: validatedData.company
      });

      return NextResponse.json({
        success: true,
        data: {
          submissionId: contactSubmission.id,
          message: 'Thank you for your interest! Our enterprise sales team will contact you within 24 hours.',
          estimatedResponseTime: '24 hours'
        }
      });

    } catch (emailError) {
      logger.error('Failed to process enterprise contact form:', emailError);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to send your message. Please try again later.'
        },
        { status: 500 }
      );
    }
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: error.issues 
        },
        { status: 400 }
      );
    }
    
    logger.error('Contact form API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to submit contact form. Please try again.'
      },
      { status: 500 }
    );
  }
}
