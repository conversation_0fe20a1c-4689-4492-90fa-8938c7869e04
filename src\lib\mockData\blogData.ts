import { BlogCard } from "@/lib/types";

// All blog posts
export const ALL_BLOG_POSTS: BlogCard[] = [
  {
    id: "001",
    image: "/desktop/blog/featured/f-blog1.jpg",
    category: "Compliance Updates",
    date: "June 1, 2025",
    title: "New EU Whistleblower Protection Directive: What Organizations Need to Know",
    description: "The latest updates to the EU Whistleblower Protection Directive bring significant changes to compliance requirements. Learn how these changes impact your organization and what steps you need to take to ensure compliance.",
    author: {
      image: "/desktop/blog/authors/Dr. <PERSON>.jpg",
      name: "Dr. <PERSON>",
      initials: "EM"
    },
    readTime: "8 min read",
    slug: "new-eu-whistleblower-protection-directive",
    featured: true,
    tags: ['EU Directive', 'Whistleblower Protection', 'Compliance', 'Regulatory Updates'],
    content: `
      <div class="blog-content">
        <p class="lead">The European Union has recently introduced significant updates to the Whistleblower Protection Directive, marking a pivotal shift in how organizations across member states must approach whistleblowing mechanisms and protections.</p>
        
        <div class="info-box">
          <h4>Key Takeaways</h4>
          <ul>
            <li>Expanded protection for contractors, volunteers, and job applicants</li>
            <li>Lower threshold for mandatory reporting channels (50+ employees)</li>
            <li>Compliance deadline: December 2025</li>
            <li>Stricter penalties for non-compliance</li>
          </ul>
        </div>
        
        <h2>Key Changes in the Updated Directive</h2>
        
        <p>The revised directive expands protection to include not only employees but also contractors, volunteers, and job applicants who report violations. This broader scope means organizations must reconsider their reporting channels and protection measures.</p>
        
        <p>Additionally, the directive now mandates that all organizations with 50 or more employees establish formal internal reporting channels, a significant change from the previous threshold of 250 employees.</p>
        
        <blockquote>
          "The expanded scope of the EU Whistleblower Protection Directive represents one of the most significant regulatory shifts in corporate compliance this decade."
          <cite>— European Commission on Corporate Governance</cite>
        </blockquote>
        
        <h2>Implementation Timeline</h2>
        
        <p>Organizations have until December 2025 to fully comply with these new requirements. This timeline provides a critical window for preparation, but given the extensive changes required, early adoption is strongly recommended.</p>
        
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-marker">Q3 2025</div>
            <div class="timeline-content">Begin policy review and gap analysis</div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker">Q4 2025</div>
            <div class="timeline-content">Implement new reporting channels and train staff</div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker">Dec 2025</div>
            <div class="timeline-content">Full compliance deadline</div>
          </div>
        </div>
        
        <h2>Required Actions for Organizations</h2>
        
        <ul class="checklist">
          <li>Review and update existing whistleblowing policies</li>
          <li>Implement secure and confidential reporting channels</li>
          <li>Establish clear procedures for handling reports</li>
          <li>Train relevant staff on the new requirements</li>
          <li>Develop protection mechanisms for whistleblowers</li>
        </ul>
      </div>
    `
  },
  {
    id: "002",
    image: "/desktop/blog/featured/f-blog2.jpg",
    category: "Best Practices",
    date: "May 28, 2025",
    title: "Building a Culture of Ethical Reporting: 5 Strategies That Work",
    description: "Creating an environment where employees feel safe to report misconduct is crucial for organizational integrity. Discover five proven strategies to foster a culture of ethical reporting and transparency.",
    author: {
      image: "/desktop/blog/authors/James Chen, CCEP.jpg",
      name: "James Chen, CCEP",
      initials: "JC"
    },
    readTime: "8 min read",
    slug: "building-culture-ethical-reporting",
    featured: true,
    tags: ['Ethical Culture', 'Reporting Channels', 'Anti-Retaliation', 'Corporate Ethics'],
    content: `
      <div class="blog-content">
        <p class="lead">Creating a culture where employees feel empowered to report ethical concerns is essential for maintaining organizational integrity and preventing misconduct. Here are five proven strategies that can help foster such an environment.</p>
        
        <div class="strategy-card">
          <div class="strategy-number">1</div>
          <div class="strategy-content">
            <h2>Lead by Example</h2>
            
            <p>Leadership sets the tone for ethical behavior throughout the organization. When leaders demonstrate commitment to ethical principles and openly discuss the importance of reporting concerns, employees are more likely to follow suit.</p>
            
            <p>Actions speak louder than words—executives and managers should model the behavior they expect from their teams, including acknowledging mistakes and demonstrating accountability.</p>
            
            <div class="action-steps">
              <h4>Action Steps:</h4>
              <ul>
                <li>Include ethics discussions in regular team meetings</li>
                <li>Share examples of how reported issues led to positive changes</li>
                <li>Recognize and reward ethical behavior publicly</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div class="strategy-card">
          <div class="strategy-number">2</div>
          <div class="strategy-content">
            <h2>Provide Multiple Reporting Channels</h2>
            
            <p>Different employees may feel comfortable with different reporting methods. Offering various channels—such as anonymous hotlines, digital platforms, designated ethics officers, and open-door policies—ensures that everyone has access to a reporting method that works for them.</p>
          </div>
        </div>
      </div>
    `
  },
  {
    id: "003",
    image: "/desktop/blog/featured/f-blog3.jpg",
    category: "Case Studies",
    date: "May 15, 2025",
    title: "How Acme Corporation Transformed Their Compliance Program",
    description: "After facing regulatory challenges, Acme Corporation revamped their compliance program with remarkable results. This case study examines their journey, the solutions implemented, and the measurable outcomes achieved.",
    author: {
      image: "/desktop/blog/authors/Olivia Washington.jpg",
      name: "Olivia Washington",
      initials: "OW"
    },
    readTime: "1 min read",
    slug: "how-acme-corporation-transformed-compliance",
    featured: true,
    tags: ['Case Study', 'Compliance Program', 'Transformation', 'Best Practices'],
    content: `
      <div class="blog-content">
        <p class="lead">Acme Corporation, a global manufacturing company with over 10,000 employees, faced significant compliance challenges in 2023. This case study examines how they transformed their approach to create a model compliance program.</p>
        
        <h2>The Challenge</h2>
        
        <p>Following a series of whistleblower complaints and a subsequent regulatory investigation, Acme Corporation was fined $5 million for compliance failures. The company faced several critical issues:</p>
        
        <ul>
          <li>Inconsistent reporting mechanisms across global locations</li>
          <li>Low employee trust in the reporting process</li>
          <li>Inadequate investigation procedures</li>
          <li>Poor documentation and follow-up on reported concerns</li>
        </ul>
        
        <h2>The Solution</h2>
        
        <p>Acme's leadership team recognized the need for a comprehensive overhaul of their compliance program. They implemented a multi-phase approach:</p>
        
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-marker">Phase 1</div>
            <div class="timeline-content">Assessment and planning (3 months)</div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker">Phase 2</div>
            <div class="timeline-content">Technology implementation (4 months)</div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker">Phase 3</div>
            <div class="timeline-content">Training and communication (ongoing)</div>
          </div>
        </div>
      </div>
    `
  },
  {
    id: "004",
    image: "/desktop/blog/category/c-blog1.jpg",
    category: "Compliance Updates",
    date: "May 30, 2025",
    title: "Global Compliance Trends to Watch in 2025",
    description: "Stay ahead of the curve with our analysis of emerging compliance trends that will shape regulatory landscapes worldwide in 2025.",
    author: {
      image: "/desktop/blog/authors/Michael Reeves.jpg",
      name: "Michael Reeves",
      initials: "MR"
    },
    readTime: "8 min read",
    slug: "global-compliance-trends-2025",
    tags: ['Compliance Trends', 'Regulatory Updates', 'AI Governance', 'ESG Reporting'],
    content: `
      <div class="blog-content">
        <p class="lead">As regulatory environments continue to evolve globally, compliance professionals must stay informed about emerging trends that will impact their organizations. Here are the key compliance trends to watch in 2025.</p>
        
        <h2>1. AI Governance Frameworks</h2>
        
        <p>With artificial intelligence becoming increasingly integrated into business operations, regulators worldwide are developing frameworks to govern its use. Organizations must prepare for new requirements related to AI transparency, bias mitigation, and ethical use.</p>
        
        <div class="info-box">
          <h4>Key AI Governance Developments</h4>
          <ul>
            <li>EU AI Act implementation timeline</li>
            <li>US sector-specific AI regulations</li>
            <li>Global standards for AI risk assessments</li>
            <li>Requirements for algorithmic impact analyses</li>
          </ul>
        </div>
        
        <h2>2. Enhanced ESG Reporting Requirements</h2>
        
        <p>Environmental, Social, and Governance (ESG) reporting is moving from voluntary to mandatory in many jurisdictions. Companies should prepare for more stringent disclosure requirements and verification processes.</p>
      </div>
    `
  },
  {
    id: "005",
    image: "/desktop/blog/category/c-blog2.jpg",
    category: "Best Practices",
    date: "May 27, 2025",
    title: "Designing an Effective Whistleblower Policy",
    description: "Learn the essential components of a robust whistleblower policy that encourages reporting while protecting both the organization and its employees.",
    author: {
      image: "/desktop/blog/authors/Sofia Rodriguez.jpg",
      name: "Sofia Rodriguez",
      initials: "SR"
    },
    readTime: "8 min read",
    slug: "designing-effective-whistleblower-policy",
    tags: ['Whistleblower Policy', 'Best Practices', 'Compliance', 'Corporate Ethics'],
    content: `
      <div class="blog-content">
        <p class="lead">A well-designed whistleblower policy is essential for fostering a speak-up culture and ensuring that potential misconduct is identified and addressed promptly. This guide outlines the key elements of an effective whistleblower policy.</p>
        
        <h2>Clear Scope and Purpose</h2>
        
        <p>Your policy should clearly define its purpose and scope, including:</p>
        
        <ul>
          <li>Types of misconduct covered (e.g., legal violations, ethical breaches, safety concerns)</li>
          <li>Who can report concerns (employees, contractors, suppliers, customers)</li>
          <li>Geographic and operational scope</li>
        </ul>
        
        <div class="example-box">
          <h4>Example Policy Statement</h4>
          <p>"This policy applies to all employees, contractors, and business partners of [Company Name] globally. It covers reports of suspected legal violations, policy breaches, financial irregularities, safety concerns, and ethical misconduct that may affect our company, stakeholders, or the public interest."</p>
        </div>
        
        <h2>Multiple Reporting Channels</h2>
        
        <p>Provide various ways for individuals to report concerns, such as:</p>
        
        <ul>
          <li>Direct manager reporting</li>
          <li>HR department</li>
          <li>Dedicated ethics office or compliance team</li>
          <li>Anonymous hotline (phone and/or web-based)</li>
          <li>Dedicated email address</li>
        </ul>
      </div>
    `
  },
  {
    id: "006",
    image: "/desktop/blog/category/c-blog3.jpg",
    category: "Industry Trends",
    date: "May 25, 2025",
    title: "AI in Compliance: Opportunities and Challenges",
    description: "Artificial intelligence is transforming compliance management. Discover how AI tools can enhance monitoring capabilities while navigating ethical considerations.",
    author: {
      image: "/desktop/blog/authors/Dr. Raj Patel.jpg",
      name: "Dr. Raj Patel",
      initials: "RP"
    },
    readTime: "1 min read",
    slug: "ai-in-compliance",
    tags: ['AI', 'Compliance Technology', 'Ethics', 'Innovation'],
    content: `
      <div class="blog-content">
        <p class="lead">Artificial intelligence is revolutionizing compliance management, offering unprecedented capabilities for monitoring, analysis, and risk detection. However, these opportunities come with significant challenges that organizations must address.</p>
        
        <h2>Key AI Applications in Compliance</h2>
        
        <div class="grid-box">
          <div class="grid-item">
            <h3>Transaction Monitoring</h3>
            <p>AI systems can analyze millions of transactions in real-time, identifying patterns and anomalies that might indicate fraud, money laundering, or other compliance issues.</p>
          </div>
          
          <div class="grid-item">
            <h3>Policy Management</h3>
            <p>Natural language processing can help analyze regulatory changes and automatically update internal policies to ensure alignment with current requirements.</p>
          </div>
          
          <div class="grid-item">
            <h3>Risk Assessment</h3>
            <p>Predictive analytics can identify emerging risks before they materialize, allowing for proactive compliance management.</p>
          </div>
          
          <div class="grid-item">
            <h3>Training Optimization</h3>
            <p>AI can personalize compliance training based on employee roles, past behavior, and risk profiles, increasing effectiveness and engagement.</p>
          </div>
        </div>
      </div>
    `
  },
  {
    id: "007",
    image: "/desktop/blog/category/c-blog4.jpg",
    category: "Case Studies",
    date: "May 22, 2025",
    title: "Financial Services: Compliance Success Story",
    description: "How a leading financial institution revamped their whistleblowing system to address regulatory concerns and foster a speak-up culture.",
    author: {
      image: "/desktop/blog/authors/Dr. Elizabeth Morgan.jpg",
      name: "Dr. Elizabeth Morgan",
      initials: "EM"
    },
    readTime: "8 min read",
    slug: "financial-services-compliance-success",
    tags: ['Financial Services', 'Case Study', 'Whistleblowing', 'Compliance'],
    content: `
      <div class="blog-content">
        <p class="lead">When Global Financial Partners (GFP), a multinational financial services firm with over 50,000 employees, faced regulatory scrutiny over its whistleblowing procedures, the company embarked on a comprehensive transformation of its compliance program.</p>
        
        <h2>The Challenge</h2>
        
        <p>Following a series of high-profile incidents where employee concerns were mishandled, GFP was subject to regulatory action, including a $25 million fine and enhanced supervision requirements. Key issues identified included:</p>
        
        <ul>
          <li>Inconsistent handling of whistleblower reports</li>
          <li>Inadequate protection for reporters</li>
          <li>Poor documentation of investigation processes</li>
          <li>Lack of senior management oversight</li>
          <li>Insufficient training for managers and investigators</li>
        </ul>
        
        <h2>The Solution</h2>
        
        <p>GFP implemented a comprehensive whistleblowing transformation program with the following key components:</p>
        
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-marker">Month 1-2</div>
            <div class="timeline-content">Comprehensive program assessment and benchmarking</div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker">Month 3-5</div>
            <div class="timeline-content">Implementation of new whistleblowing platform and protocols</div>
          </div>
          <div class="timeline-item">
            <div class="timeline-marker">Month 6-8</div>
            <div class="timeline-content">Global training rollout and communication campaign</div>
          </div>
        </div>
      </div>
    `
  },
  {
    id: "008",
    image: "/desktop/blog/category/c-blog5.jpg",
    category: "Best Practices",
    date: "May 20, 2025",
    title: "Anonymous Reporting: Best Practices for Implementation",
    description: "Implementing anonymous reporting channels requires careful planning. Learn how to balance anonymity with effective investigation procedures.",
    author: {
      image: "/desktop/blog/authors/James Chen, CCEP.jpg",
      name: "James Chen, CCEP",
      initials: "JC"
    },
    readTime: "7 min read",
    slug: "anonymous-reporting-best-practices",
    tags: ['Anonymous Reporting', 'Best Practices', 'Whistleblowing', 'Investigations'],
    content: `
      <div class="blog-content">
        <p class="lead">Anonymous reporting channels are essential components of an effective compliance program, but they present unique challenges for organizations. This article explores best practices for implementing and managing anonymous reporting systems.</p>
        
        <h2>The Value of Anonymous Reporting</h2>
        
        <p>Research consistently shows that anonymous reporting channels are critical for encouraging whistleblowers to come forward. According to the Ethics & Compliance Initiative's 2024 Global Business Ethics Survey, organizations with well-implemented anonymous reporting systems identify misconduct 50% more frequently than those without such systems.</p>
        
        <blockquote>
          "Anonymous reporting options remove a significant barrier to speaking up, particularly in cases involving senior management or sensitive issues like harassment."
          <cite>— Association of Certified Fraud Examiners, 2025 Report</cite>
        </blockquote>
        
        <h2>Key Implementation Considerations</h2>
        
        <div class="checklist">
          <h3>Technology Selection</h3>
          <ul>
            <li>Choose platforms with robust security features</li>
            <li>Ensure the system allows two-way anonymous communication</li>
            <li>Consider accessibility across different devices and locations</li>
            <li>Evaluate integration capabilities with case management systems</li>
          </ul>
        </div>
      </div>
    `
  },
  {
    id: "009",
    image: "/desktop/blog/no-image.png",
    category: "Company News",
    date: "May 1, 2025",
    title: "Announcing Our New Compliance Training Program",
    description: "We're excited to launch our comprehensive compliance training program designed to help organizations build a culture of integrity and ethical conduct.",
    author: {
      image: "/desktop/blog/no-image.png",
      name: "Katherine Bennett",
      initials: "KB"
    },
    readTime: "5 min read",
    slug: "new-compliance-training-program",
    tags: ['Training', 'Compliance', 'Company News', 'Education'],
    content: `
      <div class="blog-content">
        <p class="lead">We are thrilled to announce the launch of our new Compliance Excellence Training Program, designed to help organizations of all sizes build and maintain effective compliance cultures through engaging, practical education.</p>
        
        <h2>Program Overview</h2>
        
        <p>Our new training program addresses the most critical compliance challenges facing organizations today, with modules covering:</p>
        
        <ul>
          <li>Whistleblower protection and reporting procedures</li>
          <li>Anti-corruption and bribery prevention</li>
          <li>Data privacy and protection</li>
          <li>Ethical decision-making frameworks</li>
          <li>Conflicts of interest management</li>
          <li>Industry-specific regulatory requirements</li>
        </ul>
        
        <div class="info-box">
          <h4>Key Program Features</h4>
          <ul>
            <li>Scenario-based learning with real-world examples</li>
            <li>Microlearning modules for flexible implementation</li>
            <li>Customizable content to align with your policies</li>
            <li>Multi-language support for global organizations</li>
            <li>Comprehensive analytics and reporting</li>
          </ul>
        </div>
      </div>
    `
  }
];

// Helper functions
export const getFeaturedPosts = () => ALL_BLOG_POSTS.filter(post => post.featured);
export const getNonFeaturedPosts = () => ALL_BLOG_POSTS.filter(post => !post.featured);

// Get blog post by ID
export function getBlogPostById(id: string) {
  return ALL_BLOG_POSTS.find(post => post.id === id);
}

// Get blog post by slug
export function getBlogPostBySlug(slug: string) {
  return ALL_BLOG_POSTS.find(post => post.slug === slug);
}

// Get related blog posts (excluding the current post)
export function getRelatedBlogPosts(currentPostId: string, count: number = 3) {
  const currentPost = getBlogPostById(currentPostId);
  if (!currentPost) return [];
  
  // First try to find posts in the same category
  let relatedPosts = ALL_BLOG_POSTS.filter(post => 
    post.id !== currentPostId && 
    post.category === currentPost.category
  );
  
  // If we don't have enough, add posts with matching tags
  if (relatedPosts.length < count && currentPost.tags) {
    const postsWithMatchingTags = ALL_BLOG_POSTS.filter(post => 
      post.id !== currentPostId && 
      post.category !== currentPost.category &&
      post.tags?.some(tag => currentPost.tags?.includes(tag))
    );
    
    // Add posts with matching tags that aren't already in relatedPosts
    for (const post of postsWithMatchingTags) {
      if (relatedPosts.length >= count) break;
      if (!relatedPosts.some(p => p.id === post.id)) {
        relatedPosts.push(post);
      }
    }
  }
  
  // If we still don't have enough, add other posts
  if (relatedPosts.length < count) {
    const otherPosts = ALL_BLOG_POSTS.filter(post => 
      post.id !== currentPostId && 
      !relatedPosts.some(p => p.id === post.id)
    );
    
    relatedPosts = [...relatedPosts, ...otherPosts.slice(0, count - relatedPosts.length)];
  }
  
  return relatedPosts.slice(0, count);
}