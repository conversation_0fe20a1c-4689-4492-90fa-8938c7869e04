"use client";

import { useEffect } from "react";
import Header from "@/components/dashboard-components/Header";
import { notificationSystem } from "@/lib/utils/notificationSystem";
import { notificationsData } from "@/lib/mockData/notificationData";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Server, Database, Shield, Mail, Globe, HardDrive, Cpu } from "lucide-react";

export default function SystemPage() {
    // Initialize notification system
    useEffect(() => {
        notificationSystem.updateNotifications(notificationsData);
    }, []);

    const systemStatus = [
        {
            service: "Web Server",
            status: "Running",
            uptime: "99.9%",
            icon: Server,
            color: "text-green-600 bg-green-50"
        },
        {
            service: "Database",
            status: "Running",
            uptime: "99.8%",
            icon: Database,
            color: "text-green-600 bg-green-50"
        },
        {
            service: "Security Service",
            status: "Running",
            uptime: "100%",
            icon: Shield,
            color: "text-green-600 bg-green-50"
        },
        {
            service: "Email Service",
            status: "Running",
            uptime: "98.5%",
            icon: Mail,
            color: "text-green-600 bg-green-50"
        }
    ];

    const systemSettings = [
        {
            category: "Email Notifications",
            description: "Configure system email notifications",
            enabled: true
        },
        {
            category: "SMS Alerts",
            description: "Enable SMS alerts for critical events",
            enabled: false
        },
        {
            category: "Auto Backup",
            description: "Automatic daily system backups",
            enabled: true
        },
        {
            category: "Maintenance Mode",
            description: "Enable maintenance mode for updates",
            enabled: false
        },
        {
            category: "Debug Logging",
            description: "Enable detailed system logging",
            enabled: false
        },
        {
            category: "API Rate Limiting",
            description: "Enable API request rate limiting",
            enabled: true
        }
    ];

    const systemResources = [
        {
            resource: "CPU Usage",
            value: "23%",
            icon: Cpu,
            color: "text-green-600",
            status: "Normal"
        },
        {
            resource: "Memory Usage",
            value: "67%",
            icon: HardDrive,
            color: "text-yellow-600",
            status: "Moderate"
        },
        {
            resource: "Disk Space",
            value: "45%",
            icon: HardDrive,
            color: "text-green-600",
            status: "Normal"
        },
        {
            resource: "Network",
            value: "12%",
            icon: Globe,
            color: "text-green-600",
            status: "Normal"
        }
    ];

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="System"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900 mb-2">System</h1>
                                <p className="text-gray-600">Monitor system status and configure settings</p>
                            </div>
                            <Button className="bg-[#BBF49C] text-[#1E4841] hover:bg-[#BBF49C]/90">
                                System Health Check
                            </Button>
                        </div>
                    </div>

                    {/* System Resources */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        {systemResources.map((resource, index) => (
                            <Card key={index} className="bg-white border-0 shadow-sm">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-600">
                                        {resource.resource}
                                    </CardTitle>
                                    <resource.icon className={`h-4 w-4 ${resource.color}`} />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-gray-900 mb-1">
                                        {resource.value}
                                    </div>
                                    <Badge 
                                        variant="secondary" 
                                        className={`text-xs ${
                                            resource.status === 'Normal' ? 'bg-green-100 text-green-800' :
                                            resource.status === 'Moderate' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-red-100 text-red-800'
                                        }`}
                                    >
                                        {resource.status}
                                    </Badge>
                                </CardContent>
                            </Card>
                        ))}
                    </div>

                    {/* System Status */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                System Services
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {systemStatus.map((service, index) => (
                                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <div className={`p-2 rounded-lg ${service.color}`}>
                                                <service.icon className="h-5 w-5" />
                                            </div>
                                            <div>
                                                <h3 className="font-medium text-gray-900">{service.service}</h3>
                                                <p className="text-sm text-gray-600">Uptime: {service.uptime}</p>
                                            </div>
                                        </div>
                                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                                            {service.status}
                                        </Badge>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* System Settings */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                System Configuration
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {systemSettings.map((setting, index) => (
                                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                        <div>
                                            <h3 className="font-medium text-gray-900">{setting.category}</h3>
                                            <p className="text-sm text-gray-600">{setting.description}</p>
                                        </div>
                                        <Switch 
                                            checked={setting.enabled}
                                            className="data-[state=checked]:bg-[#BBF49C]"
                                        />
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* System Actions */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                System Actions
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex flex-col items-center justify-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C]"
                                >
                                    <Database className="h-5 w-5" />
                                    <span>Backup Now</span>
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex flex-col items-center justify-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C]"
                                >
                                    <Server className="h-5 w-5" />
                                    <span>Restart Services</span>
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex flex-col items-center justify-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C]"
                                >
                                    <Shield className="h-5 w-5" />
                                    <span>Security Scan</span>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </main>
            </div>
        </>
    );
}