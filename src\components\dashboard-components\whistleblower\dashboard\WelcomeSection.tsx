import { ShieldCheck } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

interface WelcomeSectionProps {
    userName: string;
}

export default function WelcomeSection({ userName }: WelcomeSectionProps) {
    const { getPreviousLoginFormatted, getSessionDuration } = useAuth();

    return (
        <div className="p-3 sm:p-4 md:p-6">
            <h1 className="text-xl sm:text-2xl xl:text-2xl font-semibold text-[#242E2C] mb-2">
                Welcome back, {userName}!
            </h1>
            <p className="text-[#6B7271] font-normal text-sm sm:text-base mb-4">
                Track your report submissions, response progress, and follow up securely.
            </p>
            <div className="flex flex-col lg:flex-row lg:items-center gap-2 lg:gap-4 text-xs sm:text-sm font-normal text-[#6B7280]">
                <span className="flex items-center gap-2">
                    <ShieldCheck className="w-4 h-4" />
                    Secure connection established
                </span>
                <span className="hidden lg:inline text-3xl/0">•</span>
                <span>
                    Last login: {getPreviousLoginFormatted()}
                </span>
                <span className="hidden lg:inline text-3xl/0">•</span>
                <span>
                    Session: {getSessionDuration()}
                </span>
            </div>
        </div>
    );
}