"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Arrow<PERSON>own<PERSON><PERSON><PERSON><PERSON>, Eye, Paperclip } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface MessageBubbleProps {
  message: {
    id: string;
    content: string;
    timestamp: string;
    isFromUser: boolean;
    senderName?: string;
    attachments?: Array<{
      name?: string;
      fileName?: string;
      size?: number;
      fileSize?: string;
    }>;
    html?: string;
  };
  senderAvatar?: string;
  senderName?: string;
}

export default function MessageBubble({ message, senderAvatar, senderName }: MessageBubbleProps) {
  if (message.isFromUser) {
    return (
      <div className="flex items-start gap-3 justify-end">
        <div className="flex flex-col gap-2 max-w-md">
          <div className="flex items-center gap-2 justify-end">
            <p className="text-xs text-[#6B7280]">{message.timestamp}</p>
            <p className="text-sm font-medium text-[#111827]">You</p>
          </div>
          <div className="bg-[#ECF4E9] rounded-2xl rounded-tr-md p-3">
            <div
              className="text-sm text-[#1F2937]"
              dangerouslySetInnerHTML={{
                __html: message.html || message.content
              }}
            />
            {message.attachments && message.attachments.length > 0 && (
              <div className="mt-3 space-y-2">
                {message.attachments.map((file, fileIndex: number) => (
                  <div key={fileIndex} className="bg-white border border-[#E5E7EB] rounded-lg p-2">
                    <div className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-lg bg-green-100 flex items-center justify-center">
                        <Paperclip className="w-4 h-4 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-medium text-[#111827]">{file.name || file.fileName}</p>
                        <p className="text-xs text-[#6B7280]">{file.size ? `${(file.size / 1024).toFixed(1)} KB` : file.fileSize}</p>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" className="p-1">
                          <ArrowDownToLine className="w-3 h-3 text-[#6B7271]" />
                        </Button>
                        <Button variant="ghost" size="sm" className="p-1">
                          <Eye className="w-3 h-3 text-[#6B7271]" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="flex items-center gap-2 text-xs text-[#6B7280] justify-end">
            <LockKeyhole className="w-3 h-3" />
            <span>Encrypted</span>
            <span>•</span>
            <CheckCheck className="w-3 h-3 text-green-600" />
            <span>Sent</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-start gap-3">
      <div className={`h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0 ${senderAvatar || "bg-[#BBF49C]"}`}>
        <span className="text-xs font-medium text-[#1E4841]">
          {senderName?.split(' ').map(n => n[0]).join('').slice(0, 2)}
        </span>
      </div>
      <div className="flex flex-col gap-2 max-w-md">
        <div className="flex items-center gap-2">
          <p className="text-sm font-medium text-[#111827]">{message.senderName}</p>
          <p className="text-xs text-[#6B7280]">{message.timestamp}</p>
        </div>
        <div className="bg-[#F9FAFB] border border-[#E5E7EB] rounded-2xl rounded-tl-md p-3">
          <p className="text-sm text-[#1F2937]">
            {message.content}
          </p>
        </div>
        <div className="flex items-center gap-2 text-xs text-[#6B7280]">
          <LockKeyhole className="w-3 h-3" />
          <span>Encrypted</span>
          <span>•</span>
          <span>Delivered</span>
        </div>
      </div>
    </div>
  );
}