"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, FileText, Search, AlertCircle, LogOut } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface AnonymousSession {
  companyName: string;
  sessionId: string;
  expiresAt: string;
}

export default function AnonymousDashboard() {
  const router = useRouter();
  const [session, setSession] = useState<AnonymousSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for anonymous session
    const sessionData = localStorage.getItem('anonymousSession');
    if (sessionData) {
      try {
        const parsedSession = JSON.parse(sessionData);
        const expiresAt = new Date(parsedSession.expiresAt);
        
        if (expiresAt > new Date()) {
          setSession(parsedSession);
        } else {
          // Session expired
          localStorage.removeItem('anonymousSession');
          toast({
            title: "Session Expired",
            description: "Your anonymous session has expired. Please start a new session.",
            variant: "destructive"
          });
          router.push('/login/whistleblower');
        }
      } catch (error) {
        console.error('Error parsing session data:', error);
        localStorage.removeItem('anonymousSession');
        router.push('/login/whistleblower');
      }
    } else {
      router.push('/login/whistleblower');
    }
    setIsLoading(false);
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('anonymousSession');
    toast({
      title: "Session Ended",
      description: "Your anonymous session has been ended."
    });
    router.push('/login/whistleblower');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1E4841] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading anonymous dashboard...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-[#1E4841] mr-3" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Anonymous Portal</h1>
                <p className="text-sm text-gray-600">{session.companyName}</p>
              </div>
            </div>
            <Button 
              variant="outline" 
              onClick={handleLogout}
              className="flex items-center gap-2"
            >
              <LogOut className="h-4 w-4" />
              End Session
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to the Anonymous Reporting Portal</h2>
          <p className="text-gray-600">
            Your identity is completely protected. You can submit reports and track their progress anonymously.
          </p>
        </div>

        {/* Session Info */}
        <Card className="mb-8 border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-800">Anonymous Session Active</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Session expires: {new Date(session.expiresAt).toLocaleString()}
                </p>
                <p className="text-sm text-blue-700">
                  Session ID: {session.sessionId}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Submit Report */}
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <FileText className="h-6 w-6 text-[#1E4841]" />
                Submit New Report
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Submit a new anonymous report about misconduct, fraud, or other concerns.
              </p>
              <Link href="/dashboard/anonymous/new-report">
                <Button className="w-full bg-[#1E4841] hover:bg-[#1E4841]/90">
                  Start Report
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Track Report */}
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Search className="h-6 w-6 text-[#1E4841]" />
                Track Existing Report
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Check the status of a previously submitted report using your reference number.
              </p>
              <Link href="/login/whistleblower">
                <Button variant="outline" className="w-full">
                  Track Report
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Help & Support */}
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Shield className="h-6 w-6 text-[#1E4841]" />
                Help & Support
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Get help with the reporting process or learn about our privacy protections.
              </p>
              <Link href="/help">
                <Button variant="outline" className="w-full">
                  Get Help
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* Privacy Notice */}
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800">Your Privacy is Protected</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-green-700 space-y-2">
              <li>• Your IP address and personal information are not logged</li>
              <li>• All communications are encrypted end-to-end</li>
              <li>• Reports are processed without revealing your identity</li>
              <li>• You can track your report status using only the reference number</li>
              <li>• No cookies or tracking technologies are used in anonymous mode</li>
            </ul>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
