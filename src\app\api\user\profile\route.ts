import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import User from '@/lib/db/models/User';

export const runtime = 'nodejs';

// GET user profile
export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    const user = await User.findById(request.user.id)
      .select('-hashedPassword -passwordResetToken -emailVerificationToken -unlockToken -twoFactor.secret -twoFactor.backupCodes')
      .populate('companyId', 'name');
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    // Transform user data for frontend
    const profileData = {
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email,
      phone: user.phoneNumber || '',
      department: '', // This would come from a separate department field or company data
      jobTitle: '', // This would come from a separate jobTitle field
      employeeId: '', // This would come from a separate employeeId field
      preferredLanguage: user.preferences?.language || 'English',
      timezone: 'UTC', // This would come from user preferences
      notificationPreferences: {
        emailNotifications: user.preferences?.notifications?.email ?? true,
        smsNotifications: user.preferences?.notifications?.sms ?? false,
        reportUpdates: true, // This would be a separate field
        systemAlerts: true, // This would be a separate field
      },
      privacySettings: {
        profileVisibility: 'private', // This would be a separate field
        allowDirectContact: false, // This would be a separate field
        shareReportingHistory: false, // This would be a separate field
      },
    };
    
    return NextResponse.json({
      success: true,
      data: profileData
    });
  } catch (error) {
    console.error('Get user profile API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

// PUT update user profile
export const PUT = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    const profileData = await request.json();
    
    // Validate required fields
    if (!profileData.firstName || !profileData.lastName || !profileData.email) {
      return NextResponse.json(
        { success: false, error: 'First name, last name, and email are required' },
        { status: 400 }
      );
    }
    
    // Check if email is already taken by another user
    if (profileData.email !== request.user.email) {
      const existingUser = await User.findOne({ 
        email: profileData.email, 
        _id: { $ne: request.user.id } 
      });
      
      if (existingUser) {
        return NextResponse.json(
          { success: false, error: 'Email is already in use' },
          { status: 400 }
        );
      }
    }
    
    // Update user profile
    const updateData = {
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      email: profileData.email,
      phoneNumber: profileData.phone,
      'preferences.language': profileData.preferredLanguage || 'English',
      'preferences.notifications.email': profileData.notificationPreferences?.emailNotifications ?? true,
      'preferences.notifications.sms': profileData.notificationPreferences?.smsNotifications ?? false,
    };
    
    const updatedUser = await User.findByIdAndUpdate(
      request.user.id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-hashedPassword -passwordResetToken -emailVerificationToken -unlockToken -twoFactor.secret -twoFactor.backupCodes');
    
    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    console.error('Update user profile API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
