import WebSocket, { WebSocketServer } from 'ws';
import http from 'http';

// Create HTTP server
const server = http.createServer();

// Create WebSocket server
const wss = new WebSocketServer({ 
  server,
  path: '/ws'
});

// Store connected clients and their info
const clients = new Map();
const onlineUsers = new Set();

// Broadcast to all connected clients
function broadcast(message, excludeClient = null) {
  const messageStr = JSON.stringify(message);
  wss.clients.forEach(client => {
    if (client !== excludeClient && client.readyState === WebSocket.OPEN) {
      client.send(messageStr);
    }
  });
}

// Send user status updates
function broadcastUserStatus() {
  const message = {
    type: 'user_status',
    data: {
      users: Array.from(onlineUsers),
      timestamp: new Date().toISOString()
    },
    timestamp: new Date().toISOString()
  };
  broadcast(message);
}

// Handle new connections
wss.on('connection', (ws, req) => {
  console.log('New WebSocket connection from:', req.socket.remoteAddress);
  
  let clientInfo = {
    id: null,
    userId: null,
    role: null,
    sessionId: null,
    lastActivity: new Date()
  };

  // Handle messages from client
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('Received message:', message.type, message.data);

      switch (message.type) {
        case 'auth':
          // Authenticate user
          clientInfo.userId = message.data.userId;
          clientInfo.role = message.data.role;
          clientInfo.sessionId = message.data.sessionId;
          clientInfo.id = `${message.data.userId}_${Date.now()}`;
          
          clients.set(ws, clientInfo);
          onlineUsers.add(message.data.userId);
          
          console.log(`User ${message.data.userId} authenticated with role ${message.data.role}`);
          
          // Send authentication confirmation
          ws.send(JSON.stringify({
            type: 'connection',
            data: { status: 'authenticated', userId: message.data.userId },
            timestamp: new Date().toISOString()
          }));
          
          // Broadcast updated user status
          broadcastUserStatus();
          break;

        case 'ping':
          // Respond to heartbeat
          ws.send(JSON.stringify({
            type: 'connection',
            data: { type: 'pong', timestamp: message.data.timestamp },
            timestamp: new Date().toISOString()
          }));
          
          // Update last activity
          if (clientInfo.userId) {
            clientInfo.lastActivity = new Date();
          }
          break;

        case 'user_status':
          // Update user online status
          if (clientInfo.userId) {
            if (message.data.isOnline) {
              onlineUsers.add(clientInfo.userId);
            } else {
              onlineUsers.delete(clientInfo.userId);
            }
            broadcastUserStatus();
          }
          break;

        case 'typing':
          // Broadcast typing indicator
          if (clientInfo.userId) {
            broadcast({
              type: 'typing',
              data: {
                conversationId: message.data.conversationId,
                isTyping: message.data.isTyping
              },
              userId: clientInfo.userId,
              timestamp: new Date().toISOString()
            }, ws);
          }
          break;

        case 'message':
          // Broadcast new message
          if (clientInfo.userId) {
            broadcast({
              type: 'message',
              data: {
                conversationId: message.data.conversationId,
                content: message.data.content,
                messageType: message.data.messageType || 'text',
                attachments: message.data.attachments || []
              },
              userId: clientInfo.userId,
              timestamp: new Date().toISOString()
            }, ws);
          }
          break;

        case 'notification':
          // Send notification to specific user or broadcast
          const notification = {
            type: 'notification',
            data: {
              _id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              title: message.data.title,
              message: message.data.message,
              type: message.data.notificationType || 'info',
              priority: message.data.priority || 'normal',
              status: 'unread',
              createdAt: new Date().toISOString(),
              actionUrl: message.data.actionUrl,
              isRealTime: true
            },
            timestamp: new Date().toISOString()
          };

          if (message.data.targetUserId) {
            // Send to specific user
            clients.forEach((info, client) => {
              if (info.userId === message.data.targetUserId && client.readyState === WebSocket.OPEN) {
                client.send(JSON.stringify(notification));
              }
            });
          } else {
            // Broadcast to all
            broadcast(notification);
          }
          break;

        default:
          console.log('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });

  // Handle connection close
  ws.on('close', () => {
    console.log('Client disconnected');
    
    if (clientInfo.userId) {
      onlineUsers.delete(clientInfo.userId);
      broadcastUserStatus();
    }
    
    clients.delete(ws);
  });

  // Handle errors
  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });

  // Send initial connection message
  ws.send(JSON.stringify({
    type: 'connection',
    data: { status: 'connected' },
    timestamp: new Date().toISOString()
  }));
});

// Simulate periodic notifications for testing
setInterval(() => {
  if (wss.clients.size > 0) {
    const notifications = [
      {
        title: 'New Report Submitted',
        message: 'A new whistleblower report has been submitted and requires review.',
        notificationType: 'report',
        priority: 'high'
      },
      {
        title: 'System Maintenance',
        message: 'Scheduled maintenance will begin in 30 minutes.',
        notificationType: 'system',
        priority: 'normal'
      },
      {
        title: 'Investigation Update',
        message: 'Case #WB-2025-0428 has been updated with new findings.',
        notificationType: 'investigation',
        priority: 'high'
      }
    ];

    const randomNotification = notifications[Math.floor(Math.random() * notifications.length)];
    
    broadcast({
      type: 'notification',
      data: {
        _id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...randomNotification,
        status: 'unread',
        createdAt: new Date().toISOString(),
        isRealTime: true
      },
      timestamp: new Date().toISOString()
    });
  }
}, 60000); // Send a test notification every minute

// Clean up inactive users
setInterval(() => {
  const now = new Date();
  const inactiveThreshold = 5 * 60 * 1000; // 5 minutes

  clients.forEach((info, client) => {
    if (info.userId && (now - info.lastActivity) > inactiveThreshold) {
      console.log(`Removing inactive user: ${info.userId}`);
      onlineUsers.delete(info.userId);
      clients.delete(client);
      
      if (client.readyState === WebSocket.OPEN) {
        client.close(1000, 'Inactive');
      }
    }
  });
  
  broadcastUserStatus();
}, 30000); // Check every 30 seconds

// Start server
const PORT = process.env.WS_PORT || 3001;
server.listen(PORT, () => {
  console.log(`WebSocket server running on port ${PORT}`);
  console.log(`WebSocket endpoint: ws://localhost:${PORT}/ws`);
});

// Handle server shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down WebSocket server...');
  wss.close(() => {
    server.close(() => {
      console.log('WebSocket server closed');
      process.exit(0);
    });
  });
});

process.on('SIGINT', () => {
  console.log('Shutting down WebSocket server...');
  wss.close(() => {
    server.close(() => {
      console.log('WebSocket server closed');
      process.exit(0);
    });
  });
});