import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    // Get recent activity based on user role
    const userId = request.user!.role === 'whistleblower' ? request.user!.id : undefined;
    const companyId = request.user!.companyId;
    
    const activities = await DataService.getRecentActivity(userId, companyId, limit);
    
    return NextResponse.json({
      success: true,
      data: activities,
      pagination: {
        limit,
        offset,
        hasMore: activities.length === limit
      }
    });
  } catch (error) {
    console.error('Recent Activity GET API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});