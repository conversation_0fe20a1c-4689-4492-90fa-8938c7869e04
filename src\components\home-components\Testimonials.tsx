"use client";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ArrowLeftIcon, ArrowRightIcon } from "lucide-react";
import { Inter, Sen } from "next/font/google";
import { useState, useEffect, useCallback } from "react";
import { testimonials } from "@/lib/mockData";

const inter = Inter({
    weight: ["400", "600"],
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-inter'
});

const sen = Sen({
    weight: ["400", "600"],
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-sen'
});

const interFontClass = `font-[${inter.variable}]`;
const senFontClass = `font-[${sen.variable}]`;

export default function Testimonials() {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [autoSwipe, setAutoSwipe] = useState(true);
    const [direction, setDirection] = useState('');
    const [isAnimating, setIsAnimating] = useState(false);

    const handleSwipe = useCallback((dir: 'left' | 'right') => {
        if (isAnimating) return;
        setIsAnimating(true);
        setDirection(dir);
        setTimeout(() => {
            setCurrentIndex((prevIndex) => 
                dir === 'left' 
                    ? (prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1)
                    : (prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1)
            );
            setDirection('');
            setIsAnimating(false);
        }, 500);
    }, [isAnimating]);

    const swipeLeft = useCallback(() => handleSwipe('left'), [handleSwipe]);
    const swipeRight = useCallback(() => handleSwipe('right'), [handleSwipe]);

    useEffect(() => {
        let interval: NodeJS.Timeout;
        
        if (autoSwipe && !isAnimating) {
            interval = setInterval(swipeLeft, 3000);
        }

        return () => interval && clearInterval(interval);
    }, [autoSwipe, isAnimating, swipeLeft]);

    return (
        <div className={`flex flex-col xl:flex-row justify-between gap-8 xl:gap-16 text-[#232536] w-80 md:w-160 lg:w-220 xl:w-350 bg-[#FBF6EA] mx-auto mt-80 md:mt-90 lg:mt-100 xl:mt-75 p-4 lg:p-20 xl:p-25}`}
             onMouseEnter={() => setAutoSwipe(false)}
             onMouseLeave={() => setAutoSwipe(true)}>
            <div className="w-full xl:w-1/3 flex flex-col gap-4 p-2">
                <p className={`font-semibold text-sm sm:text-base ${interFontClass}`}>TESTIMONIALS</p>
                <p className="font-bold text-2xl md:text-3xl xl:text-4xl">What people say about our Product</p>
                <p className={`font-normal text-sm sm:text-base ${interFontClass}`}>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.</p>
            </div>
            <div className="border-1 border-black/20 mx-5 lg:mx-10 xl:mx-15"></div>
            <div className="w-full xl:w-2/3 flex flex-col justify-between gap-10 xl:gap-20 pr-4 xl:pr-30 pt-2 pb-2 pl-2">
                <div className="overflow-hidden flex flex-col justify-between">
                    <p className={`font-bold text-xl sm:text-2xl tracking-wide pr-4 xl:pr-10 transition-all duration-500 ease-in-out ${
                        direction === 'left' ? 'opacity-0 translate-x-[-200px]' : 
                        direction === 'right' ? 'opacity-0 translate-x-[200px]' : 'opacity-100 translate-x-0'
                    }`}>&ldquo;{testimonials[currentIndex].quote}&rdquo;</p>
                    <div className={`flex flex-col sm:flex-row items-center sm:items-start gap-4 sm:gap-2 mt-10 xl:mt-20 transition-all duration-500 ease-in-out ${
                        direction ? 'opacity-0' : 'opacity-100'
                    }`}>
                        <Avatar className="h-8 w-8 md:h-12 md:w-12">
                            <AvatarImage src={testimonials[currentIndex].avatar} alt={`${testimonials[currentIndex].author} picture`} className="h-8 w-8 md:h-12 md:w-12" />
                            <AvatarFallback>{testimonials[currentIndex].author.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                        </Avatar>
                        <div className="w-full flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-0">
                            <div className="flex flex-col items-center sm:items-start">
                                <p className={`font-bold text-center sm:text-left text-xl sm:text-2xl ${senFontClass}`}>{testimonials[currentIndex].author}</p>
                                <p className={`text-sm sm:text-base text-center sm:text-left font-normal ${interFontClass}`}>{testimonials[currentIndex].role}</p>
                            </div>
                            <div className="flex gap-4">
                                <ArrowLeftIcon onClick={swipeRight} className="h-8 w-8 sm:h-10 sm:w-10 bg-white text-[#232536] hover:bg-[#1E4841] hover:text-white rounded-full p-1 cursor-pointer transition-colors duration-300" />
                                <ArrowRightIcon onClick={swipeLeft} className="h-8 w-8 sm:h-10 sm:w-10 bg-white text-[#232536] hover:bg-[#1E4841] hover:text-white rounded-full p-1 cursor-pointer transition-colors duration-300" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}