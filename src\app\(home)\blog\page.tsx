"use client";
import Footer from "@/components/home-components/shared/Footer";
import Header from "@/components/home-components/shared/Header";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ArrowRight, Search, X, Calendar as CalendarIcon, Filter } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState, useMemo } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
    Pagination,
    PaginationContent,
    PaginationItem,
    PaginationLink,
    PaginationPrevious,
    PaginationNext
} from "@/components/ui/pagination";
import { Label } from "@/components/ui/label";
import { FeaturedBlogCard, CategoryBlogCard } from "@/components/home-components/blog/BlogCard";

import { ALL_BLOG_POSTS, getFeaturedPosts } from "@/lib/mockData";

const CATEGORIES = [
    "All",
    "Compliance Updates",
    "Best Practices",
    "Case Studies",
    "Industry Trends",
    "Company News"
];
const TOPICS = [
    "Whistleblower Protection",
    "Regulatory Compliance",
    "Ethics & Governance",
    "Risk Management",
    "Corporate Culture"
];
const AUTHORS = [
    "Dr. Elizabeth Morgan",
    "James Chen, CCEP",
    "Olivia Washington",
    "Michael Reeves",
    "Sofia Rodriguez"
];
const CONTENT_TYPES = [
    "Articles",
    "Whitepapers",
    "Videos",
    "Podcasts",
    "Infographics"
];

export default function BlogPage() {
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedCategory, setSelectedCategory] = useState("All");
    const [selectedTopics, setSelectedTopics] = useState<string[]>([TOPICS[0]]);
    const [selectedAuthors, setSelectedAuthors] = useState<string[]>(["All Authors"]);
    const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>([CONTENT_TYPES[0]]);
    const [dateFrom, setDateFrom] = useState("");
    const [dateTo, setDateTo] = useState("");
    const [page, setPage] = useState(1);
    const pageSize = 6;

    // Get featured posts for the Featured Articles section
    const featuredPosts = getFeaturedPosts();

    // Use ALL_BLOG_POSTS for Browse by Category section
    const allBlogPosts = ALL_BLOG_POSTS;

    // State to track applied filters (only updated when Apply button is clicked)
    const [appliedFilters, setAppliedFilters] = useState({
        searchQuery: "",
        selectedCategory: "All",
        selectedTopics: [TOPICS[0]],
        selectedAuthors: <AUTHORS>
        selectedContentTypes: [CONTENT_TYPES[0]],
        dateFrom: "",
        dateTo: ""
    });

    // Apply filters function
    const applyFilters = () => {
        setAppliedFilters({
            searchQuery,
            selectedCategory,
            selectedTopics,
            selectedAuthors,
            selectedContentTypes,
            dateFrom,
            dateTo
        });
        setPage(1);
    };

    // Helper function to parse date strings
    const parseDate = (dateStr: string) => {
        if (!dateStr) return null;
        return new Date(dateStr);
    };

    // Helper function to parse blog post dates
    const parseBlogDate = (dateStr: string) => {
        const months: Record<string, number> = {
            'January': 0, 'February': 1, 'March': 2, 'April': 3, 'May': 4, 'June': 5,
            'July': 6, 'August': 7, 'September': 8, 'October': 9, 'November': 10, 'December': 11
        };

        const parts = dateStr.split(' ');
        if (parts.length === 3) {
            const month = months[parts[0]];
            const day = parseInt(parts[1].replace(',', ''));
            const year = parseInt(parts[2]);
            return new Date(year, month, day);
        }
        return null;
    };

    // Comprehensive filtering logic
    const filteredCards = useMemo(() => {
        const fromDate = parseDate(appliedFilters.dateFrom);
        const toDate = parseDate(appliedFilters.dateTo);

        const cards = allBlogPosts.filter(card => {
            // Category filter
            const categoryMatch = appliedFilters.selectedCategory === "All" || card.category === appliedFilters.selectedCategory;
            if (!categoryMatch) return false;

            // Author filter
            const authorMatch = appliedFilters.selectedAuthors.includes("All Authors") || appliedFilters.selectedAuthors.includes(card.author.name);
            if (!authorMatch) return false;

            // Topic filter using tags
            const topicMatch = appliedFilters.selectedTopics.length === 0 ||
                appliedFilters.selectedTopics.includes(TOPICS[0]) ||
                appliedFilters.selectedTopics.some(selectedTopic =>
                    card.tags?.some(tag => tag.toLowerCase().includes(selectedTopic.toLowerCase()))
                );
            if (!topicMatch) return false;

            // Content type filter - all blog posts are articles by default
            const contentTypeMatch = appliedFilters.selectedContentTypes.length === 0 ||
                appliedFilters.selectedContentTypes.includes(CONTENT_TYPES[0]) ||
                appliedFilters.selectedContentTypes.includes("Articles");
            if (!contentTypeMatch) return false;

            // Date range filter
            if (fromDate || toDate) {
                const cardDate = parseBlogDate(card.date);
                if (cardDate) {
                    if (fromDate && cardDate < fromDate) return false;
                    if (toDate && cardDate > toDate) return false;
                }
            }

            // Search query filter
            const searchMatch = !appliedFilters.searchQuery ||
                card.title.toLowerCase().includes(appliedFilters.searchQuery.toLowerCase()) ||
                card.description.toLowerCase().includes(appliedFilters.searchQuery.toLowerCase()) ||
                card.author.name.toLowerCase().includes(appliedFilters.searchQuery.toLowerCase()) ||
                card.category.toLowerCase().includes(appliedFilters.searchQuery.toLowerCase());
            if (!searchMatch) return false;

            return true;
        });

        // Pagination
        const start = (page - 1) * pageSize;
        return cards.slice(start, start + pageSize);
    }, [appliedFilters.dateFrom, appliedFilters.dateTo, appliedFilters.selectedCategory, appliedFilters.selectedAuthors, appliedFilters.selectedTopics, appliedFilters.selectedContentTypes, appliedFilters.searchQuery, allBlogPosts, page]);

    // Calculate total pages based on filtered cards length before pagination
    const totalPages = useMemo(() => {
        const fromDate = parseDate(appliedFilters.dateFrom);
        const toDate = parseDate(appliedFilters.dateTo);

        const filteredCount = allBlogPosts.filter(card => {
            // Category filter
            const categoryMatch = appliedFilters.selectedCategory === "All" || card.category === appliedFilters.selectedCategory;
            if (!categoryMatch) return false;

            // Author filter
            const authorMatch = appliedFilters.selectedAuthors.includes("All Authors") || appliedFilters.selectedAuthors.includes(card.author.name);
            if (!authorMatch) return false;

            // Topic filter using tags
            const topicMatch = appliedFilters.selectedTopics.length === 0 ||
                appliedFilters.selectedTopics.includes(TOPICS[0]) ||
                appliedFilters.selectedTopics.some(selectedTopic =>
                    card.tags?.some(tag => tag.toLowerCase().includes(selectedTopic.toLowerCase()))
                );
            if (!topicMatch) return false;

            // Content type filter
            const contentTypeMatch = appliedFilters.selectedContentTypes.length === 0 ||
                appliedFilters.selectedContentTypes.includes(CONTENT_TYPES[0]) ||
                appliedFilters.selectedContentTypes.includes("Articles");
            if (!contentTypeMatch) return false;

            // Date range filter
            if (fromDate || toDate) {
                const cardDate = parseBlogDate(card.date);
                if (cardDate) {
                    if (fromDate && cardDate < fromDate) return false;
                    if (toDate && cardDate > toDate) return false;
                }
            }

            return true;
        }).length;

        return Math.max(1, Math.ceil(filteredCount / pageSize));
    }, [allBlogPosts, appliedFilters.dateFrom, appliedFilters.dateTo, appliedFilters.selectedAuthors, appliedFilters.selectedCategory, appliedFilters.selectedContentTypes, appliedFilters.selectedTopics]);

    return (
        <div className="flex min-h-screen flex-col">
            <Header />
            <main id="main-content" aria-label="Investigator Portal" className="flex-1 pt-20 bg-[#FBFBFC]">
                <section className="relative px-4 sm:px-8 md:px-12 lg:px-[180px] py-8 sm:py-12 md:py-16 lg:py-20 mx-auto bg-gradient-to-r from-[#132f2a] from-70% via-[#1E4841]/90 via-90% to-[#1E4841]/80">
                    <Image
                        src="/desktop/blog/hero.png"
                        alt="Investigator background"
                        fill
                        priority
                        className="object-cover mix-blend-luminosity opacity-15"
                    />
                    <div className="relative text-center z-10">
                        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 sm:mb-6 leading-tight animate-fade-in">
                            Insights & Perspectives
                        </h1>
                        <p className="text-base sm:text-lg md:text-xl text-white mb-8 sm:mb-10 px-2 sm:px-8 md:px-20 xl:px-50 animate-slide-up">
                            Expert analysis, industry trends, and best practices in whistleblowing, compliance, and ethical business conduct.
                        </p>
                        <div className="relative w-full max-w-xl mx-auto mb-8 animate-fade-in flex flex-col sm:flex-row">
                            <div className="relative group flex-1">
                                <Input
                                    type="text"
                                    id="searchQuery"
                                    name="searchQuery"
                                    placeholder="Search articles, topics, or authors......"
                                    value={searchQuery}
                                    onChange={e => {
                                        setSearchQuery(e.target.value);
                                    }}
                                    className="w-full px-4 py-6 pl-12 pr-10 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:border-[#1E4841] focus:ring-1 focus:ring-[#1E4841] transition-all duration-300 hover:shadow-lg"
                                    aria-label="Search articles, topics, or authors"
                                />
                                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-hover:text-[#1E4841] transition-colors duration-300" />
                                {searchQuery && (
                                    <button
                                        type="button"
                                        onClick={() => setSearchQuery('')}
                                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-300"
                                        aria-label="Clear search"
                                    >
                                        <X className="h-5 w-5" />
                                    </button>
                                )}

                                {/* Search Results Dropdown */}
                                {searchQuery.length > 0 && (
                                    <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-80 overflow-y-auto">
                                        {(() => {
                                            const filteredResults = ALL_BLOG_POSTS.filter(card =>
                                                card.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                                                card.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                                                card.author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                                                card.category.toLowerCase().includes(searchQuery.toLowerCase())
                                            ).slice(0, 5);

                                            if (filteredResults.length > 0) {
                                                return filteredResults.map((card, index) => (
                                                    <Link href={`/blog/${card.id}`} key={index} className="block hover:bg-gray-50">
                                                        <div className="p-4 border-b border-gray-100 flex justify-between">
                                                            <div className="flex-1 pr-4 text-left">
                                                                <h4 className="font-semibold text-[#1E4841] line-clamp-1">{card.title}</h4>
                                                                <p className="text-sm text-gray-600 line-clamp-1">{card.description}</p>
                                                            </div>
                                                            <div className="flex flex-col items-end text-xs text-gray-500">
                                                                <span>{card.date}</span>
                                                                <span>{card.readTime}</span>
                                                            </div>
                                                        </div>
                                                    </Link>
                                                ));
                                            } else {
                                                return (
                                                    <Link href={`/blog/search?q=${encodeURIComponent(searchQuery)}`} className="block hover:bg-gray-50">
                                                        <div className="p-4 text-center">
                                                            <p className="font-medium text-[#1E4841] mb-1">No results found</p>
                                                            <p className="text-sm text-gray-600">Click to explore more in the search page</p>
                                                        </div>
                                                    </Link>
                                                );
                                            }
                                        })()}
                                    </div>
                                )}
                            </div>

                            {/* Find Blogs button - positioned below search on mobile, to the right on larger screens */}
                            {searchQuery.length > 0 && (
                                <Link href={`/blog/search?q=${encodeURIComponent(searchQuery)}`} className="sm:ml-2 mt-2 sm:mt-0 w-full sm:w-auto">
                                    <Button
                                        variant="default"
                                        className="w-full sm:w-auto h-full px-4 py-3 text-base text-[#1E4841] bg-[#BBF49C] border border-[#BBF49C] hover:border-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm hover:shadow-lg font-semibold"
                                        aria-label="Find Blogs"
                                    >
                                        <div className="flex items-center gap-2">
                                            <p className="text-[#1E4841]">Find Blogs</p>
                                            <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                                        </div>
                                    </Button>
                                </Link>
                            )}
                        </div>

                        {/* Find Blogs button - centered when no search dropdown */}
                        {searchQuery.length === 0 && (
                            <Link href="/blog/search">
                                <Button
                                    variant="default"
                                    className="px-4 py-3 sm:px-6 sm:py-4 md:px-9 md:py-7 text-base sm:text-lg md:text-xl text-[#1E4841] bg-[#BBF49C] border border-[#BBF49C] hover:border-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm hover:shadow-lg font-semibold transform hover:scale-105 animate-fade-in focus:ring-2 focus:ring-offset-2 focus:ring-[#1E4841]"
                                    aria-label="Find Blogs"
                                >
                                    <div className="flex items-center gap-2">
                                        <p className="text-[#1E4841]">Find Blogs</p>
                                        <ArrowRight className="h-4 w-4 sm:h-5 sm:w-5 transition-transform group-hover:translate-x-1" />
                                    </div>
                                </Button>
                            </Link>
                        )}
                    </div>
                </section>
                <section className="px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[180px] py-8 sm:py-10 md:py-15 mx-auto">
                    <p className="text-[#1E4841] text-xl sm:text-2xl md:text-[28px] font-bold">Featured Articles</p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 pt-6 sm:pt-8 md:pt-10">
                        {featuredPosts.map((card, index) => (
                            <FeaturedBlogCard key={index} card={card} />
                        ))}
                    </div>
                </section>
                <section className="px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[180px] py-8 sm:py-10 md:py-15 mx-auto">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-2">
                        <p className="text-[#1E4841] text-xl sm:text-2xl md:text-[28px] font-bold">Browse by Category</p>
                        <p className="text-[#1E4841] text-sm sm:text-base font-semibold cursor-pointer hover:underline">View All Categories</p>
                    </div>
                    {/* Category Tabs */}
                    <Tabs value={selectedCategory} onValueChange={cat => {
                        setSelectedCategory(cat);
                        // Apply category filter immediately
                        setAppliedFilters(prev => ({
                            ...prev,
                            selectedCategory: cat
                        }));
                    }} className="w-full mb-6 sm:mb-8 overflow-x-auto">
                        <TabsList className="flex gap-1 sm:gap-2 bg-white rounded-md px-1 py-3 sm:py-5 shadow-sm border border-[#F3F4F6] w-max min-w-full">
                            {CATEGORIES.map(cat => (
                                <TabsTrigger
                                    key={cat}
                                    value={cat}
                                    className={`px-3 py-2 sm:px-4 md:px-6 sm:py-3 md:py-4 rounded-md font-semibold text-xs sm:text-sm md:text-base transition-all duration-200 ${selectedCategory === cat ? 'data-[state=active]:bg-[#ECF4E9] data-[state=active]:text-[#1E4841]' : 'bg-white text-[#1E4841]'}`}
                                >
                                    {cat}
                                </TabsTrigger>
                            ))}
                        </TabsList>
                    </Tabs>
                    <div className="flex flex-col lg:flex-row gap-6 sm:gap-8">
                        {/* Filter Button for Mobile/Tablet */}
                        <div className="lg:hidden mb-4">
                            <Dialog>
                                <DialogTrigger asChild>
                                    <Button variant="outline" className="flex items-center gap-2 w-full justify-center">
                                        <Filter className="h-4 w-4" />
                                        <span>Filter Articles</span>
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto bg-white">
                                    <DialogHeader>
                                        <DialogTitle className="text-lg font-bold text-[#1E4841]">Filter Articles</DialogTitle>
                                        <DialogDescription>
                                            Customize your article filters below
                                        </DialogDescription>
                                    </DialogHeader>
                                    <div className="space-y-4">
                                        <form className="flex flex-col gap-4" onSubmit={(e) => e.preventDefault()}>
                                            <div>
                                                <Label htmlFor="dateRange" className="block text-[#1E4841] font-semibold mb-2">Date Range</Label>
                                                <div className="flex flex-col gap-4">
                                                    <div>
                                                        <Label htmlFor="dateFrom" className="block text-[#1E4841] font-semibold mb-2">From</Label>
                                                        <Popover>
                                                            <PopoverTrigger asChild>
                                                                <Button
                                                                    id="dateFrom"
                                                                    variant={"outline"}
                                                                    className={cn(
                                                                        "w-full pl-3 text-left font-normal",
                                                                        !dateFrom && "text-muted-foreground"
                                                                    )}
                                                                >
                                                                    {dateFrom ? (
                                                                        format(new Date(dateFrom), "dd/MM/yyyy")
                                                                    ) : (
                                                                        <span>DD/MM/YYYY</span>
                                                                    )}
                                                                    <CalendarIcon className="ml-auto h-4 w-4" />
                                                                </Button>
                                                            </PopoverTrigger>
                                                            <PopoverContent className="w-auto p-0 bg-white" align="start">
                                                                <Calendar
                                                                    mode="single"
                                                                    selected={dateFrom ? new Date(dateFrom) : undefined}
                                                                    onSelect={(date) => setDateFrom(date ? date.toISOString() : "")}
                                                                    captionLayout="dropdown"
                                                                />
                                                            </PopoverContent>
                                                        </Popover>
                                                    </div>

                                                    <div>
                                                        <Label htmlFor="dateTo" className="block text-[#1E4841] font-semibold mb-2">To</Label>
                                                        <Popover>
                                                            <PopoverTrigger asChild>
                                                                <Button
                                                                    id="dateTo"
                                                                    variant={"outline"}
                                                                    className={cn(
                                                                        "w-full pl-3 text-left font-normal",
                                                                        !dateTo && "text-muted-foreground"
                                                                    )}
                                                                >
                                                                    {dateTo ? (
                                                                        format(new Date(dateTo), "dd/MM/yyyy")
                                                                    ) : (
                                                                        <span>DD/MM/YYYY</span>
                                                                    )}
                                                                    <CalendarIcon className="ml-auto h-4 w-4" />
                                                                </Button>
                                                            </PopoverTrigger>
                                                            <PopoverContent className="w-auto p-0 bg-white" align="start">
                                                                <Calendar
                                                                    mode="single"
                                                                    selected={dateTo ? new Date(dateTo) : undefined}
                                                                    onSelect={(date) => setDateTo(date ? date.toISOString() : "")}
                                                                    captionLayout="dropdown"
                                                                />
                                                            </PopoverContent>
                                                        </Popover>
                                                    </div>
                                                </div>
                                            </div>
                                            <Separator />
                                            <div>
                                                <Label htmlFor="topic-Whistleblower Protection" className="block text-[#1E4841] font-semibold mb-2">Topics</Label>
                                                <div className="flex flex-col gap-2">
                                                    {TOPICS.map(topic => (
                                                        <label key={topic} className="flex items-center gap-2">
                                                            <Checkbox
                                                                id={`topic-${topic}`}
                                                                name={`topic-${topic}`}
                                                                checked={selectedTopics.includes(topic)}
                                                                onCheckedChange={() => setSelectedTopics(selectedTopics.includes(topic) ? selectedTopics.filter(t => t !== topic) : [...selectedTopics, topic])}
                                                            />
                                                            <span>{topic}</span>
                                                        </label>
                                                    ))}
                                                </div>
                                            </div>
                                            <Separator />
                                            <div>
                                                <Label htmlFor="author-all" className="block text-[#1E4841] font-semibold mb-2">Authors</Label>
                                                <RadioGroup value={selectedAuthors[0]} onValueChange={val => setSelectedAuthors([val])}>
                                                    <label className="flex items-center gap-2">
                                                        <RadioGroupItem id="author-all" value="All Authors" />
                                                        <span>All Authors</span>
                                                    </label>
                                                    {AUTHORS.map(author => (
                                                        <label key={author} className="flex items-center gap-2">
                                                            <RadioGroupItem id={`author-${author}`} value={author} />
                                                            <span>{author}</span>
                                                        </label>
                                                    ))}
                                                </RadioGroup>
                                            </div>
                                            <Separator />
                                            <div>
                                                <Label htmlFor="content-Articles" className="block text-[#1E4841] font-semibold mb-2">Content Type</Label>
                                                <div className="flex flex-col gap-2">
                                                    {CONTENT_TYPES.map(type => (
                                                        <label key={type} className="flex items-center gap-2">
                                                            <Checkbox
                                                                id={`content-${type}`}
                                                                name={`content-${type}`}
                                                                checked={selectedContentTypes.includes(type)}
                                                                onCheckedChange={() => setSelectedContentTypes(selectedContentTypes.includes(type) ? selectedContentTypes.filter(t => t !== type) : [...selectedContentTypes, type])}
                                                            />
                                                            <span>{type}</span>
                                                        </label>
                                                    ))}
                                                </div>
                                            </div>
                                            <Separator />
                                            <div className="flex gap-2">
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    onClick={() => {
                                                        setSelectedTopics([TOPICS[0]]);
                                                        setSelectedAuthors(["All Authors"]);
                                                        setSelectedContentTypes([CONTENT_TYPES[0]]);
                                                        setDateFrom("");
                                                        setDateTo("");
                                                        setSearchQuery("");
                                                        setPage(1);
                                                        setAppliedFilters({
                                                            searchQuery: "",
                                                            selectedCategory: "All",
                                                            selectedTopics: [TOPICS[0]],
                                                            selectedAuthors: <AUTHORS>
                                                            selectedContentTypes: [CONTENT_TYPES[0]],
                                                            dateFrom: "",
                                                            dateTo: ""
                                                        });
                                                    }}
                                                    className="flex-1 text-[#1E4841] hover:text-gray-900 hover:bg-[#BBF49C50] transition-colors duration-300 group font-semibold"
                                                    aria-label="Clear All Filters"
                                                >
                                                    Clear All Filters
                                                </Button>
                                                <Button
                                                    type="button"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        applyFilters();
                                                    }}
                                                    variant="default"
                                                    className="flex-1 text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 font-semibold"
                                                    aria-label="Apply Filters"
                                                >
                                                    Apply
                                                </Button>
                                            </div>
                                        </form>
                                    </div>
                                </DialogContent>
                            </Dialog>                        </div>

                        {/* Desktop Filter Sidebar */}
                        <aside className="hidden lg:block w-72 bg-white rounded-xl p-4 sm:p-6 shadow-md flex-shrink-0">
                            <form className="flex flex-col gap-4" onSubmit={(e) => e.preventDefault()}>
                                <p className="text-[#1E4841] text-lg font-bold">Filter Articles</p>
                                <div>
                                    <Label htmlFor="dateRange" className="block text-[#1E4841] font-semibold mb-2">Date Range</Label>
                                    <div className="flex flex-col gap-4">
                                        <div>
                                            <Label htmlFor="dateFrom" className="block text-[#1E4841] font-semibold mb-2">From</Label>
                                            <Popover>
                                                <PopoverTrigger asChild>
                                                    <Button
                                                        id="dateFrom"
                                                        variant={"outline"}
                                                        className={cn(
                                                            "w-full pl-3 text-left font-normal",
                                                            !dateFrom && "text-muted-foreground"
                                                        )}
                                                    >
                                                        {dateFrom ? (
                                                            format(new Date(dateFrom), "dd/MM/yyyy")
                                                        ) : (
                                                            <span>DD/MM/YYYY</span>
                                                        )}
                                                        <CalendarIcon className="ml-auto h-4 w-4" />
                                                    </Button>
                                                </PopoverTrigger>
                                                <PopoverContent className="w-auto p-0 bg-white" align="start">
                                                    <Calendar
                                                        mode="single"
                                                        selected={dateFrom ? new Date(dateFrom) : undefined}
                                                        onSelect={(date) => setDateFrom(date ? date.toISOString() : "")}
                                                        captionLayout="dropdown"
                                                    />
                                                </PopoverContent>
                                            </Popover>
                                        </div>

                                        <div>
                                            <Label htmlFor="dateTo" className="block text-[#1E4841] font-semibold mb-2">To</Label>
                                            <Popover>
                                                <PopoverTrigger asChild>
                                                    <Button
                                                        id="dateTo"
                                                        variant={"outline"}
                                                        className={cn(
                                                            "w-full pl-3 text-left font-normal",
                                                            !dateTo && "text-muted-foreground"
                                                        )}
                                                    >
                                                        {dateTo ? (
                                                            format(new Date(dateTo), "dd/MM/yyyy")
                                                        ) : (
                                                            <span>DD/MM/YYYY</span>
                                                        )}
                                                        <CalendarIcon className="ml-auto h-4 w-4" />
                                                    </Button>
                                                </PopoverTrigger>
                                                <PopoverContent className="w-auto p-0 bg-white" align="start">
                                                    <Calendar
                                                        mode="single"
                                                        selected={dateTo ? new Date(dateTo) : undefined}
                                                        onSelect={(date) => setDateTo(date ? date.toISOString() : "")}
                                                        captionLayout="dropdown"
                                                    />
                                                </PopoverContent>
                                            </Popover>
                                        </div>
                                    </div>
                                </div>
                                <Separator />
                                <div>
                                    <Label htmlFor="topic-Whistleblower Protection" className="block text-[#1E4841] font-semibold mb-2">Topics</Label>
                                    <div className="flex flex-col gap-2">
                                        {TOPICS.map(topic => (
                                            <label key={topic} className="flex items-center gap-2">
                                                <Checkbox
                                                    id={`topic-${topic}`}
                                                    name={`topic-${topic}`}
                                                    checked={selectedTopics.includes(topic)}
                                                    onCheckedChange={() => setSelectedTopics(selectedTopics.includes(topic) ? selectedTopics.filter(t => t !== topic) : [...selectedTopics, topic])}
                                                />
                                                <span>{topic}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                <Separator />
                                <div>
                                    <Label htmlFor="author-all" className="block text-[#1E4841] font-semibold mb-2">Authors</Label>
                                    <RadioGroup value={selectedAuthors[0]} onValueChange={val => setSelectedAuthors([val])}>
                                        <label className="flex items-center gap-2">
                                            <RadioGroupItem id="author-all" value="All Authors" />
                                            <span>All Authors</span>
                                        </label>
                                        {AUTHORS.map(author => (
                                            <label key={author} className="flex items-center gap-2">
                                                <RadioGroupItem id={`author-${author}`} value={author} />
                                                <span>{author}</span>
                                            </label>
                                        ))}
                                    </RadioGroup>
                                </div>
                                <Separator />
                                <div>
                                    <Label htmlFor="content-Articles" className="block text-[#1E4841] font-semibold mb-2">Content Type</Label>
                                    <div className="flex flex-col gap-2">
                                        {CONTENT_TYPES.map(type => (
                                            <label key={type} className="flex items-center gap-2">
                                                <Checkbox
                                                    id={`content-${type}`}
                                                    name={`content-${type}`}
                                                    checked={selectedContentTypes.includes(type)}
                                                    onCheckedChange={() => setSelectedContentTypes(selectedContentTypes.includes(type) ? selectedContentTypes.filter(t => t !== type) : [...selectedContentTypes, type])}
                                                />
                                                <span>{type}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                                <Separator />
                                <div className="flex gap-2">
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        onClick={() => {
                                            setSelectedTopics([TOPICS[0]]);
                                            setSelectedAuthors(["All Authors"]);
                                            setSelectedContentTypes([CONTENT_TYPES[0]]);
                                            setDateFrom("");
                                            setDateTo("");
                                            setSearchQuery("");
                                            setPage(1);
                                            setAppliedFilters({
                                                searchQuery: "",
                                                selectedCategory: "All",
                                                selectedTopics: [TOPICS[0]],
                                                selectedAuthors: <AUTHORS>
                                                selectedContentTypes: [CONTENT_TYPES[0]],
                                                dateFrom: "",
                                                dateTo: ""
                                            });
                                        }}
                                        className="flex-1 text-[#1E4841] hover:text-gray-900 hover:bg-[#BBF49C50] transition-colors duration-300 group font-semibold"
                                        aria-label="Clear All Filters"
                                    >
                                        Clear All Filters
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            applyFilters();
                                        }}
                                        variant="default"
                                        className="flex-1 text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 font-semibold"
                                        aria-label="Apply Filters"
                                    >
                                        Apply
                                    </Button>
                                </div>
                            </form>
                        </aside>
                        <div className="flex flex-col items-center w-full">
                            {filteredCards.length > 0 ? (
                                <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                                    {filteredCards.map((card, index) => (
                                        <CategoryBlogCard key={index} card={card} />
                                    ))}
                                </div>
                            ) : (
                                <div className="flex flex-col items-center justify-center py-16 w-full">
                                    <p className="text-xl text-[#1E4841] font-semibold mb-2">No articles found</p>
                                    <p className="text-gray-500">Try adjusting your filters or search criteria</p>
                                </div>
                            )}
                            {/* Pagination - only show when there are results */}
                            {filteredCards.length > 0 && totalPages > 0 && (
                                <Pagination className="mt-8">
                                    <PaginationContent>
                                        <PaginationItem>
                                            <PaginationPrevious
                                                className="bg-[#ECF4E9] text-[#1E4841] font-semibold disabled:opacity-50 py-2"
                                                onClick={() => page > 1 && setPage(page - 1)}
                                                aria-disabled={page === 1}
                                                tabIndex={page === 1 ? -1 : 0}
                                            />
                                        </PaginationItem>
                                        {[...Array(totalPages)].map((_, i) => (
                                            <PaginationItem key={i}>
                                                <PaginationLink
                                                    isActive={page === i + 1}
                                                    className={`${page === i + 1 ? 'bg-[#1E4841] text-white' : 'bg-white text-[#1E4841] border border-[#ECF4E9]'} w-10 h-10`}
                                                    onClick={() => setPage(i + 1)}
                                                >
                                                    <span>{i + 1}</span>
                                                </PaginationLink>
                                            </PaginationItem>
                                        ))}
                                        <PaginationItem>
                                            <PaginationNext
                                                className="bg-[#ECF4E9] text-[#1E4841] font-semibold disabled:opacity-50 py-2"
                                                onClick={() => page < totalPages && setPage(page + 1)}
                                                aria-disabled={page === totalPages}
                                                tabIndex={page === totalPages ? -1 : 0}
                                            />
                                        </PaginationItem>
                                    </PaginationContent>
                                </Pagination>
                            )}
                        </div>
                    </div>
                </section>
            </main>
            <Footer />
        </div>
    );
}