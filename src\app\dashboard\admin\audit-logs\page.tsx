"use client";

import { useEffect } from "react";
import Header from "@/components/dashboard-components/Header";
import { notificationSystem } from "@/lib/utils/notificationSystem";
import { notificationsData } from "@/lib/mockData/notificationData";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FileText, User, Shield, AlertTriangle, Search } from "lucide-react";
import { Input } from "@/components/ui/input";

export default function AuditLogsPage() {
    // Initialize notification system
    useEffect(() => {
        notificationSystem.updateNotifications(notificationsData);
    }, []);

    const auditLogs = [
        {
            id: 1,
            timestamp: "2025-06-03 14:30:25",
            user: "<PERSON>",
            action: "Case Assignment",
            details: "Assigned case #WB-2025-089 to <PERSON>",
            type: "assignment",
            ipAddress: "*************"
        },
        {
            id: 2,
            timestamp: "2025-06-03 13:45:12",
            user: "<PERSON>",
            action: "Case Update",
            details: "Updated status of case #WB-2025-087 to In Progress",
            type: "update",
            ipAddress: "*************"
        },
        {
            id: 3,
            timestamp: "2025-06-03 12:20:08",
            user: "System",
            action: "SLA Breach",
            details: "Case #WB-2025-076 exceeded SLA deadline",
            type: "alert",
            ipAddress: "System"
        },
        {
            id: 4,
            timestamp: "2025-06-03 11:15:33",
            user: "Katherine Lee",
            action: "Case Closure",
            details: "Closed case #WB-2025-071 with resolution",
            type: "closure",
            ipAddress: "*************"
        },
        {
            id: 5,
            timestamp: "2025-06-03 10:30:45",
            user: "Admin",
            action: "User Login",
            details: "Emily Johnson logged into admin dashboard",
            type: "login",
            ipAddress: "*************"
        }
    ];

    const getActionIcon = (type: string) => {
        switch (type) {
            case 'assignment':
                return User;
            case 'update':
                return FileText;
            case 'alert':
                return AlertTriangle;
            case 'closure':
                return FileText;
            case 'login':
                return Shield;
            default:
                return FileText;
        }
    };

    const getActionColor = (type: string) => {
        switch (type) {
            case 'assignment':
                return 'bg-blue-100 text-blue-800';
            case 'update':
                return 'bg-green-100 text-green-800';
            case 'alert':
                return 'bg-red-100 text-red-800';
            case 'closure':
                return 'bg-gray-100 text-gray-800';
            case 'login':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Audit Logs"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900 mb-2">Audit Logs</h1>
                                <p className="text-gray-600">Track all system activities and user actions</p>
                            </div>
                            <div className="flex items-center gap-3">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                    <Input 
                                        placeholder="Search logs..." 
                                        className="pl-10 w-64"
                                    />
                                </div>
                                <Button variant="outline" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                                    Export Logs
                                </Button>
                            </div>
                        </div>
                    </div>

                    {/* Audit Statistics */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Total Actions</CardTitle>
                                <FileText className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">1,247</div>
                                <p className="text-xs text-gray-500">Last 30 days</p>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">User Logins</CardTitle>
                                <Shield className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">342</div>
                                <p className="text-xs text-gray-500">Last 30 days</p>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Case Actions</CardTitle>
                                <User className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">856</div>
                                <p className="text-xs text-gray-500">Last 30 days</p>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">System Alerts</CardTitle>
                                <AlertTriangle className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">49</div>
                                <p className="text-xs text-gray-500">Last 30 days</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Audit Logs Table */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold text-gray-900">
                                Recent Activity
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b border-gray-200">
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Timestamp</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">User</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Action</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Details</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">IP Address</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {auditLogs.map((log) => {
                                            const Icon = getActionIcon(log.type);
                                            return (
                                                <tr key={log.id} className="border-b border-gray-100 hover:bg-gray-50">
                                                    <td className="py-3 px-2">
                                                        <span className="text-sm text-gray-900 font-mono">
                                                            {log.timestamp}
                                                        </span>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <div className="flex items-center gap-2">
                                                            <Icon className="h-4 w-4 text-gray-400" />
                                                            <span className="text-sm text-gray-900">{log.user}</span>
                                                        </div>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <Badge variant="secondary" className={`text-xs ${getActionColor(log.type)}`}>
                                                            {log.action}
                                                        </Badge>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <span className="text-sm text-gray-600">{log.details}</span>
                                                    </td>
                                                    <td className="py-3 px-2">
                                                        <span className="text-sm text-gray-500 font-mono">{log.ipAddress}</span>
                                                    </td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                </main>
            </div>
        </>
    );
}