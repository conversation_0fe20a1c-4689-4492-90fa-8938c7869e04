import { EmailTemplate } from './baseTemplate';

export interface ReportSubmissionData {
  reportId: string;
  title: string;
  category: string;
  priority: string;
  status: string;
  submittedAt: Date;
  submitterEmail: string;
  dashboardUrl?: string;
}

export function createReportSubmissionTemplate(data: ReportSubmissionData): { html: string; text: string } {
  const { 
    reportId, 
    title, 
    category, 
    priority, 
    status, 
    submittedAt, 
    submitterEmail,
    dashboardUrl = `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/whistleblower/my-reports`
  } = data;
  
  const priorityColor = priority === 'urgent' ? '#dc3545' : priority === 'high' ? '#fd7e14' : '#28a745';
  
  const content = `
    <h2 style="color: #1E4841; margin-bottom: 20px;">Report Submitted Successfully</h2>
    
    <p style="margin-bottom: 30px;">
      Your report has been successfully submitted to our secure system. Here are the details:
    </p>
    
    ${EmailTemplate.createCard(`
      <strong>Report ID:</strong> ${reportId}<br>
      <strong>Title:</strong> ${title}<br>
      <strong>Category:</strong> ${category}<br>
      <strong>Priority:</strong> <span style="color: ${priorityColor}; font-weight: bold;">${priority.toUpperCase()}</span><br>
      <strong>Status:</strong> ${status}<br>
      <strong>Submitted:</strong> ${submittedAt.toLocaleDateString()} at ${submittedAt.toLocaleTimeString()}
    `, 'Report Details')}
    
    ${EmailTemplate.createAlert(`
      <strong>What Happens Next?</strong><br>
      • Your report will be reviewed by our team within 24-48 hours<br>
      • You'll receive updates on the investigation progress<br>
      • All communications will be secure and confidential<br>
      • You can track the status in your dashboard
    `, 'success')}
    
    ${EmailTemplate.createButton('View Your Reports', dashboardUrl)}
    
    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 30px 0; border-left: 4px solid #1E4841;">
      <h4 style="margin: 0 0 10px 0; color: #1E4841;">🔒 Security & Privacy</h4>
      <p style="margin: 0; color: #555;">
        Your report is encrypted and stored securely. Only authorized personnel can access it, and your identity is protected according to our privacy policy.
      </p>
    </div>
    
    <p style="margin-top: 30px; color: #666;">
      <strong>Important:</strong> Please save your Report ID (${reportId}) for future reference. You can use it to track your report's progress.
    </p>
  `;

  const html = EmailTemplate.createBaseTemplate(content, {
    title: 'Report Submitted Successfully',
    preheader: `Your report "${title}" has been submitted successfully. Report ID: ${reportId}`,
    headerIcon: '📋',
    headerColor: '#28a745',
    recipientEmail: submitterEmail
  });

  const text = `
Report Submitted Successfully

Your report has been successfully submitted to our secure system.

Report Details:
- Report ID: ${reportId}
- Title: ${title}
- Category: ${category}
- Priority: ${priority.toUpperCase()}
- Status: ${status}
- Submitted: ${submittedAt.toLocaleDateString()} at ${submittedAt.toLocaleTimeString()}

What Happens Next?
• Your report will be reviewed by our team within 24-48 hours
• You'll receive updates on the investigation progress
• All communications will be secure and confidential
• You can track the status in your dashboard

View your reports: ${dashboardUrl}

Security & Privacy:
Your report is encrypted and stored securely. Only authorized personnel can access it, and your identity is protected according to our privacy policy.

Important: Please save your Report ID (${reportId}) for future reference. You can use it to track your report's progress.

Best regards,
Whistleblower System Team
  `.trim();

  return { html, text };
}
