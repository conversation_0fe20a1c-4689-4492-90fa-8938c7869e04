"use client";
import React, { useState, useRef, useCallback, useEffect } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ArrowRight, Check } from "lucide-react";
import { 
  FeatureCard, 
  InvestigatorStepCard, 
  SecurityCard,
  TestimonialCard
} from "../shared/ProductCards";
import { 
  investigatorSteps,
  investigatorFeatures,
  investigatorSecurities,
  investigatorTestimonials
} from "@/lib/mockData/productsData";

// Hero Section Component
export const InvestigatorHero: React.FC = () => (
  <section className="relative px-4 sm:px-8 md:px-12 lg:px-[180px] py-12 sm:py-16 md:py-20 lg:py-[140px] mx-auto bg-gradient-to-l from-[#132f2a] from-70% via-[#1E4841]/90 via-90% to-[#1E4841]/80">
    <Image
      src="/desktop/products/investigator/hero.jpg"
      alt="Investigator background"
      fill
      className="object-cover mix-blend-luminosity opacity-55"
    />
    <div className="relative text-center z-10">
      <p className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 sm:mb-6 leading-tight">Investigate with Confidence.<br />Resolve with Precision.</p>
      <p className="text-base sm:text-lg md:text-xl text-white mb-8 sm:mb-10 px-2 sm:px-8 md:px-20 xl:px-50">Manage whistleblower cases with full visibility, built-in compliance tools, and secure collaboration.</p>
      <Button
        variant="default"
        className="px-4 py-3 sm:px-6 sm:py-4 md:px-9 md:py-7 text-base sm:text-lg md:text-xl text-[#1E4841] bg-[#BBF49C] border border-[#BBF49C] hover:border-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm font-semibold"
        aria-label="View Investigator Dashboard Demo"
      >
        <div className="flex items-center gap-2">
          <p className="hidden sm:block text-[#1E4841]">View Investigator Dashboard Demo</p>
          <p className="block sm:hidden text-[#1E4841]">View Demo</p>
          <ArrowRight className="h-4 w-4 sm:h-5 sm:w-5" />
        </div>
      </Button>
    </div>
  </section>
);

// Workflow Section Component
export const InvestigatorWorkflow: React.FC = () => (
  <section className="mx-auto px-4 sm:px-8 md:px-20 xl:px-40 py-12 sm:py-16 md:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-[28px] font-bold text-[#1E4841] mb-2 text-center">5-Step Investigation Workflow</p>
    <p className="text-sm sm:text-base md:text-lg font-normal text-[#4B5563] mb-12 sm:mb-16 lg:mb-10 xl:mb-20 px-4 sm:px-20 md:px-10 lg:px-20 xl:px-70 text-center">Our streamlined process ensures thorough, consistent, and compliant handling of every whistleblower report.</p>
    <Separator className="relative top-8 sm:top-12 md:top-15.5 lg:top-14.5 bg-[#D5D5D5] border-2 z-1 hidden md:block" />
    <Separator className="relative md:top-116 lg:top-120.5 bg-[#D5D5D5] border-2 z-1 hidden md:block xl:hidden" />
    <Separator className="relative top-204.5 bg-[#D5D5D5] border-2 z-1 hidden md:block lg:hidden" />
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 xl:gap-2 w-full">
      {investigatorSteps.map((step, index) => (
        <InvestigatorStepCard key={index} step={step} />
      ))}
    </div>
  </section>
);

// Dashboard Section Component
export const InvestigatorDashboard: React.FC = () => (
  <section className="mx-auto px-4 sm:px-8 md:px-20 xl:px-40 py-12 sm:py-16 md:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-[28px] font-bold text-[#1E4841] mb-2 text-center">Powerful Investigator Dashboard</p>
    <p className="text-sm sm:text-base md:text-lg font-normal text-[#4B5563] mb-12 sm:mb-16 md:mb-20 px-4 sm:px-20 lg:px-20 xl:px-70 text-center">A comprehensive view of all your cases with powerful tools to manage investigations efficiently.</p>
    <Image
      src="/desktop/products/investigator/icons/dashboard.svg"
      alt="Investigator Dashboard"
      width={1126}
      height={1069}
      className="w-full h-auto max-w-full"
    />
  </section>
);

// Features Section Component
export const InvestigatorFeatures: React.FC = () => (
  <section className="mx-auto px-4 sm:px-8 md:px-20 xl:px-40 py-12 sm:py-16 md:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-[28px] font-bold text-[#1E4841] mb-2 text-center">Powerful Features for Thorough Investigations</p>
    <p className="px-4 sm:px-8 md:px-20 xl:px-55 text-sm sm:text-base md:text-lg font-normal text-[#4B5563] mb-6 sm:mb-8 md:mb-6 text-center">Our Investigator Portal provides all the tools needed to conduct efficient, compliant, and thorough investigations.</p>
    <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6 sm:gap-8 md:gap-10 xl:gap-20 w-full px-2 sm:px-4 md:px-6 xl:px-15">
      {investigatorFeatures.map((feature, index) => (
        <FeatureCard key={index} {...feature} />
      ))}
    </div>
  </section>
);

// Security Section Component
export const InvestigatorSecurity: React.FC = () => (
  <section className="mx-auto px-4 sm:px-8 md:px-20 xl:px-55 py-12 sm:py-16 md:py-20 flex flex-col items-center bg-[#1E4841] relative">
    <Image
      src="/desktop/products/investigator/security.jpg"
      alt="Security Illustration"
      fill
      className="object-cover mix-blend-luminosity opacity-30"
    />
    <p className="text-xl sm:text-2xl md:text-[28px] font-bold text-white mb-2 text-center relative z-10">Security & Compliance Built In</p>
    <p className="px-4 sm:px-8 md:px-20 lg:px-10 xl:px-65 text-sm sm:text-base md:text-lg font-normal text-[#D1D5DB] mb-8 sm:mb-12 md:mb-14 text-center relative z-10">Enterprise-grade security and compliance features to protect sensitive information and maintain data integrity.</p>
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-4 sm:gap-6 w-full relative z-10">
      {investigatorSecurities.map((security, index) => (
        <SecurityCard key={index} {...security} />
      ))}
    </div>
    <div className="flex flex-col lg:flex-row justify-between items-center gap-6 sm:gap-8 mt-12 sm:mt-16 md:mt-20 p-4 sm:p-6 relative z-10 bg-[#142E2C]/50 backdrop-blur-xs rounded-2xl border-1 border-white/20">
      <div className="flex flex-1 lg:flex-1/2 flex-col gap-3 sm:gap-4">
        <p className="text-lg sm:text-xl md:text-2xl font-semibold text-white">Secure by Design</p>
        <p className="text-xs sm:text-sm md:text-base font-normal text-[#D1D5DB] tracking-wider">Our platform is built from the ground up with security as the primary consideration. Every feature and function is designed to protect sensitive information while enabling effective investigations.</p>
        <div className="flex flex-col gap-3 sm:gap-4">
          <div className="flex items-start gap-2">
            <Check className="mt-1 min-w-3 min-h-3 w-4 h-4 sm:min-w-4 sm:min-h-4 sm:w-5.5 sm:h-5.5 text-[#4ADE80] bg-[#22C55E33] p-1 rounded-full" />
            <div className="flex flex-col">
              <span className="text-xs sm:text-sm md:text-base font-medium text-white">SOC 2 Type II Certified</span>
              <span className="text-xs md:text-sm font-normal text-[#D1D5DB]">Independently verified security controls and processes</span>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <Check className="mt-1 min-w-3 min-h-3 w-4 h-4 sm:min-w-4 sm:min-h-4 sm:w-5.5 sm:h-5.5 text-[#4ADE80] bg-[#22C55E33] p-1 rounded-full" />
            <div className="flex flex-col">
              <span className="text-xs sm:text-sm md:text-base font-medium text-white">ISO 27001 Compliant</span>
              <span className="text-xs md:text-sm font-normal text-[#D1D5DB]">International standard for information security management</span>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <Check className="mt-1 min-w-3 min-h-3 w-4 h-4 sm:min-w-4 sm:min-h-4 sm:w-5.5 sm:h-5.5 text-[#4ADE80] bg-[#22C55E33] p-1 rounded-full" />
            <div className="flex flex-col">
              <span className="text-xs sm:text-sm md:text-base font-medium text-white">GDPR Ready</span>
              <span className="text-xs md:text-sm font-normal text-[#D1D5DB]">Built-in data protection and privacy compliance</span>
            </div>
          </div>
        </div>
      </div>
      <Image
        src="/desktop/products/investigator/chat.svg"
        alt="Secure Icon"
        width={583}
        height={376}
        className="w-full h-auto flex-1 lg:w-4/7 max-w-md lg:max-w-none lg:-mr-10"
      />
    </div>
  </section>
);

// Testimonials Section Component
export const InvestigatorTestimonials: React.FC = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const testimonialsRef = useRef<HTMLDivElement>(null);

  const goToNextTestimonial = useCallback(() => {
    if (isAnimating || !testimonialsRef.current) return;

    setIsAnimating(true);

    // If we're at the last testimonial, prepare to loop back
    if (currentTestimonial === investigatorTestimonials.length - 1) {
      // Clone the first testimonial and append it temporarily
      const firstClone = testimonialsRef.current.children[0].cloneNode(true);
      testimonialsRef.current.appendChild(firstClone);

      // Animate to this clone
      testimonialsRef.current.style.transition = "transform 500ms ease-in-out";
      testimonialsRef.current.style.transform = `translateX(-${(currentTestimonial + 1) * 100}%)`;

      // After animation completes, jump back to first without animation
      setTimeout(() => {
        testimonialsRef.current!.style.transition = "none";
        testimonialsRef.current!.style.transform = "translateX(0)";
        // Remove the clone
        testimonialsRef.current!.removeChild(testimonialsRef.current!.lastChild!);
        setCurrentTestimonial(0);

        // Re-enable transitions after a brief delay
        setTimeout(() => {
          testimonialsRef.current!.style.transition = "transform 500ms ease-in-out";
          setIsAnimating(false);
        }, 50);
      }, 500);
    } else {
      // Normal next slide behavior
      setCurrentTestimonial(prev => prev + 1);
      setTimeout(() => {
        setIsAnimating(false);
      }, 500);
    }
  }, [currentTestimonial, isAnimating]);

  useEffect(() => {
    const interval = setInterval(goToNextTestimonial, 5000);
    return () => clearInterval(interval);
  }, [goToNextTestimonial]);

  return (
    <section className="mx-auto px-4 sm:px-8 md:px-20 xl:px-40 py-12 sm:py-16 md:py-20 flex flex-col items-center">
      <div className="relative w-full overflow-hidden">
        <div
          ref={testimonialsRef}
          className="flex"
          style={{
            transform: `translateX(-${currentTestimonial * 100}%)`,
            transition: "transform 500ms ease-in-out"
          }}
        >
          {investigatorTestimonials.map((testimonial, index) => (
            <div
              key={index}
              className="w-full flex-shrink-0"
            >
              <TestimonialCard {...testimonial} />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

// CTA Section Component
export const InvestigatorCTA: React.FC = () => (
  <section className="mx-auto max-w-70 md:max-w-150 lg:max-w-200 xl:max-w-300 px-4 sm:px-8 md:px-20 xl:px-40 py-6 sm:py-8 md:py-12 lg:py-15 my-12 sm:my-16 md:my-20 flex flex-col items-center bg-[#1E4841] shadow-sm rounded-2xl">
    <p className="text-xl sm:text-2xl md:text-3xl font-bold text-white mb-3 sm:mb-4 text-center">Ready to transform your investigation process?</p>
    <p className="px-2 sm:px-4 md:px-8 lg:px-10 text-sm sm:text-base md:text-lg font-normal text-[#DBEAFE] mb-6 sm:mb-8 text-center">Join leading organizations that trust our platform to manage sensitive whistleblower cases with confidence and integrity.</p>
    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center w-full sm:w-auto">
      <Button
        variant="outline"
        className="w-full sm:w-auto px-4 py-3 sm:px-6 sm:py-4 md:px-9 md:py-7 text-sm sm:text-base md:text-lg text-[#1E4841] hover:bg-[#BBF49C] hover:text-gray-900 border-1 border-[#1E4841] transition-all duration-300 shadow-sm font-semibold"
        aria-label="Schedule a product demonstration"
      >
        Schedule a Demo
      </Button>
      <Button
        variant="default"
        className="w-full sm:w-auto px-4 py-3 sm:px-6 sm:py-4 md:px-9 md:py-7 text-sm sm:text-base md:text-lg text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm font-semibold"
        aria-label="Contact sales for more information"
      >
        Contact Sales
      </Button>
    </div>
  </section>
);