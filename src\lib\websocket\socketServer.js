import { createServer } from 'http';
import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';

// Create HTTP server
const httpServer = createServer();

// Create Socket.IO server with CORS configuration
const io = new Server(httpServer, {
  cors: {
    origin: ["http://localhost:3000", "http://localhost:3002"],
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

// Import and setup real-time handlers
async function setupHandlers() {
  try {
    // Dynamic import to avoid ES module issues
    const { setupRealTimeHandlers } = await import('./realTimeHandler.js');
    setupRealTimeHandlers(io);
    console.log('✅ Real-time handlers setup complete');
  } catch (error) {
    console.error('❌ Error setting up real-time handlers:', error);
    
    // Fallback basic handlers
    io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        
        socket.userId = decoded.id;
        socket.userRole = decoded.role;
        socket.companyId = decoded.companyId;
        
        next();
      } catch (error) {
        console.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    io.on('connection', (socket) => {
      console.log(`🔌 User connected: ${socket.userId} (${socket.userRole})`);

      // Join user-specific room
      if (socket.userId) {
        socket.join(`user-${socket.userId}`);
        console.log(`👤 User ${socket.userId} joined personal room`);
      }

      // Join company room for company-wide updates
      if (socket.companyId) {
        socket.join(`company-${socket.companyId}`);
        console.log(`🏢 User ${socket.userId} joined company room: ${socket.companyId}`);
      }

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        console.log(`🔌 User disconnected: ${socket.userId} (${reason})`);
      });

      // Send initial data to newly connected user
      socket.emit('connection-established', {
        userId: socket.userId,
        userRole: socket.userRole,
        companyId: socket.companyId,
        timestamp: new Date().toISOString()
      });
    });
  }
}

// Setup handlers
setupHandlers();

// Start server
const PORT = process.env.WS_PORT || 3001;
httpServer.listen(PORT, () => {
  console.log(`🚀 Socket.IO server running on port ${PORT}`);
  console.log(`📡 Socket.IO endpoint: http://localhost:${PORT}`);
});

// Handle server shutdown
process.on('SIGTERM', () => {
  console.log('🔄 Shutting down Socket.IO server...');
  io.close(() => {
    httpServer.close(() => {
      console.log('✅ Socket.IO server closed');
      process.exit(0);
    });
  });
});

process.on('SIGINT', () => {
  console.log('🔄 Shutting down Socket.IO server...');
  io.close(() => {
    httpServer.close(() => {
      console.log('✅ Socket.IO server closed');
      process.exit(0);
    });
  });
});

export { io };