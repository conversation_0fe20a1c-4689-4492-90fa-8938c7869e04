"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';

interface UnreadCounts {
  messages: number;
  notifications: number;
  isLoading: boolean;
  error: string | null;
}

export function useUnreadCounts() {
  const { user, isAuthenticated } = useAuth();
  const [counts, setCounts] = useState<UnreadCounts>({
    messages: 0,
    notifications: 0,
    isLoading: true,
    error: null
  });

  const fetchUnreadCounts = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setCounts(prev => ({ ...prev, isLoading: false }));
      return;
    }

    try {
      setCounts(prev => ({ ...prev, isLoading: true, error: null }));

      // Fetch unread notifications count
      const notificationsResponse = await fetch('/api/notifications?status=unread&limit=1', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      });

      let notificationCount = 0;
      if (notificationsResponse.ok) {
        const notificationsData = await notificationsResponse.json();
        notificationCount = notificationsData.unreadCount || 0;
      }

      // Fetch unread messages count
      const messagesResponse = await fetch('/api/conversations/read-status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        }
      });

      let messageCount = 0;
      if (messagesResponse.ok) {
        const messagesData = await messagesResponse.json();
        if (messagesData.success && messagesData.readStatuses) {
          messageCount = messagesData.readStatuses.filter((status: { isUnread: boolean }) => status.isUnread).length;
        }
      }

      setCounts({
        messages: messageCount,
        notifications: notificationCount,
        isLoading: false,
        error: null
      });

    } catch (error) {
      console.error('Error fetching unread counts:', error);
      setCounts(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch unread counts'
      }));
    }
  }, [isAuthenticated, user]);

  // Fetch counts on mount and when user changes
  useEffect(() => {
    fetchUnreadCounts();
  }, [fetchUnreadCounts]);

  // Set up polling to refresh counts periodically
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(fetchUnreadCounts, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [isAuthenticated, fetchUnreadCounts]);

  // Refresh function that can be called manually
  const refresh = useCallback(() => {
    fetchUnreadCounts();
  }, [fetchUnreadCounts]);

  return {
    ...counts,
    refresh
  };
}