"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter
} from "@/components/ui/card";
import { PRICING_PLANS, UI_CONSTANTS } from "@/lib";
import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function PricingPlans() {
  const [billing, setBilling] = useState<'monthly' | 'yearly'>('monthly');
  const plans = [...PRICING_PLANS];

  return (
    <div className={`flex flex-col items-center justify-center text-center mx-12 md:mx-16 lg:mx-24 xl:mx-44 mt-24 md:mt-32 xl:mt-40`} style={{ color: UI_CONSTANTS.colors.primary }}>
      <p className="font-semibold text-3xl md:text-4xl lg:text-5xl lg:leading-tight mx-8 md:mx-16 lg:mx-24 xl:mx-40 mb-4 md:mb-5 lg:mb-6">Powerful Compliance Solutions at the Right Price</p>
      <p className="font-normal text-lg md:text-xl md:leading-relaxed mx-8 md:mx-16 lg:mx-32 xl:mx-56">Choose a plan that fits your needs and ensures transparency, security, and compliance. Scale effortlessly as your organization grows.</p>
      <p className={`font-semibold text-base py-2 px-8 rounded-[42px] mt-10 md:mt-12 lg:mt-14 mb-4`} style={{ backgroundColor: UI_CONSTANTS.colors.accent }}>Pricing Plans</p>
      <p className="font-semibold text-2xl md:text-[28px] lg:text-[32px] lg:leading-tight mx-8 md:mx-16 lg:mx-32 xl:mx-64 mt-2">Pricing Plans for Your Whistleblower Compliance Platform</p>

      <Tabs value={billing} onValueChange={(val) => setBilling(val as 'monthly' | 'yearly')} className="w-fit mx-auto mt-8">
        <TabsList className="flex bg-[#F5F5F5] rounded-full px-2 py-6">
          <TabsTrigger
            value="monthly"
            className={`px-6 py-4 rounded-full font-semibold text-lg transition-all duration-300 ${billing === 'monthly' ? 'data-[state=active]:bg-[#BBF49C] data-[state=active]:text-[#1E4841]' : 'bg-transparent text-[#6B7271]'}`}
          >
            Monthly
          </TabsTrigger>
          <TabsTrigger
            value="yearly"
            className={`px-6 py-4 rounded-full font-semibold text-lg transition-all duration-300 ${billing === 'yearly' ? 'data-[state=active]:bg-[#BBF49C] data-[state=active]:text-[#1E4841]' : 'bg-transparent text-[#6B7271]'}`}
          >
            Yearly <span className="ml-2 text-xs font-medium bg-[#E5E6E6] text-[#6B7271] px-2 py-1 rounded-full">Save 20%</span>
          </TabsTrigger>
        </TabsList>
      </Tabs>
      
      <div className="w-full flex flex-col lg:flex-row justify-between text-center md:text-left gap-4 mt-12 md:mt-16 lg:mt-20">
        {plans.map((plan) => (
          <Card
            key={plan.name}
            className={`w-full flex flex-col justify-between shadow-none ${plan.isHighlighted
              ? "bg-[#195A4C] text-white lg:-mt-3 lg:mb-3"
              : "text-black"
            } border border-[#EBEBEB] rounded-[20px] px-5 py-8 mb-4 md:mb-0 md:grid md:grid-cols-2 md:grid-rows-[auto_1fr_auto] md:gap-4 lg:flex lg:flex-col lg:gap-0`}
          >
            <div className="md:flex md:flex-col md:gap-4 lg:gap-0">
              <CardHeader className="p-0 gap-2">
                <CardTitle className={`font-semibold text-2xl md:text-[26px] ${plan.isHighlighted ? "text-[#FBFBFC]" : "text-[#242E2C]"
                  }`}>
                  {plan.name}
                </CardTitle>
                <CardDescription className={`font-medium text-base leading-relaxed ${plan.isHighlighted ? "text-[#E5E6E6]" : "text-[#6B7271]"
                  } pr-2`}>
                  {plan.description}
                </CardDescription>
                <p className="font-extrabold text-2xl text-[28px] md:text-[32px]">
                  {billing === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice}
                </p>
              </CardHeader>

              <div>
                <Separator className={`md:hidden lg:block my-4 lg:my-2 ${plan.isHighlighted ? "bg-[#D8D8D81F]" : "bg-[#0000001F]"}`} />
                <p className="font-bold text-base mb-1">Access & Users</p>
                <ul className="ml-0 list-disc lg:list-none font-normal text-left text-sm md:text-base md:leading-loose">
                  <li className="flex items-center">
                    <Image src={plan.isHighlighted ? "/desktop/pricing/arrow-light.png" : "/desktop/pricing/arrow-dark.png"} alt="Arrow Icon" width={10} height={10} className="mr-2 rotate-90" />
                    Whistleblower: <span className="font-semibold">{plan.access.whistleblower}</span>
                  </li>
                  <li className="flex items-center">
                    <Image src={plan.isHighlighted ? "/desktop/pricing/arrow-light.png" : "/desktop/pricing/arrow-dark.png"} alt="Arrow Icon" width={10} height={10} className="mr-2 rotate-90" />
                    Investigator: <span className="font-semibold">{plan.access.investigator}</span>
                  </li>
                  <li className="flex items-center">
                    <Image src={plan.isHighlighted ? "/desktop/pricing/arrow-light.png" : "/desktop/pricing/arrow-dark.png"} alt="Arrow Icon" width={10} height={10} className="mr-2 rotate-90" />
                    Admin Panel: <span className="font-semibold">{plan.access.admin}</span>
                  </li>
                </ul>
              </div>
            </div>

            <CardContent className="p-0">
              <Separator className={`md:hidden lg:block my-4 lg:my-2 ${plan.isHighlighted ? "bg-[#D8D8D81F]" : "bg-[#0000001F]"}`} />
              <p className="font-bold text-base mb-1">Core Features</p>
              <div className="text-left font-normal flex flex-col md:gap-3 lg:gap-0">
                {plan.features.map((feature, i) => (
                  <p key={i} className="ml-0 text-sm lg:text-xs lg:leading-loose xl:text-base xl:leading-loose flex items-center">
                    <Image src={plan.isHighlighted ? "/desktop/pricing/arrow-light.png" : "/desktop/pricing/arrow-dark.png"} alt="Arrow Icon" width={10} height={10} className="mr-2 rotate-90" />
                    {feature}
                  </p>))}
              </div>
            </CardContent>

            <CardFooter className="p-0 mt-auto md:col-span-2">
              <Link href="/signup" className="w-full">
                <Button
                  variant={plan.isHighlighted ? "default" : "outline"}
                  className={`${plan.isHighlighted
                    ? "w-full mt-8 py-5 md:py-7 font-semibold text-base md:text-lg text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300"
                    : "w-full px-8 py-7 font-semibold text-base md:text-xl text-[#1E4841] hover:bg-[#BBF49C] hover:text-gray-900 border border-[#EBEBEB] transition-all duration-300"
                  }`}
                  aria-label={`Get started with ${plan.name} Pricing`}
                >
                  Get Started
                </Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}