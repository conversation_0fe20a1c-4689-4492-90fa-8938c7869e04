import mongoose from 'mongoose';
import connectDB from './mongodb';
import { Report, Notification, Conversation, Message, Blog, User, Company, PricingPlan } from './models';
import { ReportFilters, NotificationFilters, User as UserType, UserInput } from '@/lib/types';
import { randomBytes, createHash } from 'crypto';
import bcrypt from 'bcryptjs';
import { UserDocument, ReportDocument, BlogDocument, PricingPlanDocument } from './models/interfaces';
import { encryptMessage, decryptMessage, encryptHtmlContent, decryptHtmlContent } from '@/lib/encryption/messageEncryption';
import logger from '@/lib/utils/logger';

export class DataService {
  // User related methods
  static async getUserById(id: string) {
    await connectDB();
    return await User.findById(id).select('-hashedPassword') as UserDocument;
  }

  static async getUserByEmail(email: string) {
    await connectDB();
    if (!email || typeof email !== 'string') {
      throw new Error('Invalid email');
    }
    return await User.findOne({ email: email.toLowerCase().trim() }) as UserDocument;
  }

  static async getUserByResetToken(token: string) {
    await connectDB();
    return await User.findOne({
      passwordResetToken: token,
      passwordResetExpires: { $gt: new Date() }
    }) as UserDocument;
  }

  static async updateUser(userId: string, updateData: Partial<UserType>) {
    await connectDB();
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid user ID');
    }
    return await User.findByIdAndUpdate(userId, updateData, { new: true }) as UserDocument;
  }

  static async createUser(userData: UserInput) {
    await connectDB();
    
    const userDoc: Partial<UserType> = {
      ...userData,
      isActive: userData.isActive ?? true,
      role: userData.role || 'whistleblower'
    };
    
    // Hash password if provided
    if (userData.password) {
      userDoc.hashedPassword = await this.hashPassword(userData.password);
    }
    
    const user = new User(userDoc);
    return await user.save();
  }

  static async authenticateUser(email: string, password: string) {
    try {
      await connectDB();
      const user = await User.findOne({ email }) as UserDocument;
      
      if (!user) {
        return { error: 'Invalid email or password' };
      }
      
      // Check if account is inactive
      if (user.isActive === false) {
        return { error: 'Account is inactive. Please contact support.' };
      }
      
      // Check if account is locked
      if (user.accountLocked && user.accountLockedUntil && new Date() < user.accountLockedUntil) {
        const minutesLeft = Math.ceil((user.accountLockedUntil.getTime() - Date.now()) / (60 * 1000));
        return { 
          error: `Account is temporarily locked due to too many failed login attempts. Please try again in ${minutesLeft} minute${minutesLeft !== 1 ? 's' : ''}.` 
        };
      }
      
      let passwordMatch = false;
      
      // Handle password migration from SHA-256 to bcrypt
      if (user.passwordNeedsMigration && user.passwordHashAlgorithm === 'sha256') {
        // Verify with old SHA-256 method
        const salt = process.env.PASSWORD_SALT;
        if (!salt) {
          logger.error('PASSWORD_SALT environment variable not set');
          return { error: 'Authentication configuration error' };
        }
        const oldHash = createHash('sha256').update(password + salt).digest('hex');
        
        if (user.hashedPassword === oldHash) {
          passwordMatch = true;
          
          // Migrate to bcrypt
          const bcryptHash = await this.hashPassword(password);
          user.hashedPassword = bcryptHash;
          user.passwordNeedsMigration = false;
          user.passwordHashAlgorithm = 'bcrypt';
          await user.save();
        }
      } else {
        // Standard bcrypt comparison
        passwordMatch = await this.comparePassword(password, user.hashedPassword);
      }
      
      if (!passwordMatch) {
        // Increment failed login attempts
        user.failedLoginAttempts = (user.failedLoginAttempts || 0) + 1;
        
        // Lock account after 5 failed attempts
        if (user.failedLoginAttempts >= 5) {
          user.accountLocked = true;
          user.accountLockedUntil = new Date(Date.now() + 30 * 60 * 1000); // Lock for 30 minutes
          await user.save();
          return { error: 'Account locked due to too many failed login attempts. Please try again in 30 minutes.' };
        }
        
        await user.save();
        return { error: 'Invalid email or password' };
      }
      
      // Reset failed login attempts on successful login
      if (user.failedLoginAttempts > 0 || user.accountLocked) {
        user.failedLoginAttempts = 0;
        user.accountLocked = false;
        user.accountLockedUntil = undefined;
      }
      
      user.lastLogin = new Date();
      user.lastActive = new Date();
      await user.save();
      
      return { user };
    } catch (error) {
      logger.error('Authentication error:', error);
      return { error: 'An error occurred during authentication' };
    }
  }

  static async generateTwoFactorCode(userId: string) {
    try {
      await connectDB();
      const user = await User.findById(userId) as UserDocument;
      
      if (!user) {
        console.error('User not found for 2FA code generation');
        return null;
      }
      
      // Generate a 6-digit code
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
      
      // Save to user's two-factor auth data
      user.twoFactor = {
        enabled: user.twoFactor?.enabled || false,
        method: user.twoFactor?.method || 'email',
        secret: user.twoFactor?.secret,
        backupCodes: user.twoFactor?.backupCodes,
        verificationCode,
        verificationCodeExpires: expiresAt,
        attempts: 0 // Reset attempts counter
      };
      
      await user.save();
      return { verificationCode, expiresAt };
    } catch (error) {
      console.error('Error generating 2FA code:', error);
      return null;
    }
  }

  static async verifyTwoFactorCode(userId: string, code: string) {
    try {
      await connectDB();
      const user = await User.findById(userId) as UserDocument;
      
      if (!user) {
        console.error('User not found for 2FA verification');
        return false;
      }
      
      if (!user.twoFactor?.verificationCode) {
        console.error('No verification code found for user');
        return false;
      }
      
      // Check if too many attempts
      if (user.twoFactor.attempts && user.twoFactor.attempts >= 5) {
        // Clear verification code and require a new one
        user.twoFactor.verificationCode = undefined;
        user.twoFactor.verificationCodeExpires = undefined;
        await user.save();
        return false;
      }
      
      // Check if code is expired
      if (user.twoFactor.verificationCodeExpires && 
          new Date() > user.twoFactor.verificationCodeExpires) {
        return false;
      }
      
      // Check if code matches
      if (user.twoFactor.verificationCode !== code) {
        // Increment failed attempts
        user.twoFactor.attempts = (user.twoFactor.attempts || 0) + 1;
        await user.save();
        return false;
      }
      
      // Clear verification code after successful verification
      user.twoFactor.verificationCode = undefined;
      user.twoFactor.verificationCodeExpires = undefined;
      user.twoFactor.attempts = 0;
      await user.save();
      
      return true;
    } catch (error) {
      console.error('Error verifying 2FA code:', error);
      return false;
    }
  }

  // Company related methods
  static async getCompanyById(id: string) {
    await connectDB();
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new Error('Invalid company ID');
    }
    return await Company.findById(id);
  }

  static async createCompany(companyData: Record<string, unknown>) {
    await connectDB();
    const company = new Company(companyData);
    return await company.save();
  }

  static async updateCompany(id: string, updateData: Record<string, unknown>) {
    await connectDB();
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new Error('Invalid company ID');
    }
    const allowedFields = ['name', 'description', 'settings'];
    const filteredData = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = updateData[key];
        return obj;
      }, {} as Record<string, unknown>);
    return await Company.findByIdAndUpdate(id, filteredData, { new: true });
  }

  // Report related methods
  static async getReports(userId?: string, filters?: ReportFilters, companyId?: string) {
    await connectDB();
    
    const query: Record<string, unknown> = {};

    // For whistleblowers, only show their own reports
    if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      query.userId = new mongoose.Types.ObjectId(userId);
    } else if (companyId) {
      // For admins/investigators, show all reports from their company
      const companyUsers = await User.find({ companyId }).select('_id');
      const companyUserIds = companyUsers.map(user => user._id);
      if (companyUserIds.length > 0) {
        query.userId = { $in: companyUserIds };
      } else {
        // No users in company, return empty
        return [];
      }
    } else {
      // If no userId or companyId, return empty (for security)
      return [];
    }

    if (filters?.status) query.status = { $in: filters.status };
    if (filters?.category) query.category = { $in: filters.category };
    if (filters?.priority) query.priority = { $in: filters.priority };
    
    const reports = await Report.find(query)
      .populate('assignedInvestigator', 'firstName lastName')
      .populate('userId', 'firstName lastName email companyId')
      .sort({ createdAt: -1 })
      .limit(filters?.limit || 100)
      .skip(filters?.offset || 0);
    
    // Ensure all reports have a valid status
    return reports.map(report => {
      const typedReport = report.toObject() as unknown as ReportDocument;
      const reportAny = report as { status?: string };
      // Set default status if undefined
      if (!reportAny.status) reportAny.status = 'New';
      return typedReport;
    });
  }

  static async getReportById(id: string) {
    await connectDB();
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return null;
    }
    const report = await Report.findById(id).populate('assignedInvestigator', 'firstName lastName');
    if (!report) return null;
    return report as unknown as ReportDocument;
  }

  static async createReport(reportData: Partial<ReportDocument & { isDraft?: boolean; isDetailed?: boolean }>) {
    await connectDB();
    
    // Validate required fields (less strict for drafts)
    if (!reportData.title || !reportData.userId) {
      throw new Error('Missing required fields: title, userId');
    }
    
    // Whitelist allowed fields
    const allowedFields = ['title', 'description', 'category', 'priority', 'userId', 'companyId', 'status', 'isDraft', 'isDetailed'];
    const filteredData = Object.keys(reportData)
      .filter(key => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = reportData[key as keyof (ReportDocument & { isDraft?: boolean; isDetailed?: boolean })];
        return obj;
      }, {} as Partial<ReportDocument & { isDraft?: boolean; isDetailed?: boolean }>);
    
    // Generate a unique report ID if not provided
    if (!filteredData.reportId) {
      const reportCount = await Report.countDocuments();
      const reportId = `WB-${new Date().getFullYear()}-${(reportCount + 1).toString().padStart(4, '0')}`;
      filteredData.reportId = reportId;
    }
    
    const report = new Report(filteredData);
    const savedReport = await report.save() as unknown as ReportDocument;
    
    // Create a notification for the report creation
    if (reportData.userId) {
      await this.createNotification({
        userId: reportData.userId,
        type: 'report_update',
        title: 'Report Created',
        message: `Your report ${savedReport.reportId} has been created successfully.`,
        reportId: savedReport._id,
        priority: 'medium'
      });
    }
    
    // Create a conversation for the report
    try {
      await this.createConversationForReport(savedReport._id.toString(), reportData.userId?.toString());
    } catch (error) {
      console.error('Error creating conversation for report:', error);
      // Don't fail the report creation if conversation creation fails
    }
    
    // Trigger real-time stats update
    try {
      const { triggerStatsUpdateOnReportCreate } = await import('@/lib/realtime/statsUpdater');
      await triggerStatsUpdateOnReportCreate({
        reportId: savedReport.reportId,
        title: savedReport.title,
        priority: savedReport.priority,
        userId: savedReport.userId,
        companyId: savedReport.companyId
      });
    } catch (error) {
      console.error('Error triggering real-time stats update:', error);
    }
    
    return savedReport;
  }

  static async updateReport(id: string, updateData: Partial<ReportDocument>) {
    await connectDB();
    
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      throw new Error('Invalid report ID');
    }
    
    // Whitelist allowed fields for update
    const allowedFields = ['status', 'assignedInvestigator', 'priority', 'notes'];
    const filteredData = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = updateData[key as keyof ReportDocument];
        return obj;
      }, {} as Partial<ReportDocument>);
    
    // Get the original report for comparison
    const originalReport = await Report.findById(id);
    const oldStatus = (originalReport as { status?: string })?.status;
    
    const updatedReport = await Report.findByIdAndUpdate(id, filteredData, { new: true });
    const typedReport = updatedReport as unknown as ReportDocument;
    
    // Create a notification for status updates
    if (updatedReport && updateData.status) {
      // Cast to ReportDocument type
      const reportDoc = updatedReport as unknown as ReportDocument;
      if (reportDoc.userId) {
        await this.createNotification({
          userId: reportDoc.userId,
          type: 'report_update',
          title: 'Report Status Updated',
          message: `Your report ${reportDoc.reportId} status has been updated to ${updateData.status}.`,
          reportId: updatedReport._id,
          priority: 'medium'
        });
      }
    }
    
    // Trigger real-time stats update for status changes
    if (updatedReport && updateData.status && oldStatus !== updateData.status) {
      try {
        const { triggerStatsUpdateOnStatusChange } = await import('@/lib/realtime/statsUpdater');
        await triggerStatsUpdateOnStatusChange(typedReport, oldStatus, updateData.status);
        
        // Broadcast status update to all connected users
        const io = (global as { io?: unknown }).io;
        if (io && typeof io === 'object' && io !== null && 'emit' in io) {
          (io as { emit: (event: string, data: unknown) => void }).emit('realtime_event', {
            type: 'report_status_updated',
            data: {
              reportId: typedReport._id,
              oldStatus,
              newStatus: updateData.status,
              reportTitle: typedReport.title,
              userId: typedReport.userId
            },
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Error triggering real-time stats update on status change:', error);
      }
    }
    
    return typedReport;
  }

  static async deleteReport(id: string) {
    await connectDB();
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new Error('Invalid report ID');
    }
    return await Report.findByIdAndDelete(id);
  }

  static async getReportCount(filters?: Record<string, unknown>) {
    await connectDB();
    return await Report.countDocuments(filters || {});
  }

  static async getRecentActivity(userId?: string, companyId?: string, limit: number = 10) {
    await connectDB();
    
    console.log('DataService.getRecentActivity: Input params', { userId, companyId, limit });
    
    const query: Record<string, unknown> = {};
    
    // For whistleblowers, only show their own activity
    if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      console.log('DataService.getRecentActivity: Filtering by userId', userId);
      query.userId = new mongoose.Types.ObjectId(userId);
    } else if (companyId) {
      // For admins/investigators, show company-wide activity
      const companyUsers = await User.find({ companyId }).select('_id');
      const companyUserIds = companyUsers.map(user => user._id);
      console.log('DataService.getRecentActivity: Company users found', companyUserIds.length);
      if (companyUserIds.length > 0) {
        query.userId = { $in: companyUserIds };
      } else {
        // No users in company, return empty
        return [];
      }
    } else {
      // No valid filters, return empty
      console.log('DataService.getRecentActivity: No valid filters provided');
      return [];
    }
    
    console.log('DataService.getRecentActivity: Final query', query);
    
    // Get recent reports as activities
    const recentReports = await Report.find(query)
      .populate('userId', 'firstName lastName email')
      .populate('assignedInvestigator', 'firstName lastName')
      .sort({ updatedAt: -1, createdAt: -1 })
      .limit(limit);
    
    // Transform reports into activity format
    const activities = recentReports.map(report => {
      const reportObj = report.toObject() as unknown as ReportDocument & { 
        userId?: { firstName?: string; lastName?: string; email?: string } 
      };
      const user = reportObj.userId;
      const userName = user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email : 'Unknown User';
      
      // Determine activity type and description based on report status and recent changes
      let action = 'created';
      let description = `Report ${reportObj.reportId} was created`;
      
      if (reportObj.status) {
        switch (reportObj.status) {
          case 'Under Review':
            action = 'updated';
            description = `Report ${reportObj.reportId} is now under review`;
            break;
          case 'Awaiting Response':
            action = 'updated';
            description = `Report ${reportObj.reportId} is awaiting response`;
            break;
          case 'Resolved':
            action = 'resolved';
            description = `Report ${reportObj.reportId} has been resolved`;
            break;
          case 'In Progress':
            action = 'updated';
            description = `Report ${reportObj.reportId} is in progress`;
            break;
          case 'Closed':
            action = 'closed';
            description = `Report ${reportObj.reportId} has been closed`;
            break;
          default:
            action = 'created';
            description = `Report ${reportObj.reportId} was created`;
        }
      }
      
      return {
        id: reportObj._id.toString(),
        type: 'report',
        action,
        description,
        user: userName,
        timestamp: reportObj.updatedAt || reportObj.createdAt,
        reportId: reportObj._id.toString(),
        priority: reportObj.priority,
        status: reportObj.status
      };
    });
    
    return activities;
  }

  static async getDashboardStats(userId?: string, companyId?: string) {
    await connectDB();
    
    try {
      const query: Record<string, unknown> = {};
      
      // For whistleblowers, only show their own stats
      if (userId && mongoose.Types.ObjectId.isValid(userId)) {
        console.log('DataService.getDashboardStats: Filtering by userId', userId);
        query.userId = new mongoose.Types.ObjectId(userId);
      } else if (companyId) {
        // For admins/investigators, show company-wide stats
        const companyUsers = await User.find({ companyId }).select('_id');
        const companyUserIds = companyUsers.map(user => user._id);
        console.log('DataService.getDashboardStats: Company users found', companyUserIds.length);
        if (companyUserIds.length > 0) {
          query.userId = { $in: companyUserIds };
        } else {
          // No users in company, return default stats
          return this.getDefaultStats();
        }
      } else {
        // No valid filters, return default stats
        return this.getDefaultStats();
      }
      
      console.log('DataService.getDashboardStats: Final query', query);
      
      // Get report counts by status
      const [
        totalReports,
        newReports,
        underReviewReports,
        awaitingResponseReports,
        resolvedReports,
        // closedReports
      ] = await Promise.all([
        Report.countDocuments(query),
        Report.countDocuments({ ...query, status: 'New' }),
        Report.countDocuments({ ...query, status: 'Under Review' }),
        Report.countDocuments({ ...query, status: 'Awaiting Response' }),
        Report.countDocuments({ ...query, status: 'Resolved' }),
        Report.countDocuments({ ...query, status: 'Closed' })
      ]);
      
      // Get priority counts
      const [highPriorityReports] = await Promise.all([
        Report.countDocuments({ ...query, priority: 'High' }),
        Report.countDocuments({ ...query, priority: 'Medium' }),
        Report.countDocuments({ ...query, priority: 'Low' })
      ]);
      
      // Calculate monthly trends (compare current month with last month)
      const currentDate = new Date();
      const lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
      const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
      
      const lastMonthQuery = {
        ...query,
        createdAt: {
          $gte: lastMonth,
          $lt: currentMonth
        }
      };
      
      const currentMonthQuery = {
        ...query,
        createdAt: {
          $gte: currentMonth
        }
      };

      const [lastMonthTotal, currentMonthTotal, lastMonthNew, currentMonthNew, lastMonthResolved, currentMonthResolved] = await Promise.all([
        Report.countDocuments(lastMonthQuery),
        Report.countDocuments(currentMonthQuery),
        Report.countDocuments({ ...lastMonthQuery, status: 'New' }),
        Report.countDocuments({ ...currentMonthQuery, status: 'New' }),
        Report.countDocuments({ ...lastMonthQuery, status: 'Resolved' }),
        Report.countDocuments({ ...currentMonthQuery, status: 'Resolved' })
      ]);

      // Calculate percentage changes
      const totalReportsChange = lastMonthTotal > 0 
        ? Math.round(((currentMonthTotal - lastMonthTotal) / lastMonthTotal) * 100)
        : currentMonthTotal > 0 ? 100 : 0;
      
      const newReportsChange = lastMonthNew > 0 
        ? Math.round(((currentMonthNew - lastMonthNew) / lastMonthNew) * 100)
        : currentMonthNew > 0 ? 100 : 0;
      
      const resolvedReportsChange = lastMonthResolved > 0 
        ? Math.round(((currentMonthResolved - lastMonthResolved) / lastMonthResolved) * 100)
        : currentMonthResolved > 0 ? 100 : 0;

      // Get chart data for the last 6 months
      const chartData = await this.getChartData(query);
      
      return {
        totalReports,
        newReports,
        underReviewReports,
        awaitingResponseReports,
        resolvedReports,
        highPriorityReports,
        periodComparison: {
          totalReportsChange,
          newReportsChange,
          resolvedReportsChange,
          period: 'month'
        },
        chartData,
        lastCalculated: new Date()
      };
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      // Return default stats on error
      return {
        totalReports: 0,
        newReports: 0,
        underReviewReports: 0,
        awaitingResponseReports: 0,
        resolvedReports: 0,
        highPriorityReports: 0,
        periodComparison: {
          totalReportsChange: 0,
          newReportsChange: 0,
          resolvedReportsChange: 0,
          period: 'month'
        },
        chartData: {
          overTime: [],
          statusDistribution: []
        },
        lastCalculated: new Date()
      };
    }
  }

  // Helper method to get default stats when no data is available
  private static getDefaultStats() {
    return {
      totalReports: 0,
      newReports: 0,
      underReviewReports: 0,
      awaitingResponseReports: 0,
      resolvedReports: 0,
      highPriorityReports: 0,
      periodComparison: {
        totalReportsChange: 0,
        newReportsChange: 0,
        resolvedReportsChange: 0,
        period: 'month'
      },
      chartData: {
        overTime: [],
        statusDistribution: []
      },
      lastCalculated: new Date()
    };
  }

  // Helper method to get chart data
  private static async getChartData(query: Record<string, unknown>) {
    try {
      // Get data for the last 6 months
      const months = [];
      const currentDate = new Date();
      
      for (let i = 5; i >= 0; i--) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        const nextDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i + 1, 1);
        
        const monthQuery = {
          ...query,
          createdAt: {
            $gte: date,
            $lt: nextDate
          }
        };
        
        const reports = await Report.countDocuments(monthQuery);
        const cases = reports; // For now, cases = reports
        
        months.push({
          month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          reports,
          cases
        });
      }

      // Get status distribution
      const statusDistribution = await Report.aggregate([
        { $match: query },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);

      const statusColors = {
        'New': '#3B82F6',
        'Under Review': '#F59E0B',
        'Awaiting Response': '#8B5CF6',
        'Resolved': '#10B981',
        'Closed': '#6B7280'
      };

      const formattedStatusDistribution = statusDistribution.map(item => ({
        name: item._id || 'Unknown',
        value: item.count,
        fill: statusColors[item._id as keyof typeof statusColors] || '#6B7280'
      }));

      return {
        overTime: months,
        statusDistribution: formattedStatusDistribution
      };
    } catch (error) {
      console.error('Error getting chart data:', error);
      return {
        overTime: [],
        statusDistribution: []
      };
    }
  }

  // Notification related methods
  static async getNotifications(userId: string, filters?: NotificationFilters) {
    await connectDB();
    
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid user ID');
    }
    
    const query: Record<string, unknown> = { userId };
    if (filters?.status) query.status = { $in: filters.status };
    if (filters?.type) query.type = { $in: filters.type };
    
    return await Notification.find(query)
      .sort({ createdAt: -1 })
      .limit(Math.min(filters?.limit || 10, 100))
      .skip(filters?.offset || 0);
  }

  static async getUnreadNotificationCount(userId: string) {
    await connectDB();
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return 0;
    }
    return await Notification.countDocuments({ userId, status: 'unread' });
  }

  static async markNotificationAsRead(notificationId: string) {
    await connectDB();
    if (!mongoose.Types.ObjectId.isValid(notificationId)) {
      throw new Error('Invalid notification ID');
    }
    return await Notification.findByIdAndUpdate(
      notificationId, 
      { status: 'read', readAt: new Date() },
      { new: true }
    );
  }

  static async createNotification(notificationData: Record<string, unknown>) {
    await connectDB();
    
    if (!notificationData.userId || !mongoose.Types.ObjectId.isValid(notificationData.userId as string)) {
      throw new Error('Invalid user ID');
    }
    
    const allowedFields = ['userId', 'type', 'title', 'message', 'priority', 'actionUrl', 'reportId', 'metadata'];
    const filteredData = Object.keys(notificationData)
      .filter(key => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = notificationData[key];
        return obj;
      }, {} as Record<string, unknown>);
    
    const notification = new Notification(filteredData);
    const savedNotification = await notification.save();
    
    // Trigger real-time notification update
    try {
      const { RealTimeStatsUpdater } = await import('@/lib/realtime/statsUpdater');
      if (notificationData.userId) {
        await RealTimeStatsUpdater.broadcastNotificationUpdate(
          notificationData.userId.toString(),
          savedNotification
        );
      }
    } catch (error) {
      console.error('Error triggering real-time notification update:', error);
    }
    
    return savedNotification;
  }

  static async markAllNotificationsAsRead(userId: string) {
    await connectDB();
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid user ID');
    }
    return await Notification.updateMany(
      { userId, status: 'unread' },
      { status: 'read', readAt: new Date() }
    );
  }

  static async deleteNotification(id: string) {
    await connectDB();
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new Error('Invalid notification ID');
    }
    return await Notification.findByIdAndDelete(id);
  }

  // Conversation related methods
  static async getConversations(userId: string, companyId?: string) {
    await connectDB();
    
    console.log('DataService.getConversations: Input params', { userId, companyId });
    
    if (!userId || !mongoose.Types.ObjectId.isValid(userId)) {
      console.log('DataService.getConversations: Invalid userId');
      return [];
    }
    
    // Always filter by userId as participant and exclude deleted/archived conversations
    const conversationQuery: Record<string, unknown> = { 
      participants: new mongoose.Types.ObjectId(userId),
      status: { $nin: ['deleted', 'archived'] }
    };
    
    console.log('DataService.getConversations: Final query', conversationQuery);
    
    const conversations = await Conversation.find(conversationQuery)
    .populate('reportId', 'reportId title')
    .populate({
      path: 'participants',
      select: 'firstName lastName role companyId',
      match: { _id: { $ne: userId } }
    })
    .sort({ lastMessageAt: -1 });
    
    return conversations.map(conv => {
      const convAny = conv as { participants?: unknown[] };
      if (!convAny.participants) convAny.participants = [];
      return conv;
    });
  }



  static async getConversationByReportId(reportId: string) {
    await connectDB();
    return await Conversation.findOne({ reportId })
      .populate('reportId', 'reportId title')
      .populate('participants', 'firstName lastName role');
  }

  static async updateConversationStatus(id: string, status: 'active' | 'closed' | 'archived' | 'deleted') {
    await connectDB();
    return await Conversation.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    );
  }

  static async addParticipantToConversation(conversationId: string, userId: string) {
    await connectDB();
    return await Conversation.findByIdAndUpdate(
      conversationId,
      { $addToSet: { participants: userId } },
      { new: true }
    ).populate('participants', 'firstName lastName role');
  }

  static async createConversationForReport(reportId: string, userId?: string) {
    await connectDB();
    
    if (!reportId || !mongoose.Types.ObjectId.isValid(reportId)) {
      throw new Error('Invalid reportId');
    }
    
    // Check if conversation already exists for this report
    const existingConversation = await Conversation.findOne({ reportId });
    if (existingConversation) {
      return existingConversation;
    }
    
    // Get the report to access company information
    const report = await Report.findById(reportId).populate('userId', 'companyId');
    if (!report) {
      throw new Error('Report not found');
    }
    
    // Get company admins/investigators to add as participants
    const reportWithUser = report as { userId?: { companyId?: string } };
    const companyId = reportWithUser.userId?.companyId;
    
    const participants = [userId].filter(Boolean); // Start with report creator
    
    if (companyId) {
      // For 1-on-1 conversations, find the primary admin/investigator for this company
      // Priority: admin > investigator  
      const primaryStaff = await User.findOne({
        companyId,
        role: { $in: ['admin', 'investigator'] },
        isActive: true
      }).sort({ role: 1 });

      if (primaryStaff) {
        participants.push(primaryStaff._id.toString());
      }
    }
    

    
    // Create the conversation
    const conversation = new Conversation({
      reportId,
      participants: [...new Set(participants)], // Remove duplicates
      status: 'active',
      lastMessageAt: new Date()
    });
    
    const savedConversation = await conversation.save();
    
    // Send confirmation message
    if (userId) {
      await this.createMessage({
        conversationId: savedConversation._id.toString(),
        senderId: userId,
        content: `Thank you for submitting your report. Your report has been received and will be reviewed by our team. You will be notified of any updates.`,
        messageType: 'system'
      });
    }
    
    return savedConversation;
  }

  // Message related methods

  static async markMessageAsRead(messageId: string, userId: string) {
    await connectDB();
    
    const message = await Message.findById(messageId);
    if (!message) return null;
    
    // Check if user already marked as read
    const messageDoc = message as {
      readBy?: Array<{ userId: { toString: () => string }; readAt: Date }>;
      save: () => Promise<unknown>;
    };
    
    const alreadyRead = messageDoc.readBy?.some(
      (read: { userId: { toString: () => string } }) => read.userId.toString() === userId
    );
    
    if (!alreadyRead) {
      if (!messageDoc.readBy) {
        messageDoc.readBy = [];
      }
      messageDoc.readBy.push({
        userId: new mongoose.Types.ObjectId(userId),
        readAt: new Date()
      });
      await message.save();
    }
    
    return message;
  }

  static async updateMessage(messageId: string, updateData: { content?: string; htmlContent?: string }) {
    await connectDB();
    
    if (!mongoose.Types.ObjectId.isValid(messageId)) {
      throw new Error('Invalid message ID');
    }
    
    const sanitizedData = {
      content: updateData.content?.replace(/[<>"'&]/g, '').trim(),
      htmlContent: updateData.htmlContent?.replace(/<script[^>]*>.*?<\/script>/gi, ''),
      editedAt: new Date()
    };
    
    return await Message.findByIdAndUpdate(messageId, sanitizedData, { new: true });
  }

  static async deleteMessage(messageId: string, userId: string) {
    await connectDB();
    
    const message = await Message.findById(messageId);
    const messageDoc = message as unknown as {
      senderId: { toString: () => string };
      isDeleted?: boolean;
      deletedAt?: Date;
      save: () => Promise<unknown>;
    };
    
    if (!message || messageDoc.senderId.toString() !== userId) {
      throw new Error('Unauthorized to delete this message');
    }
    
    // Soft delete
    messageDoc.isDeleted = true;
    messageDoc.deletedAt = new Date();
    (message as unknown as { content: string; htmlContent?: string }).content = 'This message has been deleted';
    (message as unknown as { content: string; htmlContent?: string }).htmlContent = undefined;
    await message.save();
    
    return message;
  }

  static async addReaction(messageId: string, userId: string, emoji: string) {
    await connectDB();
    
    if (!mongoose.Types.ObjectId.isValid(messageId) || !mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid message or user ID');
    }
    
    return await Message.findByIdAndUpdate(
      messageId,
      {
        $pull: { reactions: { userId: new mongoose.Types.ObjectId(userId) } },
        $push: { reactions: { userId: new mongoose.Types.ObjectId(userId), emoji, createdAt: new Date() } }
      },
      { new: true }
    );
  }

  static async removeReaction(messageId: string, userId: string) {
    await connectDB();
    
    if (!mongoose.Types.ObjectId.isValid(messageId) || !mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid message or user ID');
    }
    
    return await Message.findByIdAndUpdate(
      messageId,
      { $pull: { reactions: { userId: new mongoose.Types.ObjectId(userId) } } },
      { new: true }
    );
  }

  // Blog related methods
  static async getBlogPosts(featured?: boolean, limit = 10, offset = 0): Promise<BlogDocument[]> {
    await connectDB();
    
    const query = featured !== undefined ? { featured } : {};
    
    const blogs = await (Blog as mongoose.Model<BlogDocument>)
      .find(query)
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .lean();
      
    return blogs as BlogDocument[];
  }

  static async getBlogBySlug(slug: string): Promise<BlogDocument | null> {
    await connectDB();
    return await (Blog as mongoose.Model<BlogDocument>)
      .findOne({ slug })
      .lean() as BlogDocument | null;
  }

  static async createBlogPost(blogData: Partial<BlogDocument>) {
    await connectDB();
    const blog = new (Blog as mongoose.Model<BlogDocument>)(blogData);
    return (await blog.save()).toObject() as BlogDocument;
  }

  static async updateBlogPost(id: string, updateData: Partial<BlogDocument>) {
    await connectDB();
    return await (Blog as mongoose.Model<BlogDocument>)
      .findByIdAndUpdate(id, updateData, { new: true })
      .lean() as BlogDocument | null;
  }

  static async deleteBlogPost(id: string): Promise<BlogDocument | null> {
    await connectDB();
    return await (Blog as mongoose.Model<BlogDocument>)
      .findByIdAndDelete(id)
      .lean() as BlogDocument | null;
  }




  // Pricing Plan related methods
  static async getPricingPlans(): Promise<PricingPlanDocument[]> {
    await connectDB();
    const plans = await (PricingPlan as mongoose.Model<PricingPlanDocument>)
      .find({ isActive: true })
      .sort({ order: 1 })
      .lean();
    return plans as PricingPlanDocument[];
  }

  static async getPricingPlanById(id: string): Promise<PricingPlanDocument | null> {
    await connectDB();
    const plan = await (PricingPlan as mongoose.Model<PricingPlanDocument>)
      .findById(id)
      .lean();
    return plan as PricingPlanDocument | null;
  }

  // Account management methods
  static async unlockAccount(userId: string) {
    await connectDB();
    return await User.findByIdAndUpdate(
      userId,
      {
        accountLocked: false,
        accountLockedUntil: undefined,
        failedLoginAttempts: 0
      },
      { new: true }
    ).select('-hashedPassword -sessionToken');
  }

  // OAuth related methods

  static async findOrCreateOAuthUser(profile: { 
    email: string;
    name?: string;
    given_name?: string;
    family_name?: string;
    picture?: string;
    sub?: string;
  }, provider: string) {
    try {
      if (!profile || !profile.email) {
        console.error('Invalid OAuth profile:', profile);
        return { error: 'Invalid OAuth profile data' };
      }
      
      await connectDB();
      
      // Try to find existing user by email
      let user = await User.findOne({ email: profile.email });
      const typedUser = user as unknown as UserDocument;
      
      if (!user) {
        // Extract name parts safely
        let firstName = '', lastName = '';
        if (profile.name) {
          const nameParts = profile.name.split(' ');
          firstName = nameParts[0] || '';
          lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
        } else if (profile.given_name && profile.family_name) {
          // Some providers use given_name and family_name
          firstName = profile.given_name;
          lastName = profile.family_name;
        }
        
        // Create new user if not found
        user = new User({
          email: profile.email,
          firstName,
          lastName,
          role: 'whistleblower', // Default role for OAuth users
          isActive: true,
          provider: provider,
          lastLogin: new Date(),
          lastActive: new Date(),
          emailVerified: true // OAuth users have verified emails
        });
        
        await user.save();
      } else {
        // Update existing user with last login and provider info
        const userAny = user as unknown as UserDocument;
        await User.findByIdAndUpdate(user._id, {
          lastLogin: new Date(),
          lastActive: new Date(),
          provider: provider,
          // If user was inactive, reactivate them
          ...(userAny.isActive === false && { isActive: true })
        });
      }
      
      return typedUser;
    } catch (error) {
      console.error('OAuth user creation/update error:', error);
      return { error: 'Failed to process OAuth authentication' };
    }
  }

  // Messaging methods
  static async createConversation(conversationData: {
    reportId: string;
    participants: string[];
  }) {
    await connectDB();

    // Enforce 1-on-1 conversations: only allow exactly 2 participants
    if (conversationData.participants.length !== 2) {
      throw new Error('Conversations must have exactly 2 participants (1-on-1 only)');
    }

    // Verify participants have different roles (whistleblower + admin/investigator)
    const participantUsers = await User.find({
      _id: { $in: conversationData.participants }
    }).select('role') as Array<{ role: string }>;

    const roles = participantUsers.map(user => user.role);
    const hasWhistleblower = roles.includes('whistleblower');
    const hasStaff = roles.includes('admin') || roles.includes('investigator');

    if (!hasWhistleblower || !hasStaff) {
      throw new Error('Conversation must be between a whistleblower and an admin/investigator');
    }

    const conversation = new Conversation(conversationData);
    return await conversation.save();
  }

  static async createMessage(messageData: {
    conversationId: string;
    senderId: string;
    content: string;
    htmlContent?: string;
    messageType?: 'text' | 'file' | 'system';
    attachments?: Array<{
      fileName: string;
      fileUrl: string;
      fileSize: number;
      mimeType: string;
    }>;
  }) {
    await connectDB();
    
    // Validate inputs
    if (!messageData.conversationId || !mongoose.Types.ObjectId.isValid(messageData.conversationId)) {
      throw new Error('Invalid conversationId');
    }
    if (!messageData.senderId || !mongoose.Types.ObjectId.isValid(messageData.senderId)) {
      throw new Error('Invalid senderId');
    }
    if (!messageData.content || messageData.content.trim().length === 0) {
      throw new Error('Message content cannot be empty');
    }
    
    // Sanitize content
    const sanitizedContent = messageData.content.replace(/[\r\n\t]/g, ' ').trim();
    
    // Encrypt message content for security
    const encryptedContent = encryptMessage(sanitizedContent);
    let encryptedHtmlContent;
    
    if (messageData.htmlContent) {
      encryptedHtmlContent = encryptHtmlContent(messageData.htmlContent);
    }
    
    const message = new Message({
      conversationId: messageData.conversationId,
      senderId: messageData.senderId,
      content: encryptedContent.encryptedContent,
      htmlContent: encryptedHtmlContent?.encryptedContent,
      messageType: messageData.messageType || 'text',
      attachments: messageData.attachments,
      timestamp: new Date(),
      isEncrypted: true,
      encryptionData: {
        contentIv: encryptedContent.iv,
        contentTag: encryptedContent.tag,
        htmlContentIv: encryptedHtmlContent?.iv,
        htmlContentTag: encryptedHtmlContent?.tag
      }
    });
    
    const savedMessage = await message.save();
    
    // Update conversation's last message timestamp
    await this.updateConversationLastActivity(messageData.conversationId);
    
    // Trigger real-time updates
    try {
      // Get conversation with participants and report data
      const conversation = await Conversation.findById(messageData.conversationId)
        .populate('participants', '_id')
        .populate('reportId', 'companyId reportId');
      
      if (conversation) {
        // Broadcast new message to participants
        const { RealTimeStatsUpdater, triggerStatsUpdateOnMessage } = await import('@/lib/realtime/statsUpdater');
        const participantIds = ((conversation as unknown as { participants: { _id: { toString: () => string } }[] }).participants).map(p => p._id.toString());
        
        await RealTimeStatsUpdater.broadcastNewMessage(
          messageData.conversationId,
          {
            _id: savedMessage._id,
            content: messageData.content, // Send unencrypted content for real-time display
            senderId: messageData.senderId,
            messageType: messageData.messageType,
            timestamp: (savedMessage as unknown as { timestamp: Date }).timestamp
          },
          participantIds
        );
        
        // Update stats
        await triggerStatsUpdateOnMessage(savedMessage, conversation);
      }
    } catch (error) {
      console.error('Error triggering real-time message updates:', error);
    }
    
    return savedMessage;
  }

  static async getConversationsByUserId(userId: string) {
    await connectDB();
    return await Conversation.find({
      participants: userId
    }).populate('reportId').populate('participants', 'firstName lastName email role');
  }

  static async getMessagesByConversationId(conversationId: string) {
    await connectDB();
    const messages = await Message.find({
      conversationId
    }).populate('senderId', 'firstName lastName email role').sort({ timestamp: 1 });
    
    // Ensure timestamp exists
    messages.forEach(msg => {
      const msgAny = msg as { timestamp?: Date; createdAt?: Date };
      if (!msgAny.timestamp) msgAny.timestamp = msgAny.createdAt || new Date();
    });
    
    // Decrypt messages before returning
    return messages.map(message => {
      const messageObj = message.toObject() as Record<string, unknown>;
      
      if (messageObj.isEncrypted && messageObj.encryptionData) {
        // Decrypt content
        const decryptedContent = decryptMessage({
          encryptedContent: messageObj.content as string,
          iv: (messageObj.encryptionData as Record<string, unknown>).contentIv as string || '',
          tag: (messageObj.encryptionData as Record<string, unknown>).contentTag as string || '',
          isEncrypted: true
        });
        
        messageObj.content = decryptedContent.content;
        
        // Decrypt HTML content if present
        if (messageObj.htmlContent && (messageObj.encryptionData as Record<string, unknown>).htmlContentIv) {
          const decryptedHtmlContent = decryptHtmlContent({
            encryptedContent: messageObj.htmlContent as string,
            iv: (messageObj.encryptionData as Record<string, unknown>).htmlContentIv as string,
            tag: (messageObj.encryptionData as Record<string, unknown>).htmlContentTag as string || '',
            isEncrypted: true
          });
          
          messageObj.htmlContent = decryptedHtmlContent.content;
        }
      }
      
      // Remove encryption data from response for security
      delete messageObj.encryptionData;
      
      return messageObj;
    });
  }

  static async getMessages(conversationId: string) {
    return await this.getMessagesByConversationId(conversationId);
  }

  static async getConversationById(conversationId: string) {
    await connectDB();
    return await Conversation.findById(conversationId)
      .populate('reportId')
      .populate('participants', 'firstName lastName email role');
  }

  static async updateConversationLastActivity(conversationId: string) {
    await connectDB();
    return await Conversation.findByIdAndUpdate(
      conversationId,
      { lastMessageAt: new Date() },
      { new: true }
    );
  }

  static async getCompanyUsers(companyId: string) {
    await connectDB();
    return await User.find({ companyId })
      .select('-hashedPassword -sessionToken')
      .sort({ createdAt: -1 });
  }

  // Migration utility: Convert group conversations to 1-on-1 conversations
  static async migrateToOneOnOneConversations() {
    await connectDB();

    console.log('Starting migration to 1-on-1 conversations...');

    // Find all conversations with more than 2 participants
    const groupConversations = await Conversation.find({
      $expr: { $gt: [{ $size: "$participants" }, 2] }
    }).populate('participants', 'role firstName lastName')
      .populate('reportId', 'reportId userId');

    console.log(`Found ${groupConversations.length} group conversations to migrate`);

    for (const conversation of groupConversations) {
      try {
        const convAny = conversation as unknown as {
          participants: Array<{ _id: string; role: string; firstName?: string; lastName?: string }>;
          reportId: { _id: string; reportId: string; userId: string };
        };

        // Find the whistleblower (report creator)
        const whistleblower = convAny.participants.find(p => p.role === 'whistleblower');

        // Find the primary admin/investigator (prefer admin over investigator)
        const staff = convAny.participants
          .filter(p => p.role === 'admin' || p.role === 'investigator')
          .sort((a, b) => (a.role === 'admin' ? 0 : 1) - (b.role === 'admin' ? 0 : 1))[0]; // admin first

        if (whistleblower && staff) {
          // Update the conversation to only have these 2 participants
          await Conversation.findByIdAndUpdate(conversation._id, {
            participants: [whistleblower._id, staff._id]
          });

          console.log(`Migrated conversation ${conversation._id} to 1-on-1 between ${whistleblower.firstName} (whistleblower) and ${staff.firstName} (${staff.role})`);
        } else {
          console.warn(`Could not migrate conversation ${conversation._id} - missing whistleblower or staff member`);
        }
      } catch (error) {
        console.error(`Error migrating conversation ${conversation._id}:`, error);
      }
    }

    console.log('Migration to 1-on-1 conversations completed');
  }

  // Utility methods
  static async hashPassword(password: string): Promise<string> {
    // Use bcrypt with automatic salt generation (cost factor 12)
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  }
  
  static async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    // Compare password with stored hash using bcrypt
    return await bcrypt.compare(password, hashedPassword);
  }

  static generateToken(): string {
    // Generate a more secure token with additional entropy
    const timestamp = Date.now().toString();
    const random = randomBytes(48).toString('hex'); // Increased from 32 to 48 bytes
    const serverSecret = process.env.SERVER_SECRET || 'whistleblower-server-secret';
    
    return createHash('sha256')
      .update(timestamp + random + serverSecret)
      .digest('hex');
  }
  
  static validateEmail(email: string): boolean {
    // Basic email validation regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  static validatePassword(password: string): { valid: boolean; message?: string } {
    if (!password || password.length < 8) {
      return { valid: false, message: 'Password must be at least 8 characters long' };
    }
    
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    if (!hasUpper) return { valid: false, message: 'Password must contain at least one uppercase letter' };
    if (!hasLower) return { valid: false, message: 'Password must contain at least one lowercase letter' };
    if (!hasNumber) return { valid: false, message: 'Password must contain at least one number' };
    if (!hasSpecial) return { valid: false, message: 'Password must contain at least one special character' };
    
    return { valid: true };
  }






}

export default DataService;

