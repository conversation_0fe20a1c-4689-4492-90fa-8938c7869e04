"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

interface FAQ {
  question: string;
  answer: string;
}

interface FAQSectionProps {
  faqs: FAQ[];
}

export default function FAQSection({ faqs }: FAQSectionProps) {
  return (
    <div className="w-full">
      <Accordion type="single" collapsible className="w-full space-y-4">
        {faqs.map((faq, index) => (
          <AccordionItem
            key={index}
            value={`item-${index}`}
            className="rounded-lg px-6 py-2 hover:shadow-sm transition-shadow border-none bg-[#F9FAFB]"
          >
            <AccordionTrigger className="text-left hover:no-underline text-lg font-semibold text-[#242E2C]">
              {faq.question}
            </AccordionTrigger>
            <AccordionContent className="text-[#242E2C] pt-2 pb-4">
              {faq.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>

      <div className="mt-12 flex flex-col items-center justify-center gap-4">
        <p className="text-[#4B5563] text-base">
          Don&apos;t see your question here?
        </p>
        <Link href="/faqs" className="text-[#1E4841] text-base font-semibold flex items-center gap-2">
          View our complete FAQ <ArrowRight size={16} />
        </Link>
      </div>
    </div>
  );
}