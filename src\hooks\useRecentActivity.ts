"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { apiClient } from '@/lib/api/client';

interface RecentActivity {
  id: string;
  type: string;
  action: string;
  description: string;
  user?: string;
  timestamp: Date;
  reportId?: string;
  priority?: string;
  status?: string;
}

interface UseRecentActivityReturn {
  activities: RecentActivity[];
  isLoading: boolean;
  error: string | null;
  refresh: () => void;
}

export function useRecentActivity(limit: number = 10): UseRecentActivityReturn {
  const { user, isAuthenticated } = useAuth();
  const [activities, setActivities] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRecentActivity = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setActivities([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const data = await apiClient.get(`/api/recent-activity?limit=${limit}`) as { success: boolean; data: RecentActivity[]; error?: string };
      
      if (data.success) {
        setActivities(data.data || []);
      } else {
        throw new Error(data.error || 'Failed to fetch recent activity');
      }
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch recent activity');
      setActivities([]);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, limit]);

  // Fetch activities on mount and when dependencies change
  useEffect(() => {
    fetchRecentActivity();
  }, [fetchRecentActivity]);

  // Set up polling to refresh activities periodically
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(fetchRecentActivity, 60000); // Refresh every minute
    return () => clearInterval(interval);
  }, [isAuthenticated, fetchRecentActivity]);

  const refresh = useCallback(() => {
    fetchRecentActivity();
  }, [fetchRecentActivity]);

  return {
    activities,
    isLoading,
    error,
    refresh
  };
}