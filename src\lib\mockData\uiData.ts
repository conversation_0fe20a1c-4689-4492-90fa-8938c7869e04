// UI-related mock data (logos, images, provision cards, workflow steps)
import { CompanyLogo, ProvisionCardData, WorkflowStep } from "@/lib/types";

// Company Logos for Banner
export const COMPANY_LOGOS: CompanyLogo[] = [
    { src: '/desktop/home/<USER>/terra.svg', alt: 'Terra logo', height: 122, width: 52 },
    { src: '/desktop/home/<USER>/webflow.svg', alt: 'Webflow logo', height: 122, width: 52 },
    { src: '/desktop/home/<USER>/linkedin.svg', alt: 'LinkedIn logo', height: 123, width: 50 },
    { src: '/desktop/home/<USER>/genz.svg', alt: 'GenZ logo', height: 123, width: 50 },
    { src: '/desktop/home/<USER>/trace.svg', alt: 'Trace logo', height: 106, width: 42 }
];

// Benefits Images
export const BENEFITS_IMAGES = [
    { src: '/desktop/home/<USER>/benefits-hero.svg', alt: 'Benefits Section picture', height: 560, width: 430 },
    { src: '/desktop/home/<USER>/benefits-hero.svg', alt: 'Benefits Placeholder', height: 460, width: 430 },
    { src: '/desktop/home/<USER>/benefits-hero.svg', alt: 'Benefits Placeholder', height: 460, width: 430 },
];

// Provision Cards
export const PROVISION_CARDS: ProvisionCardData[] = [
    {
        src: "/desktop/home/<USER>/reporting.svg",
        alt: "Whistleblower Reporting",
        width: 68,
        height: 68,
        title: "Whistleblower Reporting",
        description: "All aspects of secure and anonymous reporting, including fraud detection, compliance violations & unethical behavior"
    },
    {
        src: "/desktop/home/<USER>/management.svg",
        alt: "Admin Case Management",
        width: 66,
        height: 66,
        title: "Whistleblower Reporting",
        description: "Extend compliance best practices to investigations, legal, and risk teams."
    },
    {
        src: "/desktop/home/<USER>/investigations.svg",
        alt: "Internal Investigations",
        width: 65,
        height: 66,
        title: "Internal Investigations",
        description: "Build dynamic investigation workflows with secure documentation & collaboration tools."
    },
    {
        src: "/desktop/home/<USER>/solutions.svg",
        alt: "Smart Compliance Solutions",
        width: 65,
        height: 65,
        title: "Smart Compliance Solutions",
        description: "Our platform allows organizations to maintain transparency, compliance & ethics at scale"
    }
];

// Workflow Steps for How We Work section
export const WORKFLOW_STEPS: WorkflowStep[] = [
    {
        title: "Report Submission",
        description: "Individuals can securely submit a whistleblower report through an encrypted and anonymous platform. The system ensures confidentiality while allowing users to provide necessary details and evidence."
    },
    {
        title: "Case Review & Verification",
        description: "The submitted report is assessed by compliance officers, who verify the authenticity of the claims using advanced screening and data analysis tools."
    },
    {
        title: "Investigation Process",
        description: "Once verified, the case is assigned to the appropriate authority for further investigation. The platform tracks progress while maintaining anonymity for the whistleblower."
    },
    {
        title: "Resolution & Reporting",
        description: "Final decisions are documented, and necessary actions are taken. The whistleblower may receive updates (if opted) while ensuring full compliance with legal and ethical standards."
    }
];
