export class InputSanitizer {
  static sanitizeString(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/[<>'"&]/g, (match) => {
        const entities: { [key: string]: string } = {
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#x27;',
          '&': '&amp;'
        };
        return entities[match] || match;
      })
      .replace(/[\r\n\t]/g, ' ')
      .trim();
  }

  static sanitizeForLog(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .replace(/[\r\n\t]/g, ' ')
      .replace(/[^\w\s@.-]/g, '')
      .trim()
      .substring(0, 100);
  }

  static validateObjectId(id: string): boolean {
    return /^[0-9a-fA-F]{24}$/.test(id);
  }
}