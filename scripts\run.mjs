import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Set NODE_ENV to development for seeding
process.env.NODE_ENV = 'development';

const command = process.argv[2];

if (!command) {
  console.log('Usage: node scripts/run.js <command>');
  console.log('');
  console.log('Available commands:');
  console.log('  seed     - Seed the database with sample data');
  console.log('  verify   - Verify database structure and data');
  console.log('  clean    - Clear all data from database');
  console.log('');
  process.exit(1);
}

try {
  switch (command) {
    case 'seed':
      console.log('🌱 Starting database seeding...');
      execSync(`npx tsx "${path.join(__dirname, 'seed.ts')}"`, { 
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });
      break;
      
    case 'verify':
      console.log('🔍 Starting database verification...');
      execSync(`npx tsx "${path.join(__dirname, 'verify.ts')}"`, { 
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });
      break;
      
    case 'clean':
      console.log('🧹 Clearing database...');
      execSync(`npx tsx -e "
        import { config } from 'dotenv';
        import mongoose from 'mongoose';
        import { Company, User, Report, Notification, Conversation, Message, Blog, PricingPlan } from './src/lib/db/models/index.js';
        
        config({ path: '.env.local' });
        
        async function clearDatabase() {
          try {
            await mongoose.connect(process.env.MONGODB_URI);
            console.log('Connected to database');
            
            await Promise.all([
              Company.deleteMany({}),
              User.deleteMany({}),
              Report.deleteMany({}),
              Notification.deleteMany({}),
              Conversation.deleteMany({}),
              Message.deleteMany({}),
              Blog.deleteMany({}),
              PricingPlan.deleteMany({})
            ]);
            
            console.log('✅ Database cleared successfully');
            await mongoose.connection.close();
            process.exit(0);
          } catch (error) {
            console.error('❌ Error clearing database:', error);
            process.exit(1);
          }
        }
        
        clearDatabase();
      "`, { 
        stdio: 'inherit',
        cwd: path.join(__dirname, '..')
      });
      break;
      
    default:
      console.error(`Unknown command: ${command}`);
      process.exit(1);
  }
  
  console.log('✅ Command completed successfully!');
} catch (error) {
  console.error('❌ Command failed:', error.message);
  process.exit(1);
}