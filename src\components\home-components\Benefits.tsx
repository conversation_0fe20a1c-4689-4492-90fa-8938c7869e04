import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { BENEFITS_IMAGES } from "@/lib/mockData";

export default function Benefits() {
    const DemoButton = () => (
        <Button
            variant="default"
            aria-label="Schedule a Demo"
            className="w-fit px-4 py-3 md:px-8 md:py-6 text-sm md:text-base lg:text-lg text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm font-semibold"
        >
            Schedule a Demo
        </Button>
    );

    const BenefitsImage = ({ src, alt, height, width, className }: { src: string, alt: string, height: number, width: number, className?: string }) => (
        <Image
            src={src}
            alt={alt}
            height={height}
            width={width}
            className={className}
            priority
        />
    );

    return (
        <div className={`w-full bg-white px-4 sm:px-8 md:px-16 lg:px-44 pt-10 xl:py-12`}>
            <div className="flex flex-col lg:flex-row justify-between items-center">
                <div className="w-full lg:w-3/8 xl:w-1/3 flex flex-col gap-4 mb-8">
                    <p className="font-normal text-sm md:text-base/5">Our platform ensures a confidential and seamless way for employees and stakeholders to report concerns without fear</p>
                    <p className="font-semibold text-sm md:text-base">✔ 100% Anonymous & Secure<br />✔ Streamlined Case Management<br />✔ Real-Time Reporting & Tracking<br />✔ Compliance with Global Regulations</p>
                </div>
                <BenefitsImage
                    {...BENEFITS_IMAGES[0]}
                    className="h-auto relative w-98 md:w-110 lg:w-85 xl:w-140 rounded-sm bg-red-700 lg:mt-[-20px] xl:mt-[-200px]"
                />
            </div>
            <div className="h-1 w-full mt-10 lg:mt-6 xl:mt-10 border-t-1 border-black"></div>
            <div className="h-full my-12 md:my-14 xl:my-24 flex flex-col xl:flex-col lg:flex-row justify-between items-center lg:items-start gap-12 text-center xl:text-left">
                <div className="flex flex-col xl:flex-row md:justify-between items-center gap-6 md:gap-8 lg:gap-0 xl:gap-16 lg:w-full lg:h-[440px] xl:h-full">
                    <BenefitsImage
                        {...BENEFITS_IMAGES[1]}
                        className="relative w-100 h-auto md:w-110 lg:h-45 xl:h-85 xl:w-auto"
                    />
                    <div className="flex flex-col gap-4 xl:gap-8 items-center justify-between xl:items-start p-1">
                        <p className="font-bold text-xl lg:text-base/5 xl:text-[28px]/8">Streamline Whistleblowing with a Centralized, Anonymous Reporting System</p>
                        <p className="font-normal text-sm md:text-base/5 lg:text-sm/4 xl:text-base/5">Handling misconduct reports via email and spreadsheets is inefficient and exposes organizations to risks. Our whistleblower platform enables a secure, confidential, and compliant reporting process—ensuring that every case is tracked, managed, and resolved efficiently.</p>
                        <DemoButton />
                    </div>
                </div>
                <div className="flex flex-col-reverse xl:flex-row justify-between items-center gap-6 md:gap-8 lg:gap-0 xl:gap-12 lg:w-full lg:h-[440px] xl:h-full">
                    <div className="flex flex-col gap-4 xl:gap-8 md:pr-10 lg:pr-0 xl:pr-10 items-center justify-between xl:items-start p-1">
                        <p className="font-bold text-xl lg:text-base/5 xl:text-[28px]/8 lg:mb-1 xl:mb-0">Empower Employees to Speak Up Without Fear</p>
                        <p className="font-normal text-sm md:text-base/5 lg:text-sm/4 xl:text-base/5 mr-6">A workplace culture that values integrity and transparency starts with trust. Traditional whistleblowing processes can be intimidating, discouraging employees from coming forward. Our platform makes reporting simple, accessible, and stress-free—ensuring that concerns are heard and acted upon.</p>
                        <DemoButton />
                    </div>
                    <BenefitsImage
                        {...BENEFITS_IMAGES[2]}
                        className="relative w-100 h-auto md:w-110 lg:h-45 lg:w-auto xl:h-85 xl:w-auto"
                    />
                </div>
            </div>
        </div>
    );
}