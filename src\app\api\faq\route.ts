import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'nodejs';

// Hardcoded FAQ data
const HARDCODED_FAQS = [
  {
    id: 'faq-1',
    category: 'general',
    question: 'What is a whistleblower?',
    answer: 'A whistleblower is a person who exposes wrongdoing within an organization in the hope of stopping it.',
    order: 1,
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'faq-2',
    category: 'general',
    question: 'Is my identity protected?',
    answer: 'Yes, our platform provides multiple layers of protection to ensure your identity remains confidential.',
    order: 2,
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'faq-3',
    category: 'reporting',
    question: 'How do I submit a report?',
    answer: 'You can submit a report through our secure online form. Both anonymous and identified submissions are accepted.',
    order: 1,
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'faq-4',
    category: 'reporting',
    question: 'What happens after I submit a report?',
    answer: 'Your report will be reviewed by our compliance team and investigated according to our established procedures.',
    order: 2,
    isActive: true,
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'faq-5',
    category: 'legal',
    question: 'Am I protected from retaliation?',
    answer: 'Yes, there are legal protections in place to prevent retaliation against whistleblowers.',
    order: 1,
    isActive: true,
    createdAt: new Date('2024-01-01')
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    
    let filteredFaqs = HARDCODED_FAQS.filter(faq => faq.isActive);
    
    if (category) {
      filteredFaqs = filteredFaqs.filter(faq => faq.category === category);
    }
    
    // Sort by order, then by creation date
    filteredFaqs.sort((a, b) => {
      if (a.order !== b.order) {
        return a.order - b.order;
      }
      return a.createdAt.getTime() - b.createdAt.getTime();
    });
    
    return NextResponse.json({
      success: true,
      data: filteredFaqs
    });
  } catch (error) {
    console.error('FAQ API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}