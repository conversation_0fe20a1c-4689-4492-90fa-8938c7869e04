"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { sessionManager, SessionData } from '@/lib/utils/sessionManager';

// Hardcoded user credentials

interface User {
  id: string;
  email: string;
  role: 'admin' | 'investigator' | 'whistleblower';
  name: string;
  companyId?: string;
  firstName?: string;
  lastName?: string;
  lastLogin?: Date;
  currentLoginTime?: Date;
  previousLoginTime?: Date;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [sessionData, setSessionData] = useState<SessionData | null>(null);
  const router = useRouter();

  // Check if user is logged in on mount
  useEffect(() => {
    console.log('useAuth: Checking authentication on mount');
    const session = sessionManager.getCurrentSession();
    const token = localStorage.getItem('auth_token');
    
    console.log('useAuth: Session and token check', {
      hasSession: !!session,
      hasToken: !!token,
      isSessionValid: session ? sessionManager.isSessionValid() : false
    });
    
    if (session && sessionManager.isSessionValid() && token) {
      const previousLogin = sessionManager.getPreviousLoginTime(session.userId);
      
      const userData: User = {
        id: session.userId,
        email: session.email,
        role: session.role,
        name: session.name,
        companyId: session.companyId,
        firstName: session.firstName,
        lastName: session.lastName,
        lastLogin: previousLogin,
        currentLoginTime: session.loginTime,
        previousLoginTime: previousLogin
      };
      
      console.log('useAuth: Setting user data', userData);
      setUser(userData);
      setSessionData(session);
    } else {
      console.log('useAuth: Invalid session or token, clearing');
      // Clear invalid session and token
      sessionManager.clearSession();
      localStorage.removeItem('auth_token');
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string, loginType?: 'admin' | 'whistleblower'): Promise<boolean> => {
    try {
      // Determine the API endpoint based on login type
      let apiEndpoint = '/api/auth/login';
      if (loginType === 'admin') {
        apiEndpoint = '/api/auth/login/admin';
      } else if (loginType === 'whistleblower') {
        apiEndpoint = '/api/auth/login/whistleblower';
      }

      // Try the authentication API
      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Store JWT token
        if (result.token) {
          localStorage.setItem('auth_token', result.token);
        }

        // Create session using session manager
        const session = sessionManager.createSession({
          id: result.user.id,
          email: result.user.email,
          role: result.user.role,
          name: result.user.fullName || `${result.user.firstName} ${result.user.lastName}`.trim(),
          companyId: result.user.companyId,
          firstName: result.user.firstName,
          lastName: result.user.lastName
        });

        // Get previous login time
        const previousLogin = sessionManager.getPreviousLoginTime(session.userId);
        
        // Create user object
        const user: User = {
          id: session.userId,
          email: session.email,
          role: session.role,
          name: session.name,
          companyId: result.user.companyId,
          firstName: result.user.firstName,
          lastName: result.user.lastName,
          lastLogin: previousLogin,
          currentLoginTime: session.loginTime,
          previousLoginTime: previousLogin
        };

        setUser(user);
        setSessionData(session);
        
        // Navigate to appropriate dashboard based on role
        if (user.role === 'admin') {
          router.push('/dashboard/admin');
        } else if (user.role === 'whistleblower') {
          router.push('/dashboard/whistleblower');
        } else if (user.role === 'investigator') {
          router.push('/dashboard/admin'); // Investigators use admin dashboard
        }
        
        return true;
      } else {
        // If API returns an error, throw it to be caught below
        throw new Error(result.error || 'Authentication failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error; // Re-throw to let the calling component handle the error
    }
  };

  const logout = () => {
    // Clear JWT token
    localStorage.removeItem('auth_token');
    
    sessionManager.clearSession();
    setUser(null);
    setSessionData(null);
    router.push('/');
  };

  const isAdmin = () => {
    return user?.role === 'admin';
  };

  const isWhistleblower = () => {
    return user?.role === 'whistleblower';
  };

  const getLoginTimeFormatted = () => {
    if (!user?.currentLoginTime) return 'No login time available';
    return sessionManager.getFormattedLoginTime(user.currentLoginTime);
  };

  const getSessionDuration = () => {
    return sessionManager.getSessionDuration();
  };

  const getPreviousLoginFormatted = () => {
    if (!user?.previousLoginTime) return 'First time login';
    return sessionManager.getFormattedLoginTime(user.previousLoginTime);
  };

  return {
    user,
    sessionData,
    isLoading,
    login,
    logout,
    isAdmin,
    isWhistleblower,
    isAuthenticated: !!user,
    getLoginTimeFormatted,
    getSessionDuration,
    getPreviousLoginFormatted
  };
}