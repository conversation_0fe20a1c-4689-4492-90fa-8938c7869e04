import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>r, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { ArrowDownToLine, MoreVertical } from "lucide-react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>hart, Pie, Cell, Area, AreaChart, CartesianGrid } from "recharts";
import { useDashboardStats } from "@/hooks/useDashboardStats";

export default function AdminCaseTrends() {
    const { stats, isLoading } = useDashboardStats();
    
    // Use data from API or fallback to empty data
    const chartData = stats?.chartData || {
        overTime: [],
        statusDistribution: []
    };
    
    // Check if data is empty
    const hasOverTimeData = chartData.overTime && chartData.overTime.some(item => item.cases > 0);
    const hasStatusData = chartData.statusDistribution && chartData.statusDistribution.length > 0;

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
                {/* Loading state for both charts */}
                <Card className="lg:col-span-2">
                    <CardHeader>
                        <CardTitle className="text-base sm:text-lg">Cases Over Time</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="h-48 sm:h-56 md:h-66 min-h-[200px] flex items-center justify-center">
                            <div className="w-8 h-8 border-2 border-[#BBF49C] border-t-transparent rounded-full animate-spin" />
                        </div>
                    </CardContent>
                </Card>
                <Card className="lg:col-span-2">
                    <CardHeader>
                        <CardTitle className="text-base sm:text-lg">Case Status Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="h-48 sm:h-56 md:h-66 min-h-[200px] flex items-center justify-center">
                            <div className="w-8 h-8 border-2 border-[#BBF49C] border-t-transparent rounded-full animate-spin" />
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
            {/* Cases Over Time Chart */}
            <Card className="lg:col-span-2">
                <CardHeader className="flex flex-row items-center justify-between gap-2 sm:gap-0">
                    <div>
                        <CardTitle className="text-base sm:text-lg">Cases Over Time</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                        <ArrowDownToLine className="w-4 h-4 text-[#6B7280]" />
                        <MoreVertical className="w-4 h-4 text-[#6B7280]" />
                    </div>
                </CardHeader>
                <CardContent className="px-1 sm:px-2">
                    {!hasOverTimeData ? (
                        <div className="h-48 sm:h-56 md:h-66 min-h-[200px] flex flex-col items-center justify-center text-center">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                            </div>
                            <h3 className="text-sm font-medium text-gray-900 mb-1">No case data available</h3>
                            <p className="text-xs text-gray-500">Cases will appear here once submitted</p>
                        </div>
                    ) : (
                        <ChartContainer
                        config={{
                            cases: {
                                label: "Cases",
                                color: "#BBF49C",
                            },
                        }}
                        className="h-48 sm:h-56 md:h-66 w-full min-h-[200px]"
                    >
                        <AreaChart
                            data={chartData.overTime}
                            margin={{ top: 10, right: 15, left: 0, bottom: 0 }}
                        >
                            <defs>
                                <linearGradient id="colorCases" x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="10%" stopColor="#BBF49C" stopOpacity={0.3} />
                                    <stop offset="90%" stopColor="#BBF49C" stopOpacity={0.05} />
                                </linearGradient>
                            </defs>
                            <CartesianGrid
                                stroke="#E5E7EB"
                                horizontal={true}
                                vertical={false}
                            />
                            <XAxis
                                dataKey="month"
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 8, fill: '#242E2C', fontWeight: 400 }}
                                tickMargin={8}
                                scale="point"
                                padding={{ left: 20, right: 20 }}
                                tickFormatter={(value) => value}
                                className="sm:text-[10px]"
                            />
                            <YAxis
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 8, fill: '#242E2C', fontWeight: 400 }}
                                domain={[0, (dataMax: number) => Math.ceil(dataMax)]}
                                width={30}
                                tickMargin={8}
                                scale="linear"
                                className="sm:text-[10px] sm:w-[40px]"
                            />
                            <ChartTooltip
                                cursor={false}
                                content={<ChartTooltipContent indicator="line" />}
                            />
                            <Area
                                type="monotone"
                                dataKey="cases"
                                stroke="#BBF49C"
                                strokeWidth={2}
                                fill="url(#colorCases)"
                                activeDot={{ r: 5, stroke: "#BBF49C", strokeWidth: 2, fill: "#BBF49C" }}
                            />
                        </AreaChart>
                    </ChartContainer>
                    )}
                </CardContent>
            </Card>

            {/* Case Status Distribution */}
            <Card className="lg:col-span-2">
                <CardHeader className="flex flex-row items-center justify-between gap-2 sm:gap-0">
                    <div>
                        <CardTitle className="text-base sm:text-lg">Case Status Distribution</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                        <ArrowDownToLine className="w-4 h-4 text-[#6B7280]" />
                        <MoreVertical className="w-4 h-4 text-[#6B7280]" />
                    </div>
                </CardHeader>
                <CardContent>
                    {!hasStatusData ? (
                        <div className="h-48 sm:h-56 md:h-66 min-h-[200px] flex flex-col items-center justify-center text-center">
                            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                </svg>
                            </div>
                            <h3 className="text-sm font-medium text-gray-900 mb-1">No status data available</h3>
                            <p className="text-xs text-gray-500">Case status distribution will appear here</p>
                        </div>
                    ) : (
                        <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between gap-4 xl:gap-10 xl:pl-6">
                            <div className="w-full flex justify-center">
                                <ChartContainer
                                config={{
                                    new: {
                                        label: "New",
                                        color: "#BBF49C",
                                    },
                                    inProgress: {
                                        label: "In Progress",
                                        color: "#1E4841",
                                    },
                                    underReview: {
                                        label: "Under Review",
                                        color: "#ECF4E9",
                                    },
                                    resolved: {
                                        label: "Resolved",
                                        color: "#6B7280",
                                    },
                                    closed: {
                                        label: "Closed",
                                        color: "#E5E6E6",
                                    },
                                }}
                                className="w-48 h-48 sm:w-56 sm:h-56 md:w-70 md:h-66 min-h-[200px]"
                            >
                                <PieChart
                                    margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                                >
                                    <ChartTooltip content={<ChartTooltipContent />} />
                                    <Pie
                                        data={chartData.statusDistribution}
                                        cx="50%"
                                        cy="50%"
                                        innerRadius={50}
                                        outerRadius={90}
                                        startAngle={175}
                                        endAngle={535}
                                        paddingAngle={6}
                                        dataKey="value"
                                        cornerRadius={4}
                                        className="sm:inner-radius-[60px] sm:outer-radius-[100px] md:inner-radius-[70px] md:outer-radius-[120px]"
                                    >
                                        {chartData.statusDistribution.map((entry, index: number) => (
                                            <Cell
                                                key={`cell-${index}`}
                                                fill={entry.fill}
                                                radius={4}
                                            />
                                        ))}
                                    </Pie>
                                </PieChart>
                            </ChartContainer>
                        </div>
                        <div className="grid grid-cols-2 xl:flex xl:flex-col gap-2 w-full max-w-md">
                            {chartData.statusDistribution.map((item, index: number) => (
                                <div key={index} className="flex items-center gap-2">
                                    <div
                                        className="w-6 h-3 sm:w-8 sm:h-4 rounded-sm"
                                        style={{ backgroundColor: item.fill }}
                                    ></div>
                                    <span className="text-xs sm:text-sm text-gray-600">{item.name}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}