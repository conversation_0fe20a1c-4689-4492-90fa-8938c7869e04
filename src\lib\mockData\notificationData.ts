// Notification and language-related mock data
import { Notification, Language } from "@/lib/types";

// Notifications Data
export const notificationsData: Notification[] = [
    {
        _id: "notif-001",
        userId: "user_001",
        type: "message",
        title: "New Secure Message",
        message: "Compliance Team has sent you a new secure message regarding Case #WB-2025-0428",
        status: "unread",
        priority: "high",
        actionUrl: "/dashboard/secure-message",
        reportId: "report_001",
        createdAt: new Date("2025-01-15T09:22:00Z"),
        updatedAt: new Date("2025-01-15T09:22:00Z")
    },
    {
        _id: "notif-001a",
        userId: "user_001",
        type: "message",
        title: "Message from <PERSON>",
        message: "<PERSON> has responded to your secure message in Case #WB-2025-0402",
        status: "unread",
        priority: "medium",
        actionUrl: "/dashboard/secure-message",
        reportId: "report_003",
        createdAt: new Date("2025-01-15T08:15:00Z"),
        updatedAt: new Date("2025-01-15T08:15:00Z")
    },
    {
        _id: "notif-002",
        userId: "user_001",
        type: "report_update",
        title: "Report Status Updated",
        message: "Your report WB-2025-0011 status has been changed to 'Under Review'",
        status: "unread",
        priority: "medium",
        actionUrl: "/dashboard/reports/WB-2025-0011",
        reportId: "report_002",
        createdAt: new Date("2025-01-14T15:30:00Z"),
        updatedAt: new Date("2025-01-14T15:30:00Z")
    },
    {
        _id: "notif-003",
        userId: "user_001",
        type: "system",
        title: "System Maintenance Scheduled",
        message: "Scheduled maintenance on January 20th from 2:00 AM to 4:00 AM UTC",
        status: "read",
        priority: "low",
        actionUrl: "/dashboard/announcements",
        createdAt: new Date("2025-01-13T10:00:00Z"),
        updatedAt: new Date("2025-01-13T10:00:00Z")
    },
    {
        _id: "notif-004",
        userId: "user_001",
        type: "alert",
        title: "Action Required",
        message: "Please provide additional information for report WB-2025-0010",
        status: "unread",
        priority: "urgent",
        actionUrl: "/dashboard/reports/WB-2025-0010",
        reportId: "report_003",
        createdAt: new Date("2025-01-12T14:45:00Z"),
        updatedAt: new Date("2025-01-12T14:45:00Z")
    },
    {
        _id: "notif-005",
        userId: "user_001",
        type: "report_update",
        title: "Investigation Completed",
        message: "Investigation for report WB-2025-0008 has been completed",
        status: "read",
        priority: "medium",
        actionUrl: "/dashboard/reports/WB-2025-0008",
        reportId: "report_004",
        createdAt: new Date("2025-01-10T11:20:00Z"),
        updatedAt: new Date("2025-01-10T11:20:00Z")
    }
];

// Language Options Data
export const languageOptions: Language[] = [
    {
        code: "en",
        name: "English",
        nativeName: "English",
        flag: "🇺🇸"
    },
    {
        code: "de",
        name: "German",
        nativeName: "Deutsch",
        flag: "🇩🇪"
    },
    {
        code: "es",
        name: "Spanish",
        nativeName: "Español",
        flag: "🇪🇸"
    },
    {
        code: "fr",
        name: "French",
        nativeName: "Français",
        flag: "🇫🇷"
    },
    {
        code: "it",
        name: "Italian",
        nativeName: "Italiano",
        flag: "🇮🇹"
    }
];
