"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Wifi, 
  WifiOff, 
  Users, 
  Bell, 
  Clock, 
  Activity,
  CheckCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { sessionManager } from '@/lib/utils/sessionManager';
import { getRealTimeNotificationSystem } from '@/lib/utils/realTimeNotificationSystem';

export default function RealTimeStatusDemo() {
  const { getLoginTimeFormatted, getSessionDuration, getPreviousLoginFormatted } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);
  const [notifications, setNotifications] = useState<Array<{ 
    id: string; 
    title: string;
    message: string; 
    timestamp: Date; 
    status: string;
    isRealTime: boolean;
    priority: string;
  }>>([]);
  const [lastActivity, setLastActivity] = useState<Date>(new Date());
  const [connectionAttempts, setConnectionAttempts] = useState(0);

  // Simulate connection status
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate connection changes
      const shouldBeConnected = Math.random() > 0.1; // 90% uptime
      setIsConnected(shouldBeConnected);
      
      if (shouldBeConnected) {
        setConnectionAttempts(0);
        // Simulate online users
        const mockUsers = ['user1', 'user2', 'user3', 'admin1'];
        const randomOnlineUsers = mockUsers.filter(() => Math.random() > 0.3);
        setOnlineUsers(randomOnlineUsers);
      } else {
        setConnectionAttempts(prev => prev + 1);
        setOnlineUsers([]);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Subscribe to notifications
  useEffect(() => {
    const notificationSystem = getRealTimeNotificationSystem();
    const unsubscribe = notificationSystem.subscribe((realTimeNotifications) => {
      // Transform RealTimeNotification to our local format
      const transformedNotifications = realTimeNotifications.map(n => ({
        id: n._id || 'unknown',
        title: n.title || 'Notification',
        message: n.message || 'No message',
        timestamp: n.createdAt || new Date(),
        status: n.status || 'unread',
        isRealTime: n.isRealTime || false,
        priority: n.priority || 'medium'
      }));
      setNotifications(transformedNotifications);
    });
    return unsubscribe;
  }, []);

  // Simulate activity tracking
  useEffect(() => {
    const handleActivity = () => {
      setLastActivity(new Date());
      sessionManager.updateActivity();
    };

    const events = ['mousedown', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity);
      });
    };
  }, []);

  // Simulate real-time notifications
  const simulateNotification = () => {
    const mockNotifications = [
      {
        _id: `notif_${Date.now()}`,
        title: 'New Message',
        message: 'You have received a new secure message from the compliance team.',
        type: 'message' as const,
        priority: 'high' as const,
        status: 'unread' as const,
        userId: 'demo-user',
        createdAt: new Date(),
        isRealTime: true
      },
      {
        _id: `notif_${Date.now() + 1}`,
        title: 'Report Update',
        message: 'Your report #WB-2025-0428 has been reviewed and updated.',
        type: 'report_update' as const,
        priority: 'medium' as const,
        status: 'unread' as const,
        userId: 'demo-user',
        createdAt: new Date(),
        isRealTime: true
      },
      {
        _id: `notif_${Date.now() + 2}`,
        title: 'System Alert',
        message: 'Scheduled maintenance will begin in 30 minutes.',
        type: 'system' as const,
        priority: 'urgent' as const,
        status: 'unread' as const,
        userId: 'demo-user',
        createdAt: new Date(),
        isRealTime: true
      }
    ];

    const randomNotification = mockNotifications[Math.floor(Math.random() * mockNotifications.length)];
    getRealTimeNotificationSystem().addNotification(randomNotification);
  };

  const getTimeSince = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (minutes > 0) return `${minutes}m ago`;
    return `${seconds}s ago`;
  };

  const unreadCount = notifications.filter(n => n.status === 'unread').length;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Connection Status */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connection</CardTitle>
            {isConnected ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Badge variant={isConnected ? "default" : "destructive"}>
                {isConnected ? "Online" : "Offline"}
              </Badge>
              {!isConnected && connectionAttempts > 0 && (
                <p className="text-xs text-muted-foreground">
                  Reconnect attempts: {connectionAttempts}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Online Users */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online Users</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{onlineUsers.length}</div>
            <p className="text-xs text-muted-foreground">
              {onlineUsers.length > 0 ? 'Users active' : 'No users online'}
            </p>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Notifications</CardTitle>
            <Bell className="h-4 w-4 text-amber-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{unreadCount}</div>
            <p className="text-xs text-muted-foreground">
              Unread messages
            </p>
          </CardContent>
        </Card>

        {/* Session Info */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Session</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-sm font-bold">{getSessionDuration()}</div>
            <p className="text-xs text-muted-foreground">
              Active time
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Login Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Login Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Current Login:</span>
                <span className="text-sm font-medium">{getLoginTimeFormatted()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Previous Login:</span>
                <span className="text-sm font-medium">{getPreviousLoginFormatted()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Session Duration:</span>
                <span className="text-sm font-medium">{getSessionDuration()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Last Activity:</span>
                <span className="text-sm font-medium">{getTimeSince(lastActivity)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Real-time Features Demo */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Real-time Features
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {isConnected ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  <span className="text-sm">WebSocket Connection</span>
                </div>
                <Badge variant={isConnected ? "default" : "destructive"}>
                  {isConnected ? "Active" : "Disconnected"}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">User Status Tracking</span>
                </div>
                <Badge variant="default">
                  {onlineUsers.length} Online
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Bell className="h-4 w-4 text-amber-600" />
                  <span className="text-sm">Live Notifications</span>
                </div>
                <Badge variant={unreadCount > 0 ? "destructive" : "secondary"}>
                  {unreadCount} Unread
                </Badge>
              </div>

              <div className="pt-2">
                <Button 
                  onClick={simulateNotification}
                  variant="outline" 
                  size="sm"
                  className="w-full"
                >
                  Simulate Real-time Notification
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Notifications */}
      {notifications.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {notifications.slice(0, 5).map((notification) => (
                <div 
                  key={notification.id} 
                  className="flex items-start gap-3 p-3 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="text-sm font-medium">{notification.title}</h4>
                      {notification.isRealTime && (
                        <Badge variant="secondary" className="text-xs">
                          Real-time
                        </Badge>
                      )}
                      <Badge 
                        variant={
                          notification.priority === 'urgent' ? 'destructive' :
                          notification.priority === 'high' ? 'default' : 'secondary'
                        }
                        className="text-xs"
                      >
                        {notification.priority}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {notification.message}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {getTimeSince(notification.timestamp)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}