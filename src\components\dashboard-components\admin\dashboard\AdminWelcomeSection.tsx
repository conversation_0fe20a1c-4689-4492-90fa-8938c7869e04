"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Download, Calendar } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

export default function AdminWelcomeSection() {
    const { user, getPreviousLoginFormatted, getSessionDuration } = useAuth();
    return (
        <Card className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div className="space-y-2">
                        <h1 className="text-2xl font-bold text-gray-900">
                            Welcome back, {user?.name || 'Admin'}!
                        </h1>
                        <p className="text-gray-600">
                            Stay informed with live metrics on report status, team activity, and critical alerts
                        </p>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-3">
                        <div className="flex flex-col gap-2 text-sm text-gray-600">
                            <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                <span>Last login: {getPreviousLoginFormatted()}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                <span>Session: {getSessionDuration()}</span>
                            </div>
                        </div>
                        <Button
                            variant="outline"
                            className="flex items-center gap-2 hover:bg-[#BBF49C] hover:border-[#BBF49C] transition-colors"
                        >
                            <Download className="h-4 w-4" />
                            Export Report
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}