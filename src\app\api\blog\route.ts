import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const featured = searchParams.get('featured') === 'true';
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');
    
    const blogs = await DataService.getBlogPosts(featured, limit, offset);
    const total = blogs.length;
    
    return NextResponse.json({
      success: true,
      data: blogs,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    });
  } catch (error) {
    console.error('Blog API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}