import mongoose from 'mongoose';

const PricingPlanSchema = new mongoose.Schema({
  name: { type: String, required: true },
  price: { type: Number, required: true },
  features: [{ type: String }],
  order: { type: Number, default: 0 },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

export default mongoose.models?.PricingPlan || mongoose.model('PricingPlan', PricingPlanSchema);