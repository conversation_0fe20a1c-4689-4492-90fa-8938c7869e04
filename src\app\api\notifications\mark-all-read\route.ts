import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const PUT = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const { userId } = await request.json();
    const targetUserId = userId || request.user!.id;
    
    const result = await DataService.markAllNotificationsAsRead(targetUserId);
    
    return NextResponse.json({
      success: true,
      data: result,
      message: 'All notifications marked as read successfully'
    });
  } catch (error) {
    console.error('Mark All Notifications as Read API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});