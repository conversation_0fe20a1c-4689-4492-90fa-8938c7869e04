import { config } from 'dotenv';
import connectDB from '../src/lib/db/mongodb';
import { Company, User, Report, Conversation, Message, Notification } from '../src/lib/db/models';

config({ path: '.env.local' });

async function verifyDatabase() {
  try {
    await connectDB();
    console.log('🔍 Verifying database structure...\n');

    // Get counts
    const [
      companyCount,
      userCount,
      reportCount,
      conversationCount,
      messageCount,
      notificationCount
    ] = await Promise.all([
      Company.countDocuments(),
      User.countDocuments(),
      Report.countDocuments(),
      Conversation.countDocuments(),
      Message.countDocuments(),
      Notification.countDocuments()
    ]);

    console.log('📊 Data Summary:');
    console.log('='.repeat(40));
    console.log(`Companies: ${companyCount}`);
    console.log(`Users: ${userCount}`);
    console.log(`Reports: ${reportCount}`);
    console.log(`Conversations: ${conversationCount}`);
    console.log(`Messages: ${messageCount}`);
    console.log(`Notifications: ${notificationCount}\n`);

    // Verify companies
    console.log('🏢 Companies:');
    const companies = await Company.find({}, 'name industry size').lean();
    companies.forEach((company: any, index) => {
      console.log(`  ${index + 1}. ${company.name} (${company.industry}, ${company.size})`);
    });
    console.log();

    // Verify users by role and company
    console.log('👥 Users by Role and Company:');
    const users = await User.find({}, 'firstName lastName email role companyId')
      .populate('companyId', 'name')
      .lean();
    
    const usersByRole = users.reduce((acc: any, user: any) => {
      if (!acc[user.role]) acc[user.role] = [];
      acc[user.role].push(user);
      return acc;
    }, {});

    Object.keys(usersByRole).forEach(role => {
      console.log(`  ${role.toUpperCase()}S:`);
      usersByRole[role].forEach((user: any) => {
        const companyName = user.companyId?.name || 'No Company';
        console.log(`    - ${user.firstName} ${user.lastName} (${user.email}) - ${companyName}`);
      });
      console.log();
    });

    // Verify reports and their relationships
    console.log('📋 Reports and Workflow:');
    const reports = await Report.find({})
      .populate('userId', 'firstName lastName email')
      .populate('assignedInvestigator', 'firstName lastName email')
      .populate('companyId', 'name')
      .lean();

    for (const report of reports as any[]) {
      console.log(`  📄 ${report.title}`);
      console.log(`     ID: ${report.reportId}`);
      console.log(`     Status: ${report.status} | Priority: ${report.priority}`);
      console.log(`     Whistleblower: ${report.userId?.firstName} ${report.userId?.lastName} (${report.userId?.email})`);
      console.log(`     Investigator: ${report.assignedInvestigator?.firstName || 'Unassigned'} ${report.assignedInvestigator?.lastName || ''}`);
      console.log(`     Company: ${report.companyId?.name}`);
      
      // Check conversation
      const conversation = await Conversation.findOne({ reportId: report._id })
        .populate('participants', 'firstName lastName email role')
        .lean();
      
      if (conversation) {
        console.log(`     💬 Conversation: ${((conversation as any).participants || []).length} participants`);
        
        // Check messages
        const messageCount = await Message.countDocuments({ conversationId: conversation._id });
        console.log(`     📨 Messages: ${messageCount}`);
      }
      
      // Check notifications
      const notificationCount = await Notification.countDocuments({ reportId: report._id });
      console.log(`     🔔 Notifications: ${notificationCount}`);
      console.log();
    }

    // Verify data consistency
    console.log('⚡ Data Consistency Verification:');
    console.log('='.repeat(40));
    
    // Check if all reports have conversations
    const reportsWithoutConversations = await Report.find({
      _id: { $nin: await Conversation.distinct('reportId') }
    });
    
    if (reportsWithoutConversations.length === 0) {
      console.log('✅ All reports have conversations');
    } else {
      console.log(`❌ ${reportsWithoutConversations.length} reports missing conversations`);
    }

    // Check if all conversations have messages
    const conversationsWithoutMessages = await Conversation.find({
      _id: { $nin: await Message.distinct('conversationId') }
    });
    
    if (conversationsWithoutMessages.length === 0) {
      console.log('✅ All conversations have messages');
    } else {
      console.log(`❌ ${conversationsWithoutMessages.length} conversations missing messages`);
    }

    // Check user-company consistency
    const usersWithInvalidCompanies = await User.find({
      companyId: { $nin: await Company.distinct('_id') }
    });
    
    if (usersWithInvalidCompanies.length === 0) {
      console.log('✅ All users have valid company associations');
    } else {
      console.log(`❌ ${usersWithInvalidCompanies.length} users have invalid company associations`);
    }

    // Check notification consistency
    const notificationsWithInvalidUsers = await Notification.find({
      userId: { $nin: await User.distinct('_id') }
    });
    
    if (notificationsWithInvalidUsers.length === 0) {
      console.log('✅ All notifications have valid user associations');
    } else {
      console.log(`❌ ${notificationsWithInvalidUsers.length} notifications have invalid user associations`);
    }

    console.log('\n🎉 Database verification completed!');
    
    // Show login credentials
    console.log('\n🔑 Login Credentials:');
    console.log('='.repeat(40));
    console.log('TechCorp Industries:');
    console.log('  Admin: <EMAIL> / admin123');
    console.log('  Investigator: <EMAIL> / investigator123');
    console.log('  Whistleblower: <EMAIL> / employee123');
    console.log('\nGlobal Manufacturing Co:');
    console.log('  Admin: <EMAIL> / admin123');
    console.log('  Investigator: <EMAIL> / investigator123');
    
    return true;
  } catch (error) {
    console.error('❌ Database verification failed:', error);
    return false;
  }
}

// Run verification if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  verifyDatabase()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Verification failed:', error);
      process.exit(1);
    });
}

export { verifyDatabase };