import mongoose, { Model, Schema } from 'mongoose';

/**
 * Helper function to create Mongoose models safely
 * This prevents errors when models are accessed in middleware or edge runtime
 * 
 * @param modelName The name of the model
 * @param schema The mongoose schema
 * @returns The mongoose model
 */
export function createModel<T>(modelName: string, schema: Schema): Model<T> {
  let model: Model<T>;
  
  try {
    // Try to get existing model or create a new one
    model = (mongoose.models[modelName] as Model<T>) || mongoose.model<T>(modelName, schema);
  } catch {
    // If error occurs during model access, create it 
    model = mongoose.model<T>(modelName, schema);
  }
  
  return model;
}
