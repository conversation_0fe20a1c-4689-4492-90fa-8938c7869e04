"use client";

import React from "react";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Calendar as CalendarIcon,
  X,
  ListFilter,
  Settings
} from "lucide-react";
import { toast } from "sonner";

interface ReportsFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  priorityFilter: string;
  onPriorityChange: (value: string) => void;
  categoryFilter: string;
  onCategoryChange: (value: string) => void;
  fromDate?: Date;
  onFromDateChange: (date: Date | undefined) => void;
  toDate?: Date;
  onToDateChange: (date: Date | undefined) => void;
  dateValidationError: string | null;
  categories: string[];
  priorities: string[];
  hasActiveFilters: boolean;
  activeFilters: Array<{ type: string; label: string; value: string | Date | undefined }>;
  onRemoveFilter: (filterType: string) => void;
  onClearAllFilters: () => void;
}

export default function ReportsFilters({
  searchTerm,
  onSearchChange,
  priorityFilter,
  onPriorityChange,
  categoryFilter,
  onCategoryChange,
  fromDate,
  onFromDateChange,
  toDate,
  onToDateChange,
  dateValidationError,
  categories,
  priorities,
  hasActiveFilters,
  activeFilters,
  onRemoveFilter,
  onClearAllFilters
}: ReportsFiltersProps) {
  return (
    <>
      <div className="w-full p-4 sm:p-6 flex gap-4 lg:gap-6 bg-white">
        <div className="relative w-full flex-1/2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search by report ID, title, or keywords..."
            className="pl-10 py-4 sm:py-5"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
        <div className="flex flex-1/2 flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 flex-1">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full sm:w-42 py-4 sm:py-5 justify-between text-left font-normal">
                  {fromDate ? format(fromDate, "PPP") : <span>From Date</span>}
                  <CalendarIcon className="ml-2 h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={fromDate}
                  onSelect={onFromDateChange}
                  disabled={(date) => {
                    const today = new Date();
                    today.setHours(23, 59, 59, 999);
                    return date > today || (toDate ? date > toDate : false);
                  }}
                  autoFocus
                />
              </PopoverContent>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full sm:w-42 py-4 sm:py-5 justify-between text-left font-normal">
                  {toDate ? format(toDate, "PPP") : <span>To Date</span>}
                  <CalendarIcon className="ml-2 h-4 w-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={toDate}
                  onSelect={onToDateChange}
                  disabled={(date) => {
                    const today = new Date();
                    today.setHours(23, 59, 59, 999);
                    return date > today || (fromDate ? date < fromDate : false);
                  }}
                  autoFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-2">
            <Select 
              value={priorityFilter} 
              onValueChange={onPriorityChange}
            >
              <SelectTrigger className="w-full sm:w-42 py-4 sm:py-5">
                <SelectValue placeholder="All Priorities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                {priorities?.map(priority => (
                  <SelectItem key={priority} value={priority}>{priority}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select 
              value={categoryFilter} 
              onValueChange={onCategoryChange}
            >
              <SelectTrigger className="w-full sm:w-42 py-4 sm:py-5">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories?.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex gap-2">
              <Button variant="outline" className="p-4 sm:p-5 flex-1 sm:flex-none" onClick={() => toast.info('Advanced filters will be implemented')}>
                <ListFilter className="w-6 h-6 sm:w-8 sm:h-8" />
              </Button>
              <Button variant="outline" className="p-4 sm:p-5 flex-1 sm:flex-none" onClick={() => toast.info('Table settings will be implemented')}>
                <Settings className="w-6 h-6 sm:w-8 sm:h-8" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {hasActiveFilters && (
        <div className="w-full flex flex-col sm:flex-row sm:items-center gap-3 px-4 sm:px-6 py-4 sm:py-6 -mt-6 sm:-mt-8 bg-white">
          <p className="text-sm font-normal text-[#6B7280] flex-shrink-0">Active Filters:</p>
          <div className="flex flex-wrap items-center gap-2">
            {activeFilters.map((filter, index) => (
              <Badge key={index} variant="secondary" className="bg-[#F3F4F6] text-[#4B5563] hover:bg-[#F3F4F6]/60 px-3 py-1 text-xs font-semibold flex items-center gap-2">
                {filter.label}
                <Button onClick={() => onRemoveFilter(filter.type)} className="ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors" variant="ghost" size="icon">
                  <X className="w-3 h-3" />
                </Button>
              </Badge>
            ))}
            <Button variant="ghost" size="sm" onClick={onClearAllFilters} className="text-[#1E4841] hover:text-[#2A5D54] hover:bg-[#1E4841]/5 px-3 py-1 h-auto text-xs">
              Clear All Filters
            </Button>
          </div>
        </div>
      )}

      {dateValidationError && (
        <div className="mx-4 sm:mx-6 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 001.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{dateValidationError}</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
