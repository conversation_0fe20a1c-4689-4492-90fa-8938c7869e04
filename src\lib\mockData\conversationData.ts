// Mock conversation data for secure messaging
import { ConversationData, MessageData } from "@/lib/types";
import logger from "@/lib/utils/logger";

export const mockConversationData: ConversationData[] = [
    {
        id: "conv_001",
        name: "Compliance Team",
        caseId: "Case #WB-2025-0428",
        time: "10:24 AM",
        lastMessage: "Thank you for your detailed report. We need additional information...",
        isUnread: true,
        isActive: false,
        isOnline: true,
        isTyping: false,
        avatarBg: "bg-[#BBF49C]",
        participantType: "team"
    },
    {
        id: "conv_002",
        name: "Ethics Committee",
        caseId: "Case #WB-2025-0315",
        time: "9:15 AM",
        lastMessage: "Your report has been reviewed and assigned to our investigation team...",
        isUnread: true,
        isActive: false,
        isOnline: false,
        isTyping: false,
        avatarBg: "bg-[#DCFCE7]",
        participantType: "committee"
    },
    {
        id: "conv_003",
        name: "<PERSON>",
        caseId: "Case #WB-2025-0402",
        time: "Yesterday",
        lastMessage: "I've attached the requested documentation for your review...",
        isUnread: false,
        isActive: false,
        isOnline: true,
        isTyping: false,
        avatarBg: "bg-[#F3E8FF]",
        participantType: "investigator"
    },
    {
        id: "conv_004",
        name: "Security Officer",
        caseId: "Case #WB-2025-0410",
        time: "Apr 12",
        lastMessage: "We've implemented additional security measures as discussed...",
        isUnread: false,
        isActive: false,
        isOnline: false,
        isTyping: false,
        avatarBg: "bg-[#FEF3C7]",
        participantType: "officer"
    },
    {
        id: "conv_005",
        name: "System Administrator",
        caseId: "System Notice",
        time: "Apr 10",
        lastMessage: "Important: Security update for the messaging system has been completed...",
        isUnread: false,
        isActive: false,
        isOnline: true,
        isTyping: false,
        avatarBg: "bg-[#FEE2E2]",
        participantType: "admin"
    }
];

// Helper function to get conversation by ID
export const getConversationById = (id: string): ConversationData | undefined => {
    return mockConversationData.find(conv => conv.id === id);
};

// Helper function to get unread count
export const getUnreadCount = (): number => {
    const unreadCount = mockConversationData.filter(conv => conv.isUnread).length;
    console.log('Current unread count:', unreadCount, 'Unread conversations:', mockConversationData.filter(conv => conv.isUnread).map(c => c.id));
    return unreadCount;
};

// Helper function to mark conversation as read
export const markConversationAsRead = (conversationId: string): void => {
    const conversationIndex = mockConversationData.findIndex(conv => conv.id === conversationId);
    if (conversationIndex !== -1) {
        mockConversationData[conversationIndex].isUnread = false;

        // Notify message count update listeners
        import("@/lib/utils/messageIndicators").then(({ notifyMessageCountUpdate }) => {
            notifyMessageCountUpdate();
        });
    }
};

// Helper function to get online conversations
export const getOnlineConversations = (): ConversationData[] => {
    return mockConversationData.filter(conv => conv.isOnline);
};

// Mock message data for conversations

export const mockMessageData: MessageData[] = [
    // Conversation 1: Compliance Team - Financial irregularities case
    {
        id: "msg_001",
        conversationId: "conv_001",
        senderId: "team_001",
        senderName: "Compliance Team",
        content: "Thank you for submitting your whistleblower report regarding potential financial irregularities in the Marketing department (Case #WB-2025-0428). We've reviewed your initial report and would like to request some additional information to help with our investigation. Could you please provide more details about the timeline and any supporting documentation?",
        timestamp: "10:24 AM",
        isFromUser: false,
        isEncrypted: true,
        isRead: true,
        messageType: "text"
    },
    {
        id: "msg_002",
        conversationId: "conv_001",
        senderId: "user_001",
        senderName: "You",
        content: "Thank you for your prompt response. I'm happy to provide the additional information. I first noticed discrepancies in the budget allocation reports in early March 2025, specifically around March 5-10. I have several spreadsheets and copies of invoices that show the inconsistencies.",
        timestamp: "10:45 AM",
        isFromUser: true,
        isEncrypted: true,
        isRead: true,
        messageType: "text"
    },
    {
        id: "msg_003",
        conversationId: "conv_001",
        senderId: "user_001",
        senderName: "You",
        content: "I've attached the budget analysis spreadsheet for your review.",
        timestamp: "10:46 AM",
        isFromUser: true,
        isEncrypted: true,
        isRead: true,
        messageType: "file",
        attachments: [
            {
                fileName: "Budget_Analysis.xlsx",
                fileSize: "1.2 MB",
                fileType: "Excel Spreadsheet"
            }
        ]
    },

    // Conversation 2: Ethics Committee - Workplace harassment case
    {
        id: "msg_004",
        conversationId: "conv_002",
        senderId: "committee_001",
        senderName: "Ethics Committee",
        content: "Your report regarding workplace harassment has been received and assigned to our investigation team (Case #WB-2025-0315). We take these matters very seriously and will conduct a thorough investigation. An investigator will be in touch within 48 hours to discuss next steps.",
        timestamp: "9:15 AM",
        isFromUser: false,
        isEncrypted: true,
        isRead: true,
        messageType: "text"
    },
    {
        id: "msg_005",
        conversationId: "conv_002",
        senderId: "user_001",
        senderName: "You",
        content: "Thank you for the quick response. I appreciate the committee taking this seriously. I have additional witnesses who may be willing to provide statements if needed.",
        timestamp: "9:32 AM",
        isFromUser: true,
        isEncrypted: true,
        isRead: true,
        messageType: "text"
    },

    // Conversation 3: Sarah Martinez - Safety violations case
    {
        id: "msg_006",
        conversationId: "conv_003",
        senderId: "investigator_001",
        senderName: "Sarah Martinez",
        content: "Hello, I'm the lead investigator assigned to your safety violations report (Case #WB-2025-0402). I've reviewed the initial documentation you provided. Could you please provide more specific details about the safety equipment that was allegedly tampered with?",
        timestamp: "Yesterday",
        isFromUser: false,
        isEncrypted: true,
        isRead: true,
        messageType: "text"
    },
    {
        id: "msg_007",
        conversationId: "conv_003",
        senderId: "user_001",
        senderName: "You",
        content: "Hi Sarah, thank you for reaching out. The main issues were with the emergency exit locks being disabled and fire extinguishers being moved from their designated locations. I have photos and can provide witness contact information.",
        timestamp: "Yesterday",
        isFromUser: true,
        isEncrypted: true,
        isRead: true,
        messageType: "text"
    },
    {
        id: "msg_008",
        conversationId: "conv_003",
        senderId: "user_001",
        senderName: "You",
        content: "I've attached the requested documentation for your review.",
        timestamp: "Yesterday",
        isFromUser: true,
        isEncrypted: true,
        isRead: true,
        messageType: "file",
        attachments: [
            {
                fileName: "Safety_Evidence.pdf",
                fileSize: "2.8 MB",
                fileType: "PDF Document"
            }
        ]
    },

    // Conversation 4: Security Officer - Data breach case
    {
        id: "msg_009",
        conversationId: "conv_004",
        senderId: "security_001",
        senderName: "Security Officer",
        content: "Thank you for reporting the potential data breach incident (Case #WB-2025-0410). We've implemented additional security measures as discussed and are conducting a full audit of our systems. Your vigilance helped prevent a more serious security incident.",
        timestamp: "Apr 12",
        isFromUser: false,
        isEncrypted: true,
        isRead: true,
        messageType: "text"
    },
    {
        id: "msg_010",
        conversationId: "conv_004",
        senderId: "user_001",
        senderName: "You",
        content: "I'm glad the security measures are in place. Should I continue monitoring the systems for any unusual activity, or has the IT team taken over that responsibility?",
        timestamp: "Apr 12",
        isFromUser: true,
        isEncrypted: true,
        isRead: true,
        messageType: "text"
    },

    // Conversation 5: System Administrator - System update notice
    {
        id: "msg_011",
        conversationId: "conv_005",
        senderId: "admin_001",
        senderName: "System Administrator",
        content: "Important: Security update for the messaging system has been completed. All conversations now use enhanced end-to-end encryption. Please log out and log back in to ensure you're using the updated security protocols.",
        timestamp: "Apr 10",
        isFromUser: false,
        isEncrypted: true,
        isRead: true,
        messageType: "system"
    },
    {
        id: "msg_012",
        conversationId: "conv_005",
        senderId: "user_001",
        senderName: "You",
        content: "Thank you for the update. I've logged out and back in as requested. The new security features are working well.",
        timestamp: "Apr 10",
        isFromUser: true,
        isEncrypted: true,
        isRead: true,
        messageType: "text"
    }
];

// Helper function to get messages for a conversation
export const getMessagesByConversationId = (conversationId: string): MessageData[] => {
    return mockMessageData.filter(msg => msg.conversationId === conversationId);
};

// Archive/Delete Conversation helpers
export const archiveConversation = (conversationId: string): boolean => {
    const idx = mockConversationData.findIndex(conv => conv.id === conversationId);
    if (idx !== -1) {
        mockConversationData[idx].isActive = false;
        logger.info(`Conversation ${conversationId} archived`);
        return true;
    }
    return false;
};

export const deleteConversation = (conversationId: string): boolean => {
    const idx = mockConversationData.findIndex(conv => conv.id === conversationId);
    if (idx !== -1) {
        mockConversationData.splice(idx, 1);
        logger.info(`Conversation ${conversationId} deleted`);
        return true;
    }
    return false;
};

// Mark conversation as read in the persistent data
export const markConversationAsReadInData = (conversationId: string): boolean => {
    const conversation = mockConversationData.find(conv => conv.id === conversationId);
    if (conversation && conversation.isUnread) {
        conversation.isUnread = false;
        console.log(`✅ Conversation ${conversationId} marked as read in persistent data`);
        logger.info(`Conversation ${conversationId} marked as read in persistent data`);
        return true;
    } else if (conversation && !conversation.isUnread) {
        console.log(`ℹ️ Conversation ${conversationId} was already marked as read`);
        return false;
    } else {
        console.log(`❌ Conversation ${conversationId} not found`);
        return false;
    }
};
