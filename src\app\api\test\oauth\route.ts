import { NextResponse } from 'next/server';
import logger from '@/lib/utils/logger';

export async function GET() {
  try {
    // Check OAuth configuration
    const oauthConfig = {
      nextauth: {
        url: process.env.NEXTAUTH_URL,
        secret: process.env.NEXTAUTH_SECRET ? '***configured***' : 'missing',
      },
      google: {
        clientId: process.env.GOOGLE_CLIENT_ID ? '***configured***' : 'missing',
        clientSecret: process.env.GOOGLE_CLIENT_SECRET ? '***configured***' : 'missing',
      },
      microsoft: {
        clientId: process.env.MICROSOFT_CLIENT_ID ? '***configured***' : 'missing',
        clientSecret: process.env.MICROSOFT_CLIENT_SECRET ? '***configured***' : 'missing',
        tenantId: process.env.MICROSOFT_TENANT_ID ? '***configured***' : 'missing',
      }
    };

    // Check for missing configurations
    const missingConfigs = [];
    
    if (!process.env.NEXTAUTH_URL) missingConfigs.push('NEXTAUTH_URL');
    if (!process.env.NEXTAUTH_SECRET) missingConfigs.push('NEXTAUTH_SECRET');
    if (!process.env.GOOGLE_CLIENT_ID) missingConfigs.push('GOOGLE_CLIENT_ID');
    if (!process.env.GOOGLE_CLIENT_SECRET) missingConfigs.push('GOOGLE_CLIENT_SECRET');
    if (!process.env.MICROSOFT_CLIENT_ID) missingConfigs.push('MICROSOFT_CLIENT_ID');
    if (!process.env.MICROSOFT_CLIENT_SECRET) missingConfigs.push('MICROSOFT_CLIENT_SECRET');
    if (!process.env.MICROSOFT_TENANT_ID) missingConfigs.push('MICROSOFT_TENANT_ID');

    const isConfigured = missingConfigs.length === 0;

    // Test OAuth URLs
    const oauthUrls = {
      google: `${process.env.NEXTAUTH_URL}/api/auth/signin/google`,
      microsoft: `${process.env.NEXTAUTH_URL}/api/auth/signin/azure-ad`,
      callback: {
        google: `${process.env.NEXTAUTH_URL}/api/auth/callback/google`,
        microsoft: `${process.env.NEXTAUTH_URL}/api/auth/callback/azure-ad`,
      }
    };

    logger.info('OAuth configuration check', { isConfigured, missingConfigs });

    return NextResponse.json({
      success: isConfigured,
      message: isConfigured ? 'OAuth configuration is complete' : 'OAuth configuration is incomplete',
      config: oauthConfig,
      missingConfigs,
      urls: oauthUrls,
      recommendations: [
        'Ensure all OAuth credentials are set in environment variables',
        'Configure redirect URIs in Google Cloud Console and Azure Portal',
        'Test OAuth flow in development before production deployment',
        'Verify NEXTAUTH_URL matches your domain (including port for development)'
      ]
    });

  } catch (error) {
    logger.error('OAuth configuration check error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check OAuth configuration', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
