'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';

interface UnlockUserButtonProps {
  userId: string;
  onSuccess?: () => void;
}

export function UnlockUserButton({ userId, onSuccess }: UnlockUserButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleUnlock = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/users/unlock', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        toast({
          title: 'Account Unlocked',
          description: 'The user account has been successfully unlocked.',
        });
        
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to unlock account',
          variant: 'destructive',
        });
      }
    } catch {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleUnlock}
      disabled={isLoading}
    >
      {isLoading ? 'Unlocking...' : 'Unlock Account'}
    </Button>
  );
}