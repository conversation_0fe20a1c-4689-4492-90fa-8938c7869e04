"use client";

import { useEffect } from "react";
import Header from "@/components/dashboard-components/Header";
import { notificationSystem } from "@/lib/utils/notificationSystem";
import { notificationsData } from "@/lib/mockData/notificationData";
import AdminRecentCases from "@/components/dashboard-components/admin/dashboard/AdminRecentCases";
import AdminStatisticsCards from "@/components/dashboard-components/admin/dashboard/AdminStatisticsCards";

export default function CaseManagementPage() {
    // Initialize notification system
    useEffect(() => {
        notificationSystem.updateNotifications(notificationsData);
    }, []);

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Case Management"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">Case Management</h1>
                        <p className="text-gray-600">Manage and monitor all whistleblower cases</p>
                    </div>

                    {/* Statistics Cards */}
                    <section aria-labelledby="statistics-section">
                        <h2 id="statistics-section" className="sr-only">Case Statistics</h2>
                        <AdminStatisticsCards />
                    </section>

                    {/* Cases Table */}
                    <section aria-labelledby="cases-section">
                        <h2 id="cases-section" className="sr-only">All Cases</h2>
                        <AdminRecentCases />
                    </section>
                </main>
            </div>
        </>
    );
}