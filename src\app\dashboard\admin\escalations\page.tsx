"use client";

import { useEffect } from "react";
import Header from "@/components/dashboard-components/Header";
import { notificationSystem } from "@/lib/utils/notificationSystem";
import { notificationsData } from "@/lib/mockData/notificationData";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Clock, TrendingUp, AlertCircle } from "lucide-react";

export default function EscalationsPage() {
    // Initialize notification system
    useEffect(() => {
        notificationSystem.updateNotifications(notificationsData);
    }, []);

    const escalatedCases = [
        {
            id: "WB-2025-085",
            subject: "Health & safety violation",
            department: "Operations",
            priority: "High",
            escalatedDate: "May 30, 2025",
            slaStatus: "Breached",
            daysOverdue: 3
        },
        {
            id: "WB-2025-076",
            subject: "Financial misconduct",
            department: "Finance",
            priority: "Critical",
            escalatedDate: "May 28, 2025",
            slaStatus: "Breached",
            daysOverdue: 5
        }
    ];

    return (
        <>
            <div className="w-full h-full">
                <Header />
                <main
                    id="main-content"
                    className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
                    aria-label="Escalations & SLAs"
                >
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">Escalations & SLAs</h1>
                        <p className="text-gray-600">Monitor case escalations and SLA compliance</p>
                    </div>

                    {/* SLA Statistics */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">SLA Violations</CardTitle>
                                <AlertTriangle className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">12</div>
                                <p className="text-xs text-red-600">15% of cases</p>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Escalated Cases</CardTitle>
                                <TrendingUp className="h-4 w-4 text-orange-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">8</div>
                                <p className="text-xs text-orange-600">Requires attention</p>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Avg Response Time</CardTitle>
                                <Clock className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">2.4</div>
                                <p className="text-xs text-blue-600">days</p>
                            </CardContent>
                        </Card>

                        <Card className="bg-white border-0 shadow-sm">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">Critical Cases</CardTitle>
                                <AlertCircle className="h-4 w-4 text-purple-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold text-gray-900">4</div>
                                <p className="text-xs text-purple-600">Immediate action</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Escalated Cases */}
                    <Card className="bg-white border-0 shadow-sm">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-lg font-semibold text-gray-900">
                                    Escalated Cases
                                </CardTitle>
                                <Button variant="outline" size="sm" className="hover:bg-[#BBF49C] hover:border-[#BBF49C]">
                                    View All
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="overflow-x-auto">
                                <table className="w-full">
                                    <thead>
                                        <tr className="border-b border-gray-200">
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Case ID</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Subject</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Department</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Priority</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Escalated Date</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">SLA Status</th>
                                            <th className="text-left py-3 px-2 text-sm font-medium text-gray-600">Days Overdue</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {escalatedCases.map((case_item) => (
                                            <tr key={case_item.id} className="border-b border-gray-100 hover:bg-gray-50">
                                                <td className="py-3 px-2">
                                                    <span className="font-medium text-sm text-gray-900">#{case_item.id}</span>
                                                </td>
                                                <td className="py-3 px-2">
                                                    <span className="text-sm text-gray-900">{case_item.subject}</span>
                                                </td>
                                                <td className="py-3 px-2">
                                                    <span className="text-sm text-gray-600">{case_item.department}</span>
                                                </td>
                                                <td className="py-3 px-2">
                                                    <Badge variant="secondary" className="text-xs bg-red-100 text-red-800">
                                                        {case_item.priority}
                                                    </Badge>
                                                </td>
                                                <td className="py-3 px-2">
                                                    <span className="text-sm text-gray-600">{case_item.escalatedDate}</span>
                                                </td>
                                                <td className="py-3 px-2">
                                                    <Badge variant="secondary" className="text-xs bg-red-100 text-red-800">
                                                        {case_item.slaStatus}
                                                    </Badge>
                                                </td>
                                                <td className="py-3 px-2">
                                                    <span className="text-sm font-medium text-red-600">
                                                        {case_item.daysOverdue} days
                                                    </span>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                </main>
            </div>
        </>
    );
}