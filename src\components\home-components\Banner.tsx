"use client";

import Image from "next/image";
import { COMPANY_LOGOS } from "@/lib/mockData";

export default function Banner() {
    const renderCompanyLogos = () => (
        COMPANY_LOGOS.map((logo, index) => (
            <figure key={index} className="flex flex-col items-center">
                <Image
                    src={logo.src}
                    alt={logo.alt}
                    width={logo.width}
                    height={logo.height}
                    className="w-[100px] lg:w-[120px] xl:w-[115px] h-auto"
                />
                <figcaption className="sr-only">{logo.alt}</figcaption>
            </figure>
        ))
    );

    return (
        <section aria-labelledby="trusted-companies" className={`flex flex-col xl:flex-row items-center justify-between gap-6 sm:gap-4 flex-wrap bg-[#F8FEF4] w-full px-4 sm:px-8 md:px-16 lg:px-44 py-8`}>
            <h2 id="trusted-companies" className="w-full xl:w-1/4 text-center xl:text-left text-base md:text-lg font-bold text-[#242E2C]">
                <strong>Trusted by top companies worldwide</strong>
            </h2>
            <div className="w-full xl:w-5/7 flex items-center justify-center gap-4 md:gap-6 lg:gap-8 xl:gap-12 flex-wrap md:flex-nowrap xl:flex-nowrap">
                {renderCompanyLogos()}
            </div>
        </section>
    );
}