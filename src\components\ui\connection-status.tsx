"use client";

import React from 'react';
import { Wifi, WifiOff, RotateCcw, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
interface ConnectionStatus {
  isConnected: boolean;
  isReconnecting: boolean;
  reconnectAttempts: number;
  latency: number;
  lastConnected: Date;
}

interface ConnectionStatusIndicatorProps {
  connectionStatus: ConnectionStatus;
  onReconnect?: () => void;
  showDetails?: boolean;
  className?: string;
}

export function ConnectionStatusIndicator({
  connectionStatus,
  onReconnect,
  showDetails = false,
  className = ""
}: ConnectionStatusIndicatorProps) {
  const { isConnected, isReconnecting, reconnectAttempts, latency, lastConnected } = connectionStatus;

  const getStatusIcon = () => {
    if (isReconnecting) {
      return <RotateCcw className="w-4 h-4 animate-spin" />;
    }
    if (isConnected) {
      return <Wifi className="w-4 h-4" />;
    }
    return <WifiOff className="w-4 h-4" />;
  };

  const getStatusColor = () => {
    if (isReconnecting) return 'text-amber-600';
    if (isConnected) return 'text-green-600';
    return 'text-red-600';
  };

  const getStatusText = () => {
    if (isReconnecting) return `Reconnecting... (${reconnectAttempts}/10)`;
    if (isConnected) return 'Connected';
    return 'Disconnected';
  };

  const getLatencyColor = () => {
    if (!latency) return 'text-gray-500';
    if (latency < 100) return 'text-green-600';
    if (latency < 300) return 'text-amber-600';
    return 'text-red-600';
  };

  const formatLastConnected = () => {
    if (!lastConnected) return 'Never';
    const now = new Date();
    const diff = now.getTime() - lastConnected.getTime();
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    
    if (minutes > 0) return `${minutes}m ${seconds}s ago`;
    return `${seconds}s ago`;
  };

  if (!showDetails) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className={`flex items-center gap-2 ${className}`}>
              <div className={getStatusColor()}>
                {getStatusIcon()}
              </div>
              {!isConnected && onReconnect && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onReconnect}
                  className="h-6 px-2 text-xs"
                  disabled={isReconnecting}
                >
                  Retry
                </Button>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <div className="font-medium">{getStatusText()}</div>
              {latency && (
                <div className="text-xs">Latency: {latency}ms</div>
              )}
              {!isConnected && lastConnected && (
                <div className="text-xs">Last connected: {formatLastConnected()}</div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <div className={`flex items-center gap-3 p-3 bg-white border rounded-lg ${className}`}>
      <div className={`flex items-center gap-2 ${getStatusColor()}`}>
        {getStatusIcon()}
        <span className="text-sm font-medium">{getStatusText()}</span>
      </div>

      <div className="flex items-center gap-4 text-xs text-gray-600">
        {latency && (
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${getLatencyColor().replace('text-', 'bg-')}`} />
            <span className={getLatencyColor()}>{latency}ms</span>
          </div>
        )}

        {!isConnected && lastConnected && (
          <div className="flex items-center gap-1">
            <AlertCircle className="w-3 h-3" />
            <span>Last: {formatLastConnected()}</span>
          </div>
        )}

        {isConnected && (
          <div className="flex items-center gap-1">
            <CheckCircle className="w-3 h-3 text-green-600" />
            <span>Real-time updates active</span>
          </div>
        )}
      </div>

      {!isConnected && onReconnect && (
        <Button
          variant="outline"
          size="sm"
          onClick={onReconnect}
          disabled={isReconnecting}
          className="ml-auto"
        >
          {isReconnecting ? (
            <>
              <RotateCcw className="w-3 h-3 mr-1 animate-spin" />
              Reconnecting...
            </>
          ) : (
            <>
              <Wifi className="w-3 h-3 mr-1" />
              Reconnect
            </>
          )}
        </Button>
      )}
    </div>
  );
}

interface ConnectionStatusBadgeProps {
  connectionStatus: ConnectionStatus;
  className?: string;
}

export function ConnectionStatusBadge({ connectionStatus, className = "" }: ConnectionStatusBadgeProps) {
  const { isConnected, isReconnecting } = connectionStatus;

  const getVariant = () => {
    if (isReconnecting) return 'secondary';
    if (isConnected) return 'default';
    return 'destructive';
  };

  const getText = () => {
    if (isReconnecting) return 'Reconnecting';
    if (isConnected) return 'Online';
    return 'Offline';
  };

  return (
    <Badge variant={getVariant()} className={`text-xs ${className}`}>
      {getText()}
    </Badge>
  );
}