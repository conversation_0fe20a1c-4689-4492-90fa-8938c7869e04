import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      console.error('Dashboard stats API: No user in request');
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // For admin users, get company-wide stats; for whistleblowers, get user-specific stats
    const userId = request.user.role === 'whistleblower' ? request.user.id : undefined;
    const companyId = request.user.companyId;
    
    console.log('Dashboard stats API: User info', {
      userId: request.user.id,
      role: request.user.role,
      companyId,
      queryUserId: userId
    });
    
    const stats = await DataService.getDashboardStats(userId, companyId);
    
    console.log('Dashboard stats API: Stats retrieved', stats);
    
    return NextResponse.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Dashboard stats API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});