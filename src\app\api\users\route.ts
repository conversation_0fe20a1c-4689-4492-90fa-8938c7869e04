import { NextResponse } from 'next/server';
import { getUsersForDisplay } from '@/lib/auth/hardcoded-users';

export const runtime = 'nodejs';

export async function GET() {
  try {
    // Return hardcoded users without passwords
    const users = getUsersForDisplay();
    
    return NextResponse.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}