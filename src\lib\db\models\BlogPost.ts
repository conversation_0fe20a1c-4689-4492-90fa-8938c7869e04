import mongoose from 'mongoose';

const BlogPostSchema = new mongoose.Schema({
  title: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  content: { type: String, required: true },
  excerpt: { type: String },
  category: { type: String, default: 'general' },
  featured: { type: Boolean, default: false },
  publishedAt: { type: Date, default: Date.now }
}, { timestamps: true });

export default mongoose.models?.BlogPost || mongoose.model('BlogPost', BlogPostSchema);