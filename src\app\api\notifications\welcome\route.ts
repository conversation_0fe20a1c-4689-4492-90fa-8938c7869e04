import { NextResponse } from 'next/server';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { NotificationService } from '@/lib/services/notificationService';
import { DataService } from '@/lib/db/dataService';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const user = request.user!;
    
    // Check if user already has a welcome notification
    const existingNotifications = await DataService.getNotifications(user.id, { limit: 100 });
    const hasWelcomeNotification = existingNotifications.some(
      notification => {
        const notificationData = notification as unknown as {
          metadata?: {
            category?: string;
          };
        };
        return notificationData.metadata?.category === 'welcome';
      }
    );
    
    if (hasWelcomeNotification) {
      return NextResponse.json({
        success: true,
        message: 'Welcome notification already exists'
      });
    }
    
    // Get user details and company info
    const userDetails = await DataService.getUserById(user.id);
    let companyName: string | undefined;
    
    if (userDetails?.companyId) {
      const company = await DataService.getCompanyById(userDetails.companyId.toString());
      const companyData = company as unknown as {
        name?: string;
      };
      companyName = companyData?.name;
    }
    
    const userName = userDetails ? `${userDetails.firstName} ${userDetails.lastName}` : 'User';
    
    // Create welcome notification
    const notification = await NotificationService.createWelcomeNotification(
      user.id,
      user.role,
      userName,
      companyName
    );
    
    return NextResponse.json({
      success: true,
      data: notification,
      message: 'Welcome notification created successfully'
    });
  } catch (error) {
    console.error('Welcome notification API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});