"use client";

import { useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

function LoginRedirectContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const role = searchParams.get('role') || 'admin';
  const redirect = searchParams.get('redirect');

  useEffect(() => {
    // Redirect to the appropriate login page based on the role
    const loginPath = `/login/${role}`;
    
    // Include the redirect parameter if it exists
    if (redirect) {
      router.push(`${loginPath}?redirect=${encodeURIComponent(redirect)}`);
    } else {
      router.push(loginPath);
    }
  }, [router, role, redirect]);

  return (
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#1E4841]"></div>
  );
}

function LoginRedirectPageContent() {
  return (<div className="min-h-screen flex items-center justify-center">
      <Suspense fallback={<div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#1E4841]"></div>}>
        <LoginRedirectContent />
      </Suspense>
    </div>
  );
}

export default function LoginRedirectPage() {
  return (
    <Suspense fallback={<div className="animate-pulse p-4">Loading...</div>}>
      <LoginRedirectPageContent />
    </Suspense>
  );
}