"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter, useParams } from "next/navigation";
import Header from "@/components/dashboard-components/Header";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
    ChevronLeft,
    Send,
    LockKeyhole,
    Timer,
    CheckCheck,
    LogOut
} from "lucide-react";
import { getConversationById, getMessagesByConversationId, markConversationAsRead } from "@/lib/mockData/conversationData";
import { notifyMessageCountUpdate } from "@/lib/utils/messageIndicators";

export default function MobileChatPage() {
    const router = useRouter();
    const params = useParams();
    const conversationId = params.conversationId as string;

    const [newMessage, setNewMessage] = useState<string>("");
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [autoLogoutTime, setAutoLogoutTime] = useState<number>(15 * 60);
    const [showTypingIndicator, setShowTypingIndicator] = useState<boolean>(false);
    const [showAutoLogout, setShowAutoLogout] = useState<boolean>(false);
    const [sentMessages, setSentMessages] = useState<Record<string, Array<{ id: string, content: string, timestamp: string }>>>({});
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    const conversation = getConversationById(conversationId);
    const conversationMessages = conversation ? getMessagesByConversationId(conversationId) : [];

    useEffect(() => {
        if (conversation && conversation.isUnread) {
            markConversationAsRead(conversationId);
            notifyMessageCountUpdate();
        }
    }, [conversationId, conversation]);

    useEffect(() => {
        if (!showAutoLogout) return;

        const timer = setInterval(() => {
            setAutoLogoutTime(prev => {
                if (prev <= 1) {
                    router.push('/logout');
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [router, showAutoLogout]);

    const resetAutoLogoutTimer = useCallback(() => {
        setAutoLogoutTime(15 * 60);
        setShowAutoLogout(false);
    }, []);

    useEffect(() => {
        let inactivityTimeout: NodeJS.Timeout;

        const handleUserActivity = () => {
            resetAutoLogoutTimer();

            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }

            inactivityTimeout = setTimeout(() => {
                setShowAutoLogout(true);
            }, 2 * 60 * 1000);
        };

        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

        events.forEach(event => {
            document.addEventListener(event, handleUserActivity, true);
        });

        handleUserActivity();

        return () => {
            events.forEach(event => {
                document.removeEventListener(event, handleUserActivity, true);
            });
            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }
        };
    }, [resetAutoLogoutTimer]);

    const formatAutoLogoutTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    const handleSendMessage = async () => {
        if (!newMessage.trim()) return;

        const messageContent = newMessage.trim();
        const timestamp = new Date().toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

        setIsLoading(true);
        setShowTypingIndicator(false);

        const newSentMessage = {
            id: `sent_${Date.now()}`,
            content: messageContent,
            timestamp: timestamp
        };
        setSentMessages(prev => ({
            ...prev,
            [conversationId]: [...(prev[conversationId] || []), newSentMessage]
        }));

        setNewMessage("");

        await new Promise(resolve => setTimeout(resolve, 1000));

        setShowTypingIndicator(true);
        setTimeout(() => {
            setShowTypingIndicator(false);
        }, 3000);

        setIsLoading(false);
        resetAutoLogoutTimer();
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key.toLowerCase()) {
                case 'enter':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        handleSendMessage();
                    }
                    break;
            }
        }
    };

    const handleBack = () => {
        router.push('/dashboard/whistleblower/secure-message');
    };

    if (!conversation) {
        return (
            <div className="w-full h-screen flex flex-col">
                <Header />
                <main id="main-content" className="flex-1 bg-white flex items-center justify-center">
                    <div className="text-center">
                        <h1 className="text-xl font-semibold text-[#242E2C] mb-2">Conversation Not Found</h1>
                        <p className="text-sm text-[#6B7280] mb-4">The conversation you&apos;re looking for doesn&apos;t exist.</p>
                        <Button onClick={handleBack} className="bg-[#1E4841] text-white hover:bg-[#1E4841]/90">
                            Back to Messages
                        </Button>
                    </div>
                </main>
            </div>
        );
    }

    return (
        <div className="w-full h-screen flex flex-col">
            <Header />
            <main id="main-content" className="flex-1 bg-white" aria-label="Secure message chat">
                <div className="p-4 border-b flex items-center justify-between bg-gray-50">
                    <div className="flex items-center gap-3">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleBack}
                            className="p-1 hover:bg-gray-200 transition-colors"
                            aria-label="Back to conversations"
                        >
                            <ChevronLeft className="w-5 h-5 text-[#6B7271]" />
                        </Button>
                        <div className="relative">
                            <div className={`h-10 w-10 rounded-full ${conversation.avatarBg} flex items-center justify-center shadow-sm transition-transform hover:scale-105`}>
                                <span className="text-sm font-medium text-[#1E4841]">
                                    {conversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                </span>
                            </div>
                            {conversation.isOnline && (
                                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                            )}
                        </div>
                        <div className="flex-1">
                            <div className="flex items-center gap-2">
                                <p className="text-base font-medium text-[#111827]">{conversation.name}</p>
                                {conversation.isOnline && (
                                    <span className="text-xs text-green-600 font-medium">Online</span>
                                )}
                            </div>
                            <div className="flex items-center gap-2 text-xs text-[#6B7280]">
                                <span>{conversation.caseId}</span>
                                <span className="text-xs">•</span>
                                <LockKeyhole className="w-3 h-3" />
                                <span>Encrypted</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0">
                    <div className="text-center sticky top-0 z-10 pb-2">
                        <p className="text-xs text-[#4B5563] bg-gray-100/90 backdrop-blur-sm px-3 py-1.5 rounded-full inline-block shadow-sm">
                            May 15, 2025
                        </p>
                    </div>

                    <div className="flex justify-center">
                        <div className="flex items-center gap-2 text-xs text-[#1E4841] bg-[#ECF4E9] px-4 py-2.5 rounded-lg max-w-2xl text-center shadow-sm transition-all hover:bg-[#E1EDE9]">
                            <LockKeyhole className="w-4 h-4 animate-pulse" />
                            <span>This conversation is end-to-end encrypted. Messages are secured and private.</span>
                        </div>
                    </div>                    {conversationMessages.map((message) => (
                        <div key={message.id}>
                            {message.isFromUser ? (
                                <div className="flex items-start gap-2 justify-end group">
                                    <div className="flex flex-col gap-1 max-w-[80%]">
                                        <div className="bg-[#ECF4E9] rounded-2xl rounded-tr-md p-3 shadow-sm hover:bg-[#E1EDE9] transition-colors">
                                            <p className="text-sm text-[#1F2937] leading-relaxed break-words">
                                                {message.content}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2 text-xs text-[#6B7280] justify-end px-2 opacity-80 group-hover:opacity-100 transition-opacity">
                                            <div className="flex items-center gap-1 bg-gray-50 px-2 py-0.5 rounded-full">
                                                <LockKeyhole className="w-3 h-3" />
                                                <span>Encrypted</span>
                                            </div>
                                            <span>•</span>
                                            <div className="flex items-center gap-1 text-green-600">
                                                <CheckCheck className="w-3 h-3" />
                                                <span>Read</span>
                                            </div>
                                            <span>•</span>
                                            <span>{message.timestamp}</span>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="flex items-start gap-2">
                                    <div className={`h-8 w-8 rounded-full ${conversation.avatarBg} flex items-center justify-center flex-shrink-0`}>
                                        <span className="text-xs font-medium text-[#1E4841]">
                                            {conversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                        </span>
                                    </div>
                                    <div className="flex flex-col gap-1 max-w-[80%]">
                                        <div className="bg-[#F9FAFB] border border-[#E5E7EB] rounded-2xl rounded-tl-md p-3">
                                            <p className="text-sm text-[#1F2937]">
                                                {message.content}
                                            </p>
                                        </div>
                                        <div className="flex items-center gap-2 text-xs text-[#6B7280] px-2">
                                            <span>{message.timestamp}</span>
                                            <span>•</span>
                                            <LockKeyhole className="w-3 h-3" />
                                            <span>Encrypted</span>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    ))}

                    {(sentMessages[conversationId] || []).map((message) => (
                        <div key={message.id} className="flex items-start gap-2 justify-end">
                            <div className="flex flex-col gap-1 max-w-[80%]">
                                <div className="bg-[#ECF4E9] rounded-2xl rounded-tr-md p-3">
                                    <p className="text-sm text-[#1F2937]">
                                        {message.content}
                                    </p>
                                </div>
                                <div className="flex items-center gap-2 text-xs text-[#6B7280] justify-end px-2">
                                    <LockKeyhole className="w-3 h-3" />
                                    <span>Encrypted</span>
                                    <span>•</span>
                                    <CheckCheck className="w-3 h-3 text-green-600" />
                                    <span>Sent</span>
                                    <span>•</span>
                                    <span>{message.timestamp}</span>
                                </div>
                            </div>
                        </div>
                    ))}

                    {showTypingIndicator && (
                        <div className="flex items-start gap-2">
                            <div className={`h-8 w-8 rounded-full ${conversation.avatarBg} flex items-center justify-center flex-shrink-0`}>
                                <span className="text-xs font-medium text-[#1E4841]">
                                    {conversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                </span>
                            </div>
                            <div className="bg-[#F9FAFB] border border-[#E5E7EB] rounded-2xl rounded-tl-md p-3">
                                <div className="flex items-center gap-1">
                                    <div className="w-2 h-2 bg-[#6B7280] rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-[#6B7280] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                    <div className="w-2 h-2 bg-[#6B7280] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                <div className="p-4 border-t bg-gray-50">
                    {showAutoLogout && (
                        <div className="flex items-center gap-2 text-xs bg-red-50 p-2 rounded-lg border border-red-100 mb-3">
                            <Timer className="w-4 h-4 text-red-600 animate-pulse" />
                            <span className="text-red-600 font-medium">Auto-logout in {formatAutoLogoutTime(autoLogoutTime)}</span>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => router.push('/logout')}
                                className="ml-auto p-1 text-red-600 hover:bg-red-100 transition-colors"
                                aria-label="Logout now"
                            >
                                <LogOut className="w-3 h-3" />
                            </Button>
                        </div>
                    )}
                    <div className="flex gap-2">
                        <div className="flex-1 relative">
                            <Textarea
                                ref={textareaRef}
                                name="message"
                                aria-label="Type a message"
                                placeholder="Type your message... (Ctrl+Enter to send)"
                                value={newMessage}
                                onChange={(e) => {
                                    setNewMessage(e.target.value);
                                    resetAutoLogoutTimer();
                                }}
                                className="flex-1 min-h-[60px] max-h-[200px] resize-none border-2 border-[#D1D5DB] focus:border-[#1E4841] focus:ring-[#1E4841] pr-10 transition-all"
                                onKeyDown={handleKeyDown}
                            />
                            <div className="absolute right-3 bottom-3">
                                <LockKeyhole className="w-4 h-4 text-[#1E4841] opacity-50" />
                            </div>
                        </div>
                        <Button
                            onClick={handleSendMessage}
                            disabled={!newMessage.trim() || isLoading}
                            className="px-4 py-2 bg-[#1E4841] text-white hover:bg-[#1E4841]/90 disabled:opacity-50 self-end transition-all duration-200 hover:scale-105 focus:ring-2 focus:ring-[#1E4841] focus:ring-offset-2"
                            aria-label="Send message"
                        >
                            {isLoading ? (
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            ) : (
                                <Send className="w-4 h-4" />
                            )}
                        </Button>
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                        <p className="text-xs text-[#6B7280] flex items-center gap-1">
                            <kbd className="px-2 py-0.5 text-xs bg-gray-100 rounded-md">Ctrl</kbd>
                            <span>+</span>
                            <kbd className="px-2 py-0.5 text-xs bg-gray-100 rounded-md">Enter</kbd>
                            <span>to send</span>
                        </p>
                        <span className="text-[#6B7280]">•</span>
                        <div className="flex items-center gap-1 text-xs text-[#1E4841]">
                            <LockKeyhole className="w-3 h-3" />
                            <span>End-to-end encrypted</span>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
}