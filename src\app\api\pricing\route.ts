import { NextResponse } from 'next/server';

export const runtime = 'nodejs';

// Hardcoded pricing plans
const HARDCODED_PRICING_PLANS = [
  {
    id: 'plan-basic',
    name: 'Basic',
    price: 29,
    features: [
      'Up to 100 reports per month',
      'Basic reporting dashboard',
      'Email notifications',
      'Standard security',
      '24/7 support'
    ],
    order: 1,
    isActive: true
  },
  {
    id: 'plan-professional',
    name: 'Professional',
    price: 79,
    features: [
      'Up to 500 reports per month',
      'Advanced analytics dashboard',
      'Email & SMS notifications',
      'Enhanced security features',
      'Custom branding',
      'Priority support',
      'API access'
    ],
    order: 2,
    isActive: true
  },
  {
    id: 'plan-enterprise',
    name: 'Enterprise',
    price: 199,
    features: [
      'Unlimited reports',
      'Full analytics suite',
      'Multi-channel notifications',
      'Advanced security & compliance',
      'Full customization',
      'Dedicated account manager',
      'Full API access',
      'Custom integrations',
      'On-premise deployment option'
    ],
    order: 3,
    isActive: true
  }
];

export async function GET() {
  try {
    const activePlans = HARDCODED_PRICING_PLANS
      .filter(plan => plan.isActive)
      .sort((a, b) => a.order - b.order);
    
    return NextResponse.json({
      success: true,
      data: activePlans
    });
  } catch (error) {
    console.error('Pricing API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}