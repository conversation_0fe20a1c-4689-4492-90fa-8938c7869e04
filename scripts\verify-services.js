#!/usr/bin/env node

/**
 * Service Verification Script
 * Verifies email and OAuth configuration
 */

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '../.env.local') });

console.log('🔍 Verifying Email and OAuth Services Configuration\n');

// Email Service Verification
console.log('📧 Email Service Configuration:');
const emailConfig = {
  host: process.env.EMAIL_SERVER_HOST,
  port: process.env.EMAIL_SERVER_PORT,
  user: process.env.EMAIL_SERVER_USER,
  password: process.env.EMAIL_SERVER_PASSWORD ? '***configured***' : 'missing',
  from: process.env.EMAIL_FROM
};

console.log('   Host:', emailConfig.host || '❌ Missing');
console.log('   Port:', emailConfig.port || '❌ Missing');
console.log('   User:', emailConfig.user || '❌ Missing');
console.log('   Password:', emailConfig.password);
console.log('   From:', emailConfig.from || '❌ Missing');

const emailConfigured = emailConfig.host && emailConfig.port && emailConfig.user && process.env.EMAIL_SERVER_PASSWORD;
console.log('   Status:', emailConfigured ? '✅ Configured' : '❌ Incomplete\n');

// OAuth Configuration Verification
console.log('🔐 OAuth Configuration:');

// NextAuth
console.log('   NextAuth URL:', process.env.NEXTAUTH_URL || '❌ Missing');
console.log('   NextAuth Secret:', process.env.NEXTAUTH_SECRET ? '***configured***' : '❌ Missing');

// Google OAuth
console.log('   Google Client ID:', process.env.GOOGLE_CLIENT_ID ? '***configured***' : '❌ Missing');
console.log('   Google Client Secret:', process.env.GOOGLE_CLIENT_SECRET ? '***configured***' : '❌ Missing');

// Microsoft OAuth
console.log('   Microsoft Client ID:', process.env.MICROSOFT_CLIENT_ID ? '***configured***' : '❌ Missing');
console.log('   Microsoft Client Secret:', process.env.MICROSOFT_CLIENT_SECRET ? '***configured***' : '❌ Missing');
console.log('   Microsoft Tenant ID:', process.env.MICROSOFT_TENANT_ID ? '***configured***' : '❌ Missing');

const oauthConfigured = process.env.NEXTAUTH_URL && 
                       process.env.NEXTAUTH_SECRET && 
                       process.env.GOOGLE_CLIENT_ID && 
                       process.env.GOOGLE_CLIENT_SECRET && 
                       process.env.MICROSOFT_CLIENT_ID && 
                       process.env.MICROSOFT_CLIENT_SECRET && 
                       process.env.MICROSOFT_TENANT_ID;

console.log('   Status:', oauthConfigured ? '✅ Configured' : '❌ Incomplete\n');

// Database Configuration
console.log('🗄️  Database Configuration:');
console.log('   MongoDB URI:', process.env.MONGODB_URI ? '***configured***' : '❌ Missing');
console.log('   JWT Secret:', process.env.JWT_SECRET ? '***configured***' : '❌ Missing\n');

// Overall Status
console.log('📊 Overall Status:');
const allConfigured = emailConfigured && oauthConfigured && process.env.MONGODB_URI && process.env.JWT_SECRET;
console.log('   Email Service:', emailConfigured ? '✅' : '❌');
console.log('   OAuth Providers:', oauthConfigured ? '✅' : '❌');
console.log('   Database:', process.env.MONGODB_URI ? '✅' : '❌');
console.log('   JWT:', process.env.JWT_SECRET ? '✅' : '❌');
console.log('   Overall:', allConfigured ? '✅ Ready for testing' : '❌ Configuration incomplete\n');

// Recommendations
if (!allConfigured) {
  console.log('🔧 Recommendations:');
  
  if (!emailConfigured) {
    console.log('   📧 Email Service:');
    console.log('      - Verify SMTP credentials with your email provider');
    console.log('      - Test email sending with /api/test/email endpoint');
    console.log('      - Check firewall/network settings for SMTP port access');
  }
  
  if (!oauthConfigured) {
    console.log('   🔐 OAuth Configuration:');
    console.log('      - Set up Google OAuth in Google Cloud Console');
    console.log('      - Configure Microsoft OAuth in Azure Portal');
    console.log('      - Ensure redirect URIs are properly configured');
    console.log('      - Generate a strong NEXTAUTH_SECRET');
  }
  
  console.log('   🧪 Testing:');
  console.log('      - Visit /test/services for interactive testing');
  console.log('      - Test OAuth flow on login pages');
  console.log('      - Verify email notifications work');
}

// Test URLs
console.log('🔗 Test URLs (when server is running):');
console.log('   Service Tests: http://localhost:3002/test/services');
console.log('   Email Test API: http://localhost:3002/api/test/email');
console.log('   OAuth Test API: http://localhost:3002/api/test/oauth');
console.log('   Whistleblower Login: http://localhost:3002/login/whistleblower');
console.log('   Admin Login: http://localhost:3002/login/admin');

console.log('\n✨ Verification complete!');

// Exit with appropriate code
process.exit(allConfigured ? 0 : 1);
