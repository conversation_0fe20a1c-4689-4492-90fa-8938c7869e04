"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { toast } from '@/components/ui/use-toast';
import LoginForm, { LoginFormData } from '@/components/auth-components/login/shared/LoginForm';
import ErrorBoundary from '@/components/common/ErrorBoundary';
import { useAuth } from '@/hooks/useAuth';

export default function AdminLoginPage() {
    const [isLoading, setIsLoading] = useState(false);
    const { login } = useAuth();

    const handleLogin = async (data: LoginFormData) => {
        setIsLoading(true);
        try {
            const success = await login(data.email, data.password, 'admin');
            
            if (success) {
                toast({
                    title: "Login Successful",
                    description: "Welcome back! Redirecting to admin dashboard..."
                });
                // Login function already handles navigation to dashboard
            } else {
                throw new Error('Invalid credentials');
            }
        } catch (error) {
            console.error('Login error:', error);
            toast({
                title: "Login Failed",
                description: error instanceof Error ? error.message : "Invalid credentials. Please try again.",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };




    return (
        <ErrorBoundary>
            <div className="min-h-screen flex flex-col lg:flex-row">
                {/* Left side content */}
                <div className="flex lg:w-1/2 xl:w-1/2 p-4 sm:p-6 md:p-8 lg:p-12 xl:p-16 flex-col justify-center items-center bg-white">
                    <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg">
                        <div className="mb-6 sm:mb-8">
                            <Link href="/">
                                <Image
                                    src="/logo.svg"
                                    alt="Logo"
                                    width={120}
                                    height={40}
                                    className="h-auto w-16 sm:w-20 lg:w-24"
                                    priority
                                />
                            </Link>
                        </div>

                        <div className="mb-6 sm:mb-8">
                            <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-2xl font-bold text-gray-900 mb-2">Welcome Back, Admin</h1>
                            <p className="text-gray-500 text-sm sm:text-base">
                                Log in to your secure dashboard to manage reports, users, and compliance actions.
                            </p>
                        </div>

                        <div className="space-y-3 sm:space-y-4">
                            <LoginForm
                                onSubmit={handleLogin}
                                isLoading={isLoading}
                                buttonText="Log in to Admin Dashboard"
                            />

                            <div className="text-center text-xs sm:text-sm text-gray-600 p-3 sm:p-4 bg-blue-50 rounded-lg">
                                <p className="font-semibold mb-2">🔑 Admin Test Accounts:</p>
                                <div className="text-xs space-y-2">
                                    <div className="break-words">
                                        <strong className="text-blue-700">TechCorp Admin:</strong><br />
                                        📧 <EMAIL><br />
                                        🔒 admin123
                                    </div>
                                    <div className="break-words">
                                        <strong className="text-green-700">Global Manufacturing Admin:</strong><br />
                                        📧 <EMAIL><br />
                                        🔒 admin123
                                    </div>
                                    <div className="break-words">
                                        <strong className="text-purple-700">Investigator Access:</strong><br />
                                        📧 <EMAIL> / investigator123<br />
                                        📧 <EMAIL> / investigator123
                                    </div>
                                </div>
                            </div>

                            <div className="text-center text-xs sm:text-sm text-gray-600 p-3 sm:p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                                <p className="font-medium text-yellow-800 mb-2">Not an admin?</p>
                                <p className="text-yellow-700 mb-2">If you&apos;re a whistleblower or reporter, please use the dedicated login page:</p>
                                <Link 
                                    href="/login/whistleblower" 
                                    className="text-blue-600 hover:text-blue-800 underline font-medium text-xs sm:text-sm"
                                >
                                    Whistleblower Login →
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Right side content */}
                <div className="hidden lg:block lg:w-1/2 xl:w-1/2 relative">
                    <Image
                        src="/(auth)/login/office.png"
                        alt="Office"
                        fill
                        className="object-cover w-auto h-full"
                        style={{ objectFit: 'cover' }}
                        sizes="(max-width: 1024px) 0vw, 50vw"
                        priority
                    />
                    <div className="absolute inset-0 bg-[#1E48419E] rounded-3xl h-3/5 w-4/5 z-10 m-auto" />
                </div>
            </div>
        </ErrorBoundary>
    );
}
