import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { emailService } from '@/lib/email/emailService';
import logger from '@/lib/utils/logger';

// Validation schema for demo request
const demoRequestSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  company: z.string().min(2, 'Company name must be at least 2 characters'),
  jobTitle: z.string().min(2, 'Job title must be at least 2 characters'),
  phone: z.string().optional(),
  companySize: z.string().min(1, 'Company size is required'),
  industry: z.string().min(1, 'Industry is required'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  preferredDate: z.string().optional(),
  preferredTime: z.string().optional(),
  urgency: z.enum(['low', 'medium', 'high']).default('medium'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validatedData = demoRequestSchema.parse(body);
    
    // Create demo request record
    const demoRequest = {
      id: `demo_${Date.now()}`,
      ...validatedData,
      submittedAt: new Date().toISOString(),
      status: 'pending',
      source: 'schedule_demo_form'
    };
    
    // Check if email service is configured
    const emailConfigured = (process.env.EMAIL_SERVER_HOST || process.env.SMTP_HOST) && 
                           (process.env.EMAIL_SERVER_USER || process.env.SMTP_USER) && 
                           (process.env.EMAIL_SERVER_PASSWORD || process.env.SMTP_PASS);

    if (!emailConfigured) {
      logger.error('Email service not configured for demo requests');
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email service is not configured. Please contact the administrator.' 
        },
        { status: 500 }
      );
    }

    // Test email connection
    const connectionTest = await emailService.testConnection();
    if (!connectionTest) {
      logger.error('Email connection test failed for demo request');
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email service is currently unavailable. Please try again later.' 
        },
        { status: 500 }
      );
    }

    try {
      // Send notification to sales team
      const salesNotificationSent = await emailService.sendDemoRequestNotification(validatedData);
      
      if (!salesNotificationSent) {
        throw new Error('Failed to send sales team notification');
      }

      // Send confirmation email to user
      try {
        await emailService.sendDemoConfirmationEmail(validatedData);
        logger.info('Demo confirmation email sent', { 
          email: validatedData.email,
          company: validatedData.company 
        });
      } catch (confirmationError) {
        logger.error('Failed to send demo confirmation email:', confirmationError);
        // Don't fail the main request if confirmation email fails
      }

      logger.info('Demo request processed successfully', { 
        requestId: demoRequest.id,
        email: validatedData.email,
        company: validatedData.company,
        urgency: validatedData.urgency
      });

      return NextResponse.json({
        success: true,
        data: {
          requestId: demoRequest.id,
          message: 'Thank you for your demo request! Our sales team will contact you within 24 hours to schedule your personalized demonstration.',
          estimatedResponseTime: '24 hours'
        }
      });

    } catch (emailError) {
      logger.error('Failed to process demo request emails:', emailError);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to send your demo request. Please try again later.' 
        },
        { status: 500 }
      );
    }
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: error.issues
        },
        { status: 400 }
      );
    }
    
    logger.error('Demo request API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to submit demo request. Please try again.' 
      },
      { status: 500 }
    );
  }
}
