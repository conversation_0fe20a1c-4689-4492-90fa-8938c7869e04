import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import connectDB from '@/lib/db/mongodb';
import { Company } from '@/lib/db/models';
import jwt from 'jsonwebtoken';

export const runtime = 'nodejs';

const anonymousAccessSchema = z.object({
  companyName: z.string().min(1, 'Company name is required').max(100, 'Company name too long'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = anonymousAccessSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { companyName } = validationResult.data;

    await connectDB();

    // Check if company exists or create a new one for anonymous access
    let company = await Company.findOne({ 
      name: { $regex: new RegExp(`^${companyName.trim()}$`, 'i') }
    });

    if (!company) {
      // Create a new company for anonymous access
      company = await Company.create({
        name: companyName.trim(),
        contactEmail: `anonymous@${companyName.toLowerCase().replace(/\s+/g, '')}.com`,
        website: `${companyName.toLowerCase().replace(/\s+/g, '')}.com`,
        isActive: true,
        subscriptionStatus: 'Active'
      });
    }

    // Generate a temporary anonymous session token
    const sessionId = `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    const anonymousToken = jwt.sign(
      { 
        sessionId,
        type: 'anonymous',
        companyId: company._id,
        companyName: company.name,
        expiresAt: expiresAt.toISOString()
      },
      process.env.JWT_SECRET || 'your-secret-key-change-in-production',
      { expiresIn: '24h' }
    );

    return NextResponse.json({
      success: true,
      message: 'Anonymous session created successfully',
      sessionId,
      token: anonymousToken,
      expiresAt: expiresAt.toISOString(),
      company: {
        id: company._id,
        name: company.name
      }
    });

  } catch (error) {
    console.error('Anonymous access error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
