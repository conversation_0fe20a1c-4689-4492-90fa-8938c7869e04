import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { NotificationService } from '@/lib/services/notificationService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const url = new URL(request.url);
    const reportId = url.pathname.split('/')[3];
    const { investigatorId } = await request.json();
    
    if (request.user!.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Only admins can assign investigators' },
        { status: 403 }
      );
    }

    if (!investigatorId) {
      return NextResponse.json(
        { success: false, error: 'investigatorId is required' },
        { status: 400 }
      );
    }

    // Get report and investigator details
    const [report, investigator] = await Promise.all([
      DataService.getReportById(reportId),
      DataService.getUserById(investigatorId)
    ]);

    if (!report) {
      return NextResponse.json(
        { success: false, error: 'Report not found' },
        { status: 404 }
      );
    }

    if (!investigator || investigator.role !== 'investigator') {
      return NextResponse.json(
        { success: false, error: 'Invalid investigator' },
        { status: 400 }
      );
    }

    // Update report with assigned investigator
    const updatedReport = await DataService.updateReport(reportId, {
      assignedInvestigator: investigatorId,
      status: 'In Progress'
    });

    // Create notifications
    const investigatorName = `${investigator.firstName} ${investigator.lastName}`;
    await NotificationService.createInvestigatorAssignedNotification(
      report.userId.toString(),
      investigatorId,
      reportId,
      report.title,
      investigatorName
    );

    // Send greeting message from investigator
    const conversation = await DataService.getConversationByReportId(reportId);
    if (conversation) {
      await DataService.createMessage({
        conversationId: conversation._id.toString(),
        senderId: investigatorId,
        content: `Hello, I'm ${investigatorName} and I've been assigned to investigate your report "${report.title}". I'll be reviewing the details and may reach out if I need any additional information. Thank you for bringing this matter to our attention.`,
        messageType: 'system'
      });
    }

    return NextResponse.json({
      success: true,
      data: updatedReport,
      message: 'Investigator assigned successfully'
    });
  } catch (error) {
    console.error('Assign investigator API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});