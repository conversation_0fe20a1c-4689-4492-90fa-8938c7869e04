"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, ChevronLeft } from "lucide-react";
import Link from "next/link";

interface NotificationHeaderProps {
    unreadCount: number;
    onMarkAllAsRead: () => void;
}

export default function NotificationHeader({ unreadCount, onMarkAllAsRead }: NotificationHeaderProps) {
    return (
        <>
            {/* Breadcrumb */}
            <nav
                className="flex items-center gap-2 text-xs sm:text-sm text-[#6B7280]"
                aria-label="Breadcrumb navigation"
            >
                <Link
                    href="/dashboard"
                    className="hover:text-[#1E4841] transition-colors"
                    aria-label="Return to dashboard"
                >
                    <ChevronLeft className="w-3 h-3 sm:w-4 sm:h-4 inline mr-1" aria-hidden="true" />
                    Dashboard
                </Link>
                <span aria-hidden="true">/</span>
                <span className="text-[#1E4841] font-medium" aria-current="page">Notifications</span>
            </nav>

            {/* Header Section */}
            <header className="flex flex-col sm:flex-row sm:items-start md:items-center justify-between gap-3 sm:gap-4">
                <div className="flex-1">
                    <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-[#1F2937]">Notifications</h1>
                    <p className="text-sm sm:text-base text-[#6B7280] mt-1">
                        Stay updated with your reports and system messages
                        {unreadCount > 0 && (
                            <span
                                className="block sm:inline sm:ml-2 text-[#1E4841] font-medium mt-1 sm:mt-0"
                                aria-label={`${unreadCount} unread notifications`}
                            >
                                ({unreadCount} unread)
                            </span>
                        )}
                    </p>
                </div>
                {unreadCount > 0 && (
                    <Button
                        onClick={onMarkAllAsRead}
                        className="bg-[#1E4841] hover:bg-[#2A5D54] text-white text-sm sm:text-base px-3 sm:px-4 py-2 sm:py-2.5 w-full sm:w-auto"
                        aria-label={`Mark all ${unreadCount} notifications as read`}
                    >
                        <CheckCheck className="w-3 h-3 sm:w-4 sm:h-4 mr-2" aria-hidden="true" />
                        <span className="hidden sm:inline">Mark All as Read</span>
                        <span className="sm:hidden">Mark All Read</span>
                    </Button>
                )}
            </header>
        </>
    );
}