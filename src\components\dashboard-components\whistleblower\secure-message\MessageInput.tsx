"use client";

import { useRef, useState } from "react";
import { Send, Paperclip, X, Timer, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import LexicalEditor from "@/components/ui/lexical-editor";
import { EditorState } from 'lexical';

interface MessageInputProps {
  newMessage: string;
  onMessageChange: (message: string) => void;
  onSendMessage: () => void;
  isLoading: boolean;
  showAutoLogout: boolean;
  autoLogoutTime: number;
  onLogout: () => void;
  clearTrigger: number;
  onEditorChange: (editorState: EditorState) => void;
  onEditorKeyDown: (event: KeyboardEvent) => boolean;
}

export default function MessageInput({
  newMessage,
  onSendMessage,
  isLoading,
  showAutoLogout,
  autoLogoutTime,
  onLogout,
  clearTrigger,
  onEditorChange,
  onEditorKeyDown
}: MessageInputProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);

  const formatAutoLogoutTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setAttachedFiles(prev => [...prev, ...Array.from(files)]);
    }
  };

  const removeAttachedFile = (index: number) => {
    setAttachedFiles(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="p-4 border-t bg-gray-50">
      {showAutoLogout && (
        <div className="flex items-center gap-2 text-xs text-[#FF2121] mb-3">
          <Timer className="w-4 h-4" />
          <span>Auto-logout in {formatAutoLogoutTime(autoLogoutTime)}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onLogout}
            className="ml-2 p-1 text-[#FF2121] hover:bg-red-50"
            aria-label="Logout now"
          >
            <LogOut className="w-3 h-3" />
          </Button>
        </div>
      )}

      {attachedFiles.length > 0 && (
        <div className="mb-3 flex flex-wrap gap-2">
          {attachedFiles.map((file, index) => (
            <Badge
              key={index}
              variant="secondary"
              className="bg-[#ECF4E9] text-[#1E4841] px-3 py-1 flex items-center gap-2 max-w-[200px]"
            >
              <Paperclip className="w-3 h-3" />
              <span className="text-xs truncate">{file.name}</span>
              <span className="text-xs opacity-70">({(file.size / 1024).toFixed(1)}KB)</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeAttachedFile(index)}
                className="p-0 h-auto w-4 text-red-500 hover:text-red-700 ml-1"
              >
                <X className="w-3 h-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}

      <Input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        onChange={handleFileSelect}
        accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
      />

      <div className="flex gap-2">
        <div className="flex-1">
          <LexicalEditor
            placeholder=""
            onChange={onEditorChange}
            onKeyDown={onEditorKeyDown}
            onAttachmentClick={() => fileInputRef.current?.click()}
            attachedFilesCount={attachedFiles.length}
            showToolbar={true}
            clearTrigger={clearTrigger}
            className="flex-1"
          />
        </div>
        <Button
          onClick={onSendMessage}
          disabled={!newMessage.trim() || isLoading}
          className="px-6 py-2 bg-[#1E4841] text-white hover:bg-[#1E4841]/90 disabled:opacity-50 self-end"
          aria-label="Send message"
        >
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
        </Button>
      </div>

      <p className="text-xs text-[#6B7280] mt-2">
        Press Ctrl+Enter to send • All messages are encrypted
      </p>
    </div>
  );
}