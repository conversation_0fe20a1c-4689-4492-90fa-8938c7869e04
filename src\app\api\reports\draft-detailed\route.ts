import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const reportData = await request.json();
    
    // Create detailed draft report
    const draftReport = await DataService.createReport({
      ...reportData,
      userId: request.user!.id,
      status: 'Draft',
      priority: reportData.urgencyLevel || 'Medium',
      isDraft: true,
      isDetailed: true
    });
    
    return NextResponse.json({
      success: true,
      data: draftReport
    }, { status: 201 });
  } catch (error) {
    console.error('Detailed draft report API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to save detailed draft' },
      { status: 500 }
    );
  }
});