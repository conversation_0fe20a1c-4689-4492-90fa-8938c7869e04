import { TrackingReportData, MessageBubbleProps, EvidenceFile } from "@/lib/types";

// Mock tracking data for report progress
export const mockTrackingReport: TrackingReportData = {
  report_id: "#WB-20250429-1138",
  updateddate: "April 29, 2025 at 20:23 AM",
  steps: [
    {
      title: "Submitted",
      date: "April 15, 2025",
      icon: "/tracking-system/message.svg",
      status: "complete",
    },
    {
      title: "Under Review",
      date: "April 17, 2025",
      icon: "/tracking-system/review.svg",
      status: "complete",
    },
    {
      title: "Investigation",
      date: "Current",
      icon: "/tracking-system/invegstigation.svg",
      status: "current",
    },
    {
      title: "Action Taken",
      date: "Pending...",
      icon: "/tracking-system/action.svg",
      status: "upcoming",
    },
    {
      title: "Closed",
      date: "Pending...",
      icon: "/tracking-system/closed.svg",
      status: "upcoming",
    },
  ],
};

// Mock messages for communication
export const mockMessages: MessageBubbleProps[] = [
  {
    message: "Thank you for your report. We have received it and assigned case #WB-20250429-1138.",
    timestamp: "April 15, 2025 at 10:30 AM",
    sender: "investigator",
    isRead: true,
  },
  {
    message: "We have begun our preliminary review of your submission.",
    timestamp: "April 17, 2025 at 2:15 PM",
    sender: "investigator",
    isRead: true,
  },
  {
    message: "Could you provide additional details about the incident location?",
    timestamp: "April 20, 2025 at 9:45 AM",
    sender: "investigator",
    isRead: true,
  },
  {
    message: "The incident occurred in the main office building, 3rd floor near the conference room.",
    timestamp: "April 20, 2025 at 11:20 AM",
    sender: "user",
    isRead: true,
  },
  {
    message: "Thank you for the clarification. Our investigation is now in progress.",
    timestamp: "April 22, 2025 at 3:30 PM",
    sender: "investigator",
    isRead: false,
  },
];

// Mock evidence files
export const mockEvidenceFiles: EvidenceFile[] = [
  {
    reportId: "ev-001",
    fileName: "incident-photo.jpg",
    fileSize: 2048576, // 2MB
    mimeType: "image/jpeg",
    fileUrl: "/evidence/incident-photo.jpg",
    uploadedAt: new Date("2025-04-15T10:25:00"),
    originalName: "",
    uploadedBy: "",
    isEncrypted: false
  },
  {
    reportId: "ev-002",
    fileName: "witness-statement.pdf",
    fileSize: 512000, // 512KB
    mimeType: "application/pdf",
    fileUrl: "/evidence/witness-statement.pdf",
    uploadedAt: new Date("2025-04-15T10:26:00"),
    originalName: "",
    uploadedBy: "",
    isEncrypted: false
  },
  {
    reportId: "ev-003",
    fileName: "email-correspondence.txt",
    fileSize: 8192, // 8KB
    mimeType: "text/plain",
    fileUrl: "/evidence/email-correspondence.txt",
    uploadedAt: new Date("2025-04-15T10:27:00"),
    originalName: "",
    uploadedBy: "",
    isEncrypted: false
  },
];

// Mock report categories
export const reportCategories = [
  { value: "fraud", label: "Fraud", color: "#EF4444" },
  { value: "harassment", label: "Harassment", color: "#F59E0B" },
  { value: "safety", label: "Safety Violation", color: "#EF4444" },
  { value: "discrimination", label: "Discrimination", color: "#8B5CF6" },
  { value: "corruption", label: "Corruption", color: "#DC2626" },
  { value: "environmental", label: "Environmental Concern", color: "#059669" },
  { value: "other", label: "Other", color: "#6B7280" },
];

// Mock investigation status options
export const investigationStatuses = [
  { status: "submitted", label: "Submitted", color: "#3B82F6" },
  { status: "under-review", label: "Under Review", color: "#F59E0B" },
  { status: "investigating", label: "Investigating", color: "#8B5CF6" },
  { status: "action-taken", label: "Action Taken", color: "#059669" },
  { status: "closed", label: "Closed", color: "#6B7280" },
];

// Mock action items for the actions panel
export const actionItems = [
  {
    id: "download",
    image: "/tracking-system/Download.svg",
    text: "Download Report",
    ariaLabel: "Download complete report as a file",
  },
  {
    id: "print",
    image: "/tracking-system/Print.svg",
    text: "Print Timeline",
    ariaLabel: "Print report timeline",
  },
  {
    id: "save",
    image: "/tracking-system/Save.svg",
    text: "Save as PDF",
    ariaLabel: "Save report as PDF document",
  },
  {
    id: "share",
    image: "/tracking-system/Share.svg",
    text: "Secure Share",
    ariaLabel: "Share report securely with authorized personnel",
  },
];

// Mock report information
export const mockReportInfo = {
  title: "Inappropriate Conduct in Workplace",
  description: "Witnessed inappropriate behavior during team meeting that made several colleagues uncomfortable.",
  category: "harassment",
  priority: "high",
  incidentDate: "April 10, 2025",
  location: "Main Office Building, 3rd Floor Conference Room",
  anonymous: true,
  status: "investigating",
  assignedInvestigator: "Sarah Johnson",
  estimatedCompletion: "May 15, 2025",
};






