import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";
import {
  AdminHero,
  AdminResponsibilities,
  AdminDashboard,
  AdminUserManagement,
  AdminCaseAssignment,
  AdminFeatures,
  AdminSecurity,
  AdminTestimonials,
  AdminCTA
} from "@/components/home-components/product/admin/AdminComponents";

export default function AdminPortalPage() {
    return (
        <div className="flex flex-col min-h-screen">
            <Header />
            <main id="main-content" aria-label="Admin Portal" className="flex-1 pt-20">
                <AdminHero />
                <AdminResponsibilities />
                <AdminDashboard />
                <AdminUserManagement />
                <AdminCaseAssignment />
                <AdminFeatures />
                <AdminSecurity />
                <AdminTestimonials />
                <AdminCTA />
            </main>
            <Footer />
        </div>
    );
}