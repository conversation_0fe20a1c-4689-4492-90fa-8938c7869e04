"use client";

import { memo, useCallback } from 'react';
import { Notification } from '@/lib/types';
import { getNotificationIcon, getPriorityColor, formatTimestamp } from '@/lib/utils';

interface NotificationItemProps {
    notification: Notification;
    onMarkAsRead: (id: string) => void;
    onNavigate?: (notification: Notification) => void;
    variant?: 'popover' | 'page';
}

const NotificationItem = memo(({ 
    notification, 
    onMarkAsRead, 
    onNavigate, 
    variant = 'page' 
}: NotificationItemProps) => {
    const Icon = getNotificationIcon(notification.type);
    const priorityColor = getPriorityColor(notification.priority);

    const handleClick = useCallback(() => {
        const notificationId = notification._id;
        if (notification.status === 'unread' && notificationId) {
            onMarkAsRead(notificationId);
        }
        if (onNavigate) {
            onNavigate(notification);
        }
    }, [notification, onMarkAsRead, onNavigate]);

    const isPopover = variant === 'popover';
    const baseClasses = `${isPopover ? 'p-3' : 'p-4'} hover:bg-gray-50 transition-colors cursor-pointer border-l-4`;
    const unreadClasses = notification.status === 'unread' 
        ? 'border-l-green-500 bg-green-50/30' 
        : 'border-l-transparent';

    return (
        <div
            className={`${baseClasses} ${unreadClasses} ${!isPopover ? 'border-b last:border-b-0' : ''}`}
            onClick={handleClick}
        >
            <div className="flex items-start gap-3">
                <div className={`${isPopover ? 'p-1.5' : 'p-2'} rounded-full ${
                    notification.status === 'unread' ? 'bg-green-100' : 'bg-gray-100'
                }`}>
                    <Icon className={`${isPopover ? 'w-4 h-4' : 'w-5 h-5'} ${priorityColor}`} />
                </div>
                <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                        <p className={`${isPopover ? 'text-sm' : 'text-base'} font-medium text-gray-900 break-words ${
                            notification.status === 'unread' ? 'font-semibold' : ''
                        }`}>
                            {notification.title}
                        </p>
                        <span className={`${isPopover ? 'text-xs' : 'text-sm'} text-gray-500 flex-shrink-0 whitespace-nowrap`}>
                            {notification.createdAt ? formatTimestamp(notification.createdAt.toString()) : 'N/A'}
                        </span>
                    </div>
                    <p className={`${isPopover ? 'text-sm' : 'text-base'} text-gray-600 mt-1 break-words ${
                        isPopover ? 'line-clamp-2' : ''
                    }`}>
                        {notification.message}
                    </p>
                    {notification.status === 'unread' && (
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    )}
                </div>
            </div>
        </div>
    );
});

NotificationItem.displayName = 'NotificationItem';

export default NotificationItem;