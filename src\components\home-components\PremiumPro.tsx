import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Inter } from "next/font/google";
import { premiumProFeatures, premiumProBenefits } from "@/lib/mockData";
import Link from "next/link";

const inter = Inter({
    weight: ["200", "400", "500", "600", "700", "800"],
    subsets: ['latin']
});

const FeatureItem = ({ text }: { text: string }) => (
    <div className="flex gap-2 md:gap-4 items-center lg:text-base">
        <Image src="/desktop/home/<USER>/tick.svg" alt="Tick Icon" height={25} width={25} />
        <p>{text}</p>
    </div>
);

const BenefitItem = ({ icon, text1, text2, height, width }: { icon: string, text1?: string, text2?: string, height: number, width: number }) => (
    <div className="flex justify-center items-center gap-3 md:gap-6">
        <Image
            src={icon}
            alt="Icon"
            height={height}
            width={width}
            className="w-[50px] h-auto"
        />
        <p className="font-bold text-base">
            {text1}
            {text2 && (
                <>
                    <span className="sm:hidden">{' '}</span>
                    <br className="inline sm:hidden" />
                    {text2}
                </>
            )}
        </p>
    </div>
);

export default function PremiumPro() {
    return (
        <div className={`flex flex-col justify-between min-h-[410px] lg:max-h-[700] xl:max-h-[200] items-center ${inter.className} w-full bg-[#D9D9D9]`}>
            <div className="bg-white mx-4 md:mx-22 lg:mx-44 relative top-25 xl:top-28 flex flex-col lg:flex-row shadow-2xl rounded-lg lg:max-h-[500]">
                <div className="bg-[#1E4841]/10 flex flex-col p-6 md:p-14 w-full xl:w-3/5 items-center justify-center rounded-t-lg md:rounded-l-lg md:rounded-tr-none">
                    <p className="font-extrabold text-xl md:text-[26px] text-[#171923]">Premium PRO</p>
                    <p className="font-extrabold text-5xl md:text-[65px]/20 text-[#171923]">$169</p>
                    <p className="font-medium text-lg md:text-[20px] text-[#171923]">billed just once</p>
                    <Link href="/signup" className="w-full">
                        <Button
                            variant="default"
                            className="w-full mt-8 py-5 md:py-7 font-semibold text-base md:text-lg text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm"
                            aria-label="Get Started"
                            name="get-started"
                        >
                            Get Started
                        </Button>
                    </Link>
                </div>
                <div className="bg-white text-base xl:text-lg w-full font-normal/5 p-6 md:p-14 flex flex-col gap-6 md:gap-8 rounded-lg">
                    <p className="text-[#2D3748]">Empower your organization with secure, anonymous, and efficient whistleblower reporting.</p>
                    <div className="flex flex-col text-[#1E4841] gap-4 md:gap-6">
                        {premiumProFeatures.map((feature, index) => (
                            <FeatureItem key={index} text={feature} />
                        ))}
                    </div>
                </div>
            </div>
            <div className="mx-4 md:mx-22 lg:mx-56 xl:mx-60 relative top-65 md:top-68 lg:top-78 xl:top-50 flex flex-col lg:flex-row justify-center xl:justify-between items-start md:items-center gap-8 md:gap-6 lg:gap-10 xl:gap-25 flex-wrap xl:flex-nowrap">
                {premiumProBenefits.map((benefit, index) => (
                    <BenefitItem
                        key={index}
                        icon={benefit.icon}
                        height={benefit.height}
                        width={benefit.width}
                        text1={benefit.text1 || benefit.text}
                        text2={benefit.text2}
                    />))}
            </div>
        </div>
    );
}