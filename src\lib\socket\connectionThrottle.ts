/**
 * Connection throttling utility to prevent rapid reconnections
 * This helps avoid the connect/disconnect spam issue
 */

interface ThrottleConfig {
  maxAttempts: number;
  timeWindow: number; // milliseconds
  backoffMultiplier: number;
  maxBackoff: number; // milliseconds
}

class ConnectionThrottle {
  private attempts: Date[] = [];
  private currentBackoff = 1000; // Start with 1 second
  private config: ThrottleConfig;

  constructor(config: Partial<ThrottleConfig> = {}) {
    this.config = {
      maxAttempts: 3,
      timeWindow: 10000, // 10 seconds
      backoffMultiplier: 2,
      maxBackoff: 30000, // 30 seconds
      ...config
    };
  }

  /**
   * Check if a connection attempt should be allowed
   * @returns {boolean} - true if connection is allowed, false if throttled
   */
  canConnect(): boolean {
    const now = new Date();
    
    // Clean up old attempts outside the time window
    this.attempts = this.attempts.filter(
      attempt => now.getTime() - attempt.getTime() < this.config.timeWindow
    );

    // Check if we've exceeded max attempts in the time window
    if (this.attempts.length >= this.config.maxAttempts) {
      console.warn(`🚫 Connection throttled: ${this.attempts.length} attempts in ${this.config.timeWindow}ms`);
      return false;
    }

    return true;
  }

  /**
   * Record a connection attempt
   */
  recordAttempt(): void {
    this.attempts.push(new Date());
  }

  /**
   * Get the current backoff delay
   * @returns {number} - delay in milliseconds
   */
  getBackoffDelay(): number {
    return Math.min(this.currentBackoff, this.config.maxBackoff);
  }

  /**
   * Increase the backoff delay for failed connections
   */
  increaseBackoff(): void {
    this.currentBackoff = Math.min(
      this.currentBackoff * this.config.backoffMultiplier,
      this.config.maxBackoff
    );
    console.log(`📈 Backoff increased to ${this.currentBackoff}ms`);
  }

  /**
   * Reset backoff on successful connection
   */
  resetBackoff(): void {
    this.currentBackoff = 1000;
    console.log('✅ Backoff reset to 1000ms');
  }

  /**
   * Clear all recorded attempts (useful for manual reset)
   */
  reset(): void {
    this.attempts = [];
    this.resetBackoff();
    console.log('🔄 Connection throttle reset');
  }

  /**
   * Get current throttle status
   */
  getStatus() {
    return {
      attemptsInWindow: this.attempts.length,
      maxAttempts: this.config.maxAttempts,
      currentBackoff: this.currentBackoff,
      isThrottled: this.attempts.length >= this.config.maxAttempts,
      timeWindow: this.config.timeWindow
    };
  }
}

export const connectionThrottle = new ConnectionThrottle({
  maxAttempts: 3,      // Allow 3 attempts
  timeWindow: 15000,   // In 15 seconds
  backoffMultiplier: 2, // Double the delay each time
  maxBackoff: 30000    // Cap at 30 seconds
});