"use client";

import { ColumnDef, Table, Row, Column } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Eye,
  PenLine,
  MessageSquare,
  MoreVertical,
  Flag,
  ArrowUpDown
} from "lucide-react";
import { ReportData } from "@/lib/types";
import { useRouter } from "next/navigation";

export const useColumns = () => {
  const router = useRouter();
  
  return [
  {
    id: "select",
    header: ({ table }: { table: Table<ReportData> }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() 
            ? true 
            : table.getIsSomePageRowsSelected() 
            ? "indeterminate" 
            : false
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
        className="data-[state=checked]:bg-[#1E4841] bg-white data-[state=checked]:border-[#E5E6E6]"
      />
    ),
    cell: ({ row }: { row: Row<ReportData> }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        className="data-[state=checked]:bg-[#1E4841] data-[state=checked]:border-[#1E4841]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: "Report ID",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <button
        className="font-mono font-medium text-xs sm:text-sm text-[#242E2C] hover:text-[#1E4841] hover:underline transition-colors"
        onClick={() => router.push(`/dashboard/reports/${row.getValue("id")}`)}
      >
        {row.getValue("id")}
      </button>
    ),
  },
  {
    accessorKey: "title",
    header: ({ column }: { column: Column<ReportData> }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="h-auto p-0 font-semibold text-[#1E4841]"
      >
        Title
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<ReportData> }) => {
      const getPriorityFlag = (priority: string) => priority === "Critical" || priority === "High";
      const getStatusColor = (statusColor: string | undefined) => {
        if (!statusColor) return "#6b7280";
        const colorMatch = statusColor.match(/text-\[([#\w]+)\]/);
        if (colorMatch) return colorMatch[1];
        return "#6b7280";
      };
      
      return (
        <div className="flex items-start justify-between gap-2 sm:gap-4">
          <button
            className="font-normal text-[#1E4841] leading-relaxed min-w-0 flex-1 text-left hover:underline hover:text-[#2A5D54] transition-colors text-sm sm:text-base"
            onClick={() => router.push(`/dashboard/reports/${row.getValue("id")}`)}
          >
            {row.getValue("title")}
          </button>
          {getPriorityFlag(row.original.priority) && (
            <Flag
              className="w-3 h-3 flex-shrink-0 mt-0.5"
              style={{
                color: getStatusColor(row.original.statusColor),
                fill: getStatusColor(row.original.statusColor)
              }}
            />
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "category",
    header: "Category",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <span className="text-xs sm:text-sm text-[#1E4841] font-normal">
        {row.getValue("category")}
      </span>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <Badge
        className={`${row.original.statusColor} text-xs font-medium px-2 py-1`}
        variant="secondary"
      >
        {row.getValue("status")}
      </Badge>
    ),
  },
  {
    accessorKey: "progressPercentage",
    header: "Progress",
    cell: ({ row }: { row: Row<ReportData> }) => {
      const getStatusColor = (statusColor: string | undefined) => {
        if (!statusColor) return "#6b7280";
        const colorMatch = statusColor.match(/text-\[([#\w]+)\]/);
        if (colorMatch) return colorMatch[1];
        return "#6b7280";
      };
      
      return (
        <div className="flex flex-col gap-1">
          <div
            className="progress-wrapper cursor-help"
            style={{
              '--progress-color': getStatusColor(row.original.statusColor)
            } as React.CSSProperties}
          >
            <Progress
              value={row.getValue("progressPercentage") || 0}
              className="h-2 [&>div]:bg-[var(--progress-color)]"
            />
          </div>
          <span className="text-xs text-gray-600">
            {row.getValue("progressPercentage") || 0}% Complete
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: "dateSubmitted",
    header: ({ column }: { column: Column<ReportData> }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        className="h-auto p-0 font-semibold text-[#1E4841]"
      >
        Date Submitted
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<ReportData> }) => (
      <span className="text-xs sm:text-sm text-[#242E2C] font-normal">
        {row.getValue("dateSubmitted")}
      </span>
    ),
  },
  {
    accessorKey: "lastUpdated",
    header: "Last Updated",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <span className="text-xs sm:text-sm text-[#242E2C] font-normal">
        {row.getValue("lastUpdated")}
      </span>
    ),
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }: { row: Row<ReportData> }) => (
      <div className="flex items-center gap-0.5 sm:gap-1">
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 sm:h-8 sm:w-8 p-0 hover:bg-gray-100"
          onClick={() => router.push(`/dashboard/reports/${row.getValue("id")}`)}
        >
          <Eye className="w-3 h-3 sm:w-4 sm:h-4 text-[#6B7280]" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 sm:h-8 sm:w-8 p-0 hover:bg-gray-100"
          onClick={() => router.push(`/dashboard/reports/${row.getValue("id")}/edit`)}
        >
          <PenLine className="w-3 h-3 sm:w-4 sm:h-4 text-[#6B7280]" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 sm:h-8 sm:w-8 p-0 hover:bg-gray-100"
          onClick={() => router.push(`/dashboard/messages?report=${row.getValue("id")}`)}
        >
          <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 text-[#6B7280]" />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 sm:h-8 sm:w-8 p-0 hover:bg-gray-100"
            >
              <MoreVertical className="w-3 h-3 sm:w-4 sm:h-4 text-[#6B7280]" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => console.log("Download", row.getValue("id"))}>
              Download Report
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => console.log("Share", row.getValue("id"))}>
              Share Report
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => console.log("Archive", row.getValue("id"))}>
              Archive Report
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  ];
};

export const columns: ColumnDef<ReportData>[] = [];