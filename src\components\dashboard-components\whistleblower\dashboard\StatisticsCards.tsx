import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { ChevronUp, ChevronDown, AlertTriangle } from "lucide-react";
import { FileText, Clock, CheckCircle } from "lucide-react";
import { UI_CONSTANTS } from "@/lib/client";
import { DashboardStat } from "@/lib/types";

// Minimal stats shape accepted by this component (works with both hooks)
interface StatsLike {
    totalReports: number;
    newReports: number;
    underReviewReports: number;
    awaitingResponseReports: number;
    resolvedReports: number;
    highPriorityReports: number;
    periodComparison: {
        totalReportsChange: number;
        newReportsChange: number;
        resolvedReportsChange: number;
        period: string;
    };
    chartData?: {
        overTime: Array<{ month: string; reports: number; cases: number }>;
        statusDistribution: Array<{ name: string; value: number; fill: string }>;
    };
    lastCalculated?: Date;
}

interface StatisticsCardsProps {
    className?: string;
    stats?: StatsLike | null;
    isLoading?: boolean;
}

export default function StatisticsCards({ className, stats: propStats, isLoading: propIsLoading }: StatisticsCardsProps) {
    // Use provided props
    const stats = propStats;
    const isLoading = propIsLoading;

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
                {[...Array(4)].map((_, index) => (
                    <Card key={index} className="p-3 sm:p-4 md:p-6 animate-pulse">
                        <CardContent className="p-0">
                            <div className="flex justify-between">
                                <div className="w-full">
                                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                                    <div className="h-8 bg-gray-200 rounded"></div>
                                </div>
                                <div className="w-10 h-10 bg-gray-200 rounded-2xl"></div>
                            </div>
                            <div className="mt-4 h-4 bg-gray-200 rounded w-3/4"></div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        );
    }

    console.log('StatisticsCards: Received stats prop:', stats);
    console.log('StatisticsCards: isLoading prop:', isLoading);

    if (!stats) {
        console.log('StatisticsCards: No stats available, showing fallback');
        return (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
                <Card className="p-3 sm:p-4 md:p-6 col-span-full">
                    <CardContent className="p-0 text-center">
                        <p className="text-gray-500">No statistics available</p>
                    </CardContent>
                </Card>
            </div>
        );
    }
    const periodComparison = stats.periodComparison || {
        totalReportsChange: 0,
        resolvedReportsChange: 0,
    };
    const dashboardStats: DashboardStat[] = [
        {
            title: "Total Reports Submitted",
            value: (stats.totalReports ?? 0).toString(),
            subtitle: "Reports this month",
            icon: FileText,
            color: UI_CONSTANTS.colors.primary,
            subcolor: UI_CONSTANTS.colors.accent,
            indicator: {
                type: periodComparison.totalReportsChange >= 0 ? "increase" : "decrease",
                text: `${periodComparison.totalReportsChange >= 0 ? '+' : ''}${periodComparison.totalReportsChange}`,
                color: periodComparison.totalReportsChange >= 0 ? UI_CONSTANTS.colors.success : UI_CONSTANTS.colors.error
            }
        },
        {
            title: "Under Review",
            value: (stats.underReviewReports ?? 0).toString(),
            subtitle: "Pending cases",
            icon: Clock,
            color: UI_CONSTANTS.colors.primary,
            subcolor: UI_CONSTANTS.colors.accent,
            indicator: {
                type: "neutral",
                text: `${stats.awaitingResponseReports ?? 0} pending response`,
                color: UI_CONSTANTS.colors.textSecondary
            }
        },
        {
            title: "Resolved Cases",
            value: (stats.resolvedReports ?? 0).toString(),
            subtitle: "Completed this quarter",
            icon: CheckCircle,
            color: UI_CONSTANTS.colors.primary,
            subcolor: UI_CONSTANTS.colors.accent,
            indicator: {
                type: periodComparison.resolvedReportsChange >= 0 ? "increase" : "decrease",
                text: `${periodComparison.resolvedReportsChange >= 0 ? '+' : ''}${periodComparison.resolvedReportsChange}`,
                color: periodComparison.resolvedReportsChange >= 0 ? UI_CONSTANTS.colors.success : UI_CONSTANTS.colors.error
            }
        },
        {
            title: "High Priority",
            value: (stats.highPriorityReports ?? 0).toString(),
            subtitle: "Urgent attention needed",
            icon: AlertTriangle,
            color: UI_CONSTANTS.colors.primary,
            subcolor: UI_CONSTANTS.colors.accent,
            indicator: {
                type: "warning",
                text: (stats.highPriorityReports ?? 0) > 0 ? "Urgent" : "None",
                color: (stats.highPriorityReports ?? 0) > 0 ? UI_CONSTANTS.colors.error : UI_CONSTANTS.colors.success
            }
        },
    ];

    return (
        <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 ${className || ''}`}>
            {dashboardStats.map((stat, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow duration-300 p-3 sm:p-4 md:p-6">
                    <CardContent className="flex flex-col justify-between p-0">
                        <div className="flex justify-between">
                            <CardHeader className="w-full p-0">
                                <p className="text-xs sm:text-sm font-medium text-[#242E2C]">{stat.title}</p>
                                <p className={`text-2xl sm:text-3xl xl:text-3xl font-semibold`} style={{ color: stat.color }}>{stat.value}</p>
                            </CardHeader>
                            <div className={`p-2 sm:p-2.5 rounded-2xl w-fit h-fit`} style={{ backgroundColor: UI_CONSTANTS.colors.accent, color: UI_CONSTANTS.colors.primary }}>
                                <stat.icon className="w-4 h-4 sm:w-5 sm:h-5" />
                            </div>
                        </div>
                        <CardFooter className="p-0 mt-3 sm:mt-4">
                            <p className={`text-xs sm:text-sm font-normal flex items-center gap-1`} style={{ color: stat.indicator.color }}>
                                {stat.indicator.type === "increase" && <ChevronUp className="h-3 w-3 sm:h-4 sm:w-4" />}
                                {stat.indicator.type === "decrease" && <ChevronDown className="h-3 w-3 sm:h-4 sm:w-4" />}
                                {stat.indicator.type === "warning" && <AlertTriangle className="h-3 w-3 sm:h-4 sm:w-4" />}
                                {stat.indicator.text}
                                {stat.indicator.type === "increase" || stat.indicator.type === "decrease" ?
                                    <span className="ml-1 inline" style={{ color: UI_CONSTANTS.colors.textSecondary }}>{stat.title === "Total Reports Submitted" ? "since last month" : "than last quarter"}</span> :
                                    null}
                            </p>
                        </CardFooter>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}