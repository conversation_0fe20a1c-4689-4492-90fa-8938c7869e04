"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useEffect, useRef, useState } from "react";

export default function TwoFactorAuth() {
    const [show2FA, setShow2FA] = useState(false);
    const [code, setCode] = useState<string[]>(['', '', '', '', '', '']);
    const [timer, setTimer] = useState(268); // 4:28 in seconds
    const [resendDisabled, setResendDisabled] = useState(true);
    const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

    useEffect(() => {
        if (!show2FA) return;
        if (timer === 0) {
            setResendDisabled(false);
            return;
        }
        setResendDisabled(true);
        const interval = setInterval(() => {
            setTimer((prev) => (prev > 0 ? prev - 1 : 0));
        }, 1000);
        return () => clearInterval(interval);
    }, [show2FA, timer]);

    const handleCodeChange = (idx: number, val: string) => {
        if (!/^[0-9]?$/.test(val)) return;
        const newCode = [...code];
        newCode[idx] = val;
        setCode(newCode);
        if (val && idx < 5) {
            inputRefs.current[idx + 1]?.focus();
        }
        if (!val && idx > 0) {
            inputRefs.current[idx - 1]?.focus();
        }
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
        const paste = e.clipboardData.getData('text').replace(/\D/g, '');
        if (paste.length === 6) {
            setCode(paste.split(''));
            inputRefs.current[5]?.focus();
        }
    };

    const handleResend = () => {
        setTimer(268);
        setResendDisabled(true);
        setCode(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
    };

    const handleVerify = (e: React.FormEvent) => {
        e.preventDefault();
        // Add verification logic here
        setShow2FA(false);
    };

    const formattedTimer = `${String(Math.floor(timer / 60)).padStart(2, '0')}:${String(timer % 60).padStart(2, '0')}`;
    return (

        <Dialog open={show2FA} onOpenChange={setShow2FA}>
            <DialogContent className="max-w-md w-full p-0">
                <DialogHeader className="pt-8 px-8">
                    <DialogTitle className="text-xl font-semibold text-gray-800">Two Factor Authentication</DialogTitle>
                    <DialogDescription className="text-gray-500 text-sm mt-2 mb-0 text-center font-normal">
                        we&apos;ve sent a verification code to your email. Please enter the 6-digit code below
                    </DialogDescription>
                </DialogHeader>
                <form className="flex flex-col items-center w-full px-8 pb-8 pt-2" onSubmit={handleVerify}>
                    <div className="flex space-x-2 my-4 w-full justify-center" onPaste={handlePaste}>
                        {code.map((digit, idx) => (
                            <Input
                                key={idx}
                                ref={el => { inputRefs.current[idx] = el; }}
                                type="text"
                                inputMode="numeric"
                                maxLength={1}
                                className="w-12 h-12 text-center text-lg border-gray-300 focus:ring-green-700 focus:border-green-700"
                                value={digit}
                                onChange={e => handleCodeChange(idx, e.target.value)}
                                onKeyDown={e => {
                                    if (e.key === 'Backspace' && !code[idx] && idx > 0) {
                                        inputRefs.current[idx - 1]?.focus();
                                    }
                                }}
                            />
                        ))}
                    </div>
                    <div className="flex justify-between items-center w-full mb-4">
                        <span className="text-gray-400 text-sm">Code expires in : {formattedTimer}</span>
                        <button
                            type="button"
                            className={`text-green-900 text-sm ml-2 ${resendDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:underline'}`}
                            onClick={handleResend}
                            disabled={resendDisabled}
                        >
                            Resend Code
                        </button>
                    </div>
                    <Button type="submit" className="w-full bg-green-700 hover:bg-green-800 text-white font-semibold py-2 rounded-md mt-2">
                        Verify & Continue
                    </Button>
                </form>
            </DialogContent>
        </Dialog>
    )
}