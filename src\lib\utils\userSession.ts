// User session utilities for dashboard
export interface UserSession {
  name: string;
  email: string;
  lastLogin: Date;
  role: string;
}

export const initializeUserSession = (): UserSession => {
  // In a real app, this would get data from auth context or API
  return {
    name: "<PERSON>",
    email: "<EMAIL>",
    lastLogin: new Date("2025-01-15T09:23:00Z"),
    role: "whistleblower"
  };
};

export const formatLastLogin = (lastLogin: Date): string => {
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - lastLogin.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    return "Just now";
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
  } else {
    return lastLogin.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

export const clearUserSession = (): void => {
  localStorage.removeItem('userSession');
  localStorage.removeItem('auth_token');
  sessionStorage.clear();
};