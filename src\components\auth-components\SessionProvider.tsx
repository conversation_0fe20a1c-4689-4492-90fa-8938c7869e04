"use client";

import { ReactNode } from "react";
import { SessionProvider as NextAuthSessionProvider } from "next-auth/react";

interface SessionProviderProps {
  children: ReactNode;
  session?: Record<string, unknown> | null;
}

export function SessionProvider({ children, session }: SessionProviderProps) {
  return (
    <NextAuthSessionProvider session={session ? { ...session, expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() } : undefined}>
      {children}
    </NextAuthSessionProvider>
  );
}