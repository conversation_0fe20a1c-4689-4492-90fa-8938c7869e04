import React from 'react';

interface BlogContentProps {
  content: string;
}

/**
 * BlogContent component provides a standardized way to render blog post content
 * with consistent styling for headings, paragraphs, lists, blockquotes, etc.
 */
const BlogContent: React.FC<BlogContentProps> = ({ content }) => {
  return (
    <article className="prose prose-sm sm:prose md:prose-lg max-w-none">
      {/* Apply consistent styling to the blog content */}
      <div 
        className="blog-content"
        dangerouslySetInnerHTML={{ __html: content }}
      />

      <style jsx global>{`
        /* Base typography */
        .blog-content {
          color: #4B5563;
          line-height: 1.8;
        }

        /* Headings */
        .blog-content h1, 
        .blog-content h2, 
        .blog-content h3, 
        .blog-content h4, 
        .blog-content h5, 
        .blog-content h6 {
          color: #1E4841;
          font-weight: 700;
          margin-top: 2em;
          margin-bottom: 0.75em;
          line-height: 1.3;
        }

        .blog-content h1 {
          font-size: 2.25rem;
        }

        .blog-content h2 {
          font-size: 1.875rem;
          border-bottom: 1px solid #E5E7EB;
          padding-bottom: 0.5rem;
        }

        .blog-content h3 {
          font-size: 1.5rem;
        }

        .blog-content h4 {
          font-size: 1.25rem;
        }

        /* Paragraphs */
        .blog-content p {
          margin-bottom: 1.5em;
        }

        .blog-content .lead {
          font-size: 1.125rem;
          font-weight: 500;
          color: #374151;
        }

        /* Lists */
        .blog-content ul,
        .blog-content ol {
          margin-top: 1em;
          margin-bottom: 1em;
          padding-left: 1.5em;
        }

        .blog-content li {
          margin-bottom: 0.5em;
        }

        .blog-content ul li {
          list-style-type: disc;
        }

        .blog-content ol li {
          list-style-type: decimal;
        }

        /* Blockquotes */
        .blog-content blockquote {
          border-left: 4px solid #1E4841;
          padding-left: 1.5rem;
          margin-left: 0;
          margin-right: 0;
          font-style: italic;
          color: #4B5563;
          margin-top: 2rem;
          margin-bottom: 2rem;
        }

        .blog-content blockquote cite {
          display: block;
          font-size: 0.875rem;
          margin-top: 1rem;
          font-style: normal;
          font-weight: 500;
        }

        /* Links */
        .blog-content a {
          color: #1E4841;
          text-decoration: underline;
          text-underline-offset: 2px;
        }

        .blog-content a:hover {
          color: #132f2a;
        }

        /* Images */
        .blog-content img {
          border-radius: 0.5rem;
          margin-top: 2rem;
          margin-bottom: 2rem;
        }

        /* Code blocks */
        .blog-content pre {
          background-color: #F9FAFB;
          border-radius: 0.5rem;
          padding: 1rem;
          overflow-x: auto;
          margin-top: 1.5rem;
          margin-bottom: 1.5rem;
        }

        .blog-content code {
          font-family: monospace;
          background-color: #F3F4F6;
          padding: 0.2em 0.4em;
          border-radius: 0.25rem;
          font-size: 0.875em;
        }

        /* Tables */
        .blog-content table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 1.5rem;
          margin-bottom: 1.5rem;
        }

        .blog-content th {
          background-color: #F3F4F6;
          font-weight: 600;
          text-align: left;
          padding: 0.75rem;
          border-bottom: 2px solid #E5E7EB;
        }

        .blog-content td {
          padding: 0.75rem;
          border-bottom: 1px solid #E5E7EB;
        }

        /* Special components */
        .blog-content .info-box {
          background-color: #ECF4E9;
          border-radius: 0.5rem;
          padding: 1.5rem;
          margin-top: 1.5rem;
          margin-bottom: 1.5rem;
        }

        .blog-content .info-box h4 {
          color: #1E4841;
          margin-top: 0;
          margin-bottom: 1rem;
        }

        .blog-content .example-box {
          background-color: #F3F4F6;
          border-radius: 0.5rem;
          padding: 1.5rem;
          margin-top: 1.5rem;
          margin-bottom: 1.5rem;
        }

        .blog-content .example-box h4 {
          margin-top: 0;
          margin-bottom: 1rem;
        }

        /* Timeline component */
        .blog-content .timeline {
          margin: 2rem 0;
          position: relative;
        }

        .blog-content .timeline::before {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          left: 1rem;
          width: 2px;
          background-color: #E5E7EB;
        }

        .blog-content .timeline-item {
          position: relative;
          padding-left: 2.5rem;
          margin-bottom: 1.5rem;
        }

        .blog-content .timeline-marker {
          position: absolute;
          left: 0;
          width: 2rem;
          height: 2rem;
          background-color: #1E4841;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 0.75rem;
        }

        .blog-content .timeline-content {
          background-color: #F9FAFB;
          padding: 1rem;
          border-radius: 0.5rem;
          border-left: 3px solid #1E4841;
        }

        /* Grid layout */
        .blog-content .grid-box {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 1.5rem;
          margin: 2rem 0;
        }

        .blog-content .grid-item {
          background-color: #F9FAFB;
          padding: 1.5rem;
          border-radius: 0.5rem;
          border-top: 3px solid #1E4841;
        }

        .blog-content .grid-item h3 {
          margin-top: 0;
          font-size: 1.25rem;
        }

        /* Strategy cards */
        .blog-content .strategy-card {
          display: flex;
          gap: 1.5rem;
          margin-bottom: 2.5rem;
          margin-top: 2.5rem;
        }

        .blog-content .strategy-number {
          background-color: #1E4841;
          color: white;
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          font-weight: 700;
          flex-shrink: 0;
        }

        .blog-content .strategy-content {
          flex: 1;
        }

        .blog-content .strategy-content h2 {
          margin-top: 0;
        }

        .blog-content .action-steps {
          background-color: #F3F4F6;
          padding: 1rem;
          border-radius: 0.5rem;
          margin-top: 1rem;
        }

        .blog-content .action-steps h4 {
          margin-top: 0;
          margin-bottom: 0.5rem;
        }

        /* Checklist */
        .blog-content .checklist {
          padding-left: 0;
        }

        .blog-content .checklist li {
          list-style-type: none;
          position: relative;
          padding-left: 2rem;
          margin-bottom: 0.75rem;
        }

        .blog-content .checklist li::before {
          content: '✓';
          position: absolute;
          left: 0;
          color: #1E4841;
          font-weight: bold;
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
          .blog-content h1 {
            font-size: 1.75rem;
          }
          
          .blog-content h2 {
            font-size: 1.5rem;
          }
          
          .blog-content h3 {
            font-size: 1.25rem;
          }
          
          .blog-content h4 {
            font-size: 1.125rem;
          }
          
          .blog-content p, .blog-content li {
            font-size: 1rem;
          }
          
          .blog-content .lead {
            font-size: 1.125rem;
          }
          
          .blog-content blockquote {
            padding-left: 1rem;
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
          }
          
          .blog-content .strategy-card {
            flex-direction: column;
            gap: 1rem;
          }

          .blog-content .grid-box {
            grid-template-columns: 1fr;
          }

          .blog-content .timeline::before {
            left: 0.75rem;
          }

          .blog-content .timeline-item {
            padding-left: 2rem;
          }

          .blog-content .timeline-marker {
            width: 1.5rem;
            height: 1.5rem;
          }
          
          .blog-content img {
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
          }
          
          .blog-content pre {
            padding: 0.75rem;
            margin-top: 1rem;
            margin-bottom: 1rem;
          }
          
          .blog-content table {
            font-size: 0.875rem;
          }
          
          .blog-content th, .blog-content td {
            padding: 0.5rem;
          }
        }
      `}</style>
    </article>
  );
};

export default BlogContent;