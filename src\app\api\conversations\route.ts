import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      console.error('Conversations API: No user in request');
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 10;
    
    console.log('Conversations API: User info', {
      userId: request.user.id,
      role: request.user.role,
      companyId: request.user.companyId,
      limit
    });
    
    const conversations = await DataService.getConversations(request.user.id, request.user.companyId);
    
    // Limit the results if requested
    const limitedConversations = limit ? conversations.slice(0, limit) : conversations;
    
    console.log('Conversations API: Conversations retrieved', limitedConversations.length);
    
    return NextResponse.json({
      success: true,
      data: limitedConversations
    });
  } catch (error) {
    console.error('Conversations API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});