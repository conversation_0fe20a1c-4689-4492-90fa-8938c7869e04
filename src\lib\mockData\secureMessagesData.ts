// Secure messaging mock data - Database-ready format
import { Conversation, Message, User, ConversationWithDetails } from "@/lib/types";

// Mock investigators and team members
export const mockInvestigators: User[] = [
  {
    _id: "investigator_001",
    email: "<EMAIL>",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    role: "investigator",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2025-01-14")
  },
  {
    _id: "investigator_002",
    email: "<EMAIL>",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    role: "investigator",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2025-01-14")
  },
  {
    _id: "investigator_003",
    email: "<EMAIL>",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    role: "investigator",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2025-01-14")
  },
  {
    _id: "investigator_004",
    email: "<EMAIL>",
    firstName: "Security",
    lastName: "Officer",
    role: "investigator",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2025-01-14")
  },
  {
    _id: "investigator_005",
    email: "<EMAIL>",
    firstName: "System",
    lastName: "Administrator",
    role: "admin",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2025-01-14")
  }
];

// Mock conversations for secure messaging
export const secureConversations: Conversation[] = [
  {
    _id: "conv_001",
    reportId: "report_001",
    participants: ["user_001", "investigator_001"],
    status: "active",
    isEncrypted: true,
    createdAt: new Date("2025-05-10"),
    updatedAt: new Date("2025-05-14"),
    lastMessageAt: new Date("2025-05-14T14:30:00Z")
  },
  {
    _id: "conv_002",
    reportId: "report_002",
    participants: ["user_001", "investigator_002"],
    status: "active",
    isEncrypted: true,
    createdAt: new Date("2025-04-28"),
    updatedAt: new Date("2025-05-12"),
    lastMessageAt: new Date("2025-05-12T15:45:00Z")
  },
  {
    _id: "conv_003",
    reportId: "report_003",
    participants: ["user_001", "investigator_003"],
    status: "active",
    isEncrypted: true,
    createdAt: new Date("2025-04-15"),
    updatedAt: new Date("2025-04-20"),
    lastMessageAt: new Date("2025-04-20T11:20:00Z")
  },
  {
    _id: "conv_004",
    reportId: "report_004",
    participants: ["user_001", "investigator_004"],
    status: "active",
    isEncrypted: true,
    createdAt: new Date("2025-04-10"),
    updatedAt: new Date("2025-04-15"),
    lastMessageAt: new Date("2025-04-15T16:45:00Z")
  },
  {
    _id: "conv_005",
    reportId: "report_005",
    participants: ["user_001", "investigator_005"],
    status: "active",
    isEncrypted: true,
    createdAt: new Date("2025-04-05"),
    updatedAt: new Date("2025-04-10"),
    lastMessageAt: new Date("2025-04-10T09:30:00Z")
  }
];

// Mock messages for conversations
export const secureMessages: Message[] = [
  // Conversation 1 - Compliance Team (Alexandra)
  {
    _id: "msg_001",
    conversationId: "conv_001",
    senderId: "investigator_001",
    content: "Thank you for submitting your whistleblower report regarding potential financial irregularities in the Compliance Team. We have received all the documentation and will begin our investigation shortly. Specifically, could you provide:",
    messageType: "text",
    isEncrypted: true,
    readBy: [{ userId: "user_001", readAt: new Date("2025-05-14T14:35:00Z") }],
    createdAt: new Date("2025-05-14T14:30:00Z"),
    updatedAt: new Date("2025-05-14T14:30:00Z")
  },
  {
    _id: "msg_002",
    conversationId: "conv_001",
    senderId: "investigator_001",
    content: "1. Approximate dates when you first noticed the irregularities\n2. Names of any other individuals who might have knowledge of these issues\n3. Any documentation or evidence that supports your observations",
    messageType: "text",
    isEncrypted: true,
    readBy: [{ userId: "user_001", readAt: new Date("2025-05-14T14:35:00Z") }],
    createdAt: new Date("2025-05-14T14:31:00Z"),
    updatedAt: new Date("2025-05-14T14:31:00Z")
  },
  {
    _id: "msg_003",
    conversationId: "conv_001",
    senderId: "investigator_001",
    content: "All information you share remains confidential and protected under our whistleblower policy.\n\nBest regards,\nJames Wilson/Compliance Team",
    messageType: "text",
    isEncrypted: true,
    readBy: [{ userId: "user_001", readAt: new Date("2025-05-14T14:35:00Z") }],
    createdAt: new Date("2025-05-14T14:32:00Z"),
    updatedAt: new Date("2025-05-14T14:32:00Z")
  },
  {
    _id: "msg_004",
    conversationId: "conv_001",
    senderId: "user_001",
    content: "Hello James,\n\nThank you for your prompt response. I'm happy to provide the additional information:\n\n1. First noticed discrepancies in the budget allocation reports in early March 2025, specifically around March 10.\n2. I believe Sarah Martinez in Accounting may have also noticed these irregularities as she mentioned some concerns during our quarterly review meeting.\n3. I have several spreadsheets and copies of invoices that show the discrepancies. Is there anything else you need from me at this time?",
    messageType: "text",
    isEncrypted: true,
    readBy: [],
    createdAt: new Date("2025-05-14T15:45:00Z"),
    updatedAt: new Date("2025-05-14T15:45:00Z")
  },
  {
    _id: "msg_005",
    conversationId: "conv_001",
    senderId: "user_001",
    content: "Regards,\nEmily",
    messageType: "text",
    isEncrypted: true,
    readBy: [],
    createdAt: new Date("2025-05-14T15:46:00Z"),
    updatedAt: new Date("2025-05-14T15:46:00Z")
  },
  // Conversation 2 - Michael Chen
  {
    _id: "msg_006",
    conversationId: "conv_002",
    senderId: "investigator_002",
    content: "Thank you for your detailed report on workplace safety concerns. Our team has initiated investigating the issues at manufacturing plant B. We've scheduled an on-site inspection for next week.",
    messageType: "text",
    isEncrypted: true,
    readBy: [{ userId: "user_001", readAt: new Date("2025-05-12T16:00:00Z") }],
    createdAt: new Date("2025-05-12T15:45:00Z"),
    updatedAt: new Date("2025-05-12T15:45:00Z")
  },
  // Conversation 3 - Sarah Martinez
  {
    _id: "msg_007",
    conversationId: "conv_003",
    senderId: "user_001",
    content: "I've attached the requested files.",
    messageType: "text",
    isEncrypted: true,
    readBy: [{ userId: "investigator_003", readAt: new Date("2025-04-20T12:00:00Z") }],
    createdAt: new Date("2025-04-20T11:20:00Z"),
    updatedAt: new Date("2025-04-20T11:20:00Z")
  },
  // Conversation 4 - Security Officer
  {
    _id: "msg_008",
    conversationId: "conv_004",
    senderId: "investigator_004",
    content: "We've implemented additional security measures based on your report.",
    messageType: "text",
    isEncrypted: true,
    readBy: [{ userId: "user_001", readAt: new Date("2025-04-15T17:00:00Z") }],
    createdAt: new Date("2025-04-15T16:45:00Z"),
    updatedAt: new Date("2025-04-15T16:45:00Z")
  },
  // Conversation 5 - System Administrator
  {
    _id: "msg_009",
    conversationId: "conv_005",
    senderId: "investigator_005",
    content: "Important: Security update for secure messaging system.",
    messageType: "text",
    isEncrypted: true,
    readBy: [{ userId: "user_001", readAt: new Date("2025-04-10T10:00:00Z") }],
    createdAt: new Date("2025-04-10T09:30:00Z"),
    updatedAt: new Date("2025-04-10T09:30:00Z")
  }
];

// Extended conversation data with report details for UI display

export const conversationsWithDetails: ConversationWithDetails[] = [
  {
    id: "conv_001",
    reportId: "WB-2025-0012",
    reportTitle: "Financial Irregularities in Accounting Department",
    lastMessage: "Thank you for your prompt response. I'm happy to provide the additional information...",
    lastMessageTime: "2025-05-14T15:45:00Z",
    unreadCount: 2,
    status: "Active",
    investigator: {
      name: "Compliance Team",
      role: "Compliance Team",
      avatar: "/dashboard/header/user.svg"
    }
  },
  {
    id: "conv_002",
    reportId: "WB-2025-0011",
    reportTitle: "Workplace Safety Concerns in Manufacturing Plant B",
    lastMessage: "Thank you for your detailed report on workplace safety concerns. Our team has initiated investigating...",
    lastMessageTime: "2025-05-12T15:45:00Z",
    unreadCount: 0,
    status: "Active",
    investigator: {
      name: "Ethics Committee",
      role: "Ethics Committee",
      avatar: "/dashboard/header/user.svg"
    }
  },
  {
    id: "conv_003",
    reportId: "WB-2025-0010",
    reportTitle: "Data Privacy Violation Report",
    lastMessage: "I've attached the requested files.",
    lastMessageTime: "2025-04-20T11:20:00Z",
    unreadCount: 0,
    status: "Active",
    investigator: {
      name: "Sarah Martinez",
      role: "Senior Investigator",
      avatar: "/dashboard/header/user.svg"
    }
  },
  {
    id: "conv_004",
    reportId: "WB-2025-0009",
    reportTitle: "Security Breach Incident",
    lastMessage: "We've implemented additional security measures based on your report.",
    lastMessageTime: "2025-04-15T16:45:00Z",
    unreadCount: 0,
    status: "Active",
    investigator: {
      name: "Security Officer",
      role: "Security Team",
      avatar: "/dashboard/header/user.svg"
    }
  },
  {
    id: "conv_005",
    reportId: "WB-2025-0008",
    reportTitle: "System Access Issues",
    lastMessage: "Important: Security update for secure messaging system.",
    lastMessageTime: "2025-04-10T09:30:00Z",
    unreadCount: 0,
    status: "Active",
    investigator: {
      name: "System Administrator",
      role: "IT Administration",
      avatar: "/dashboard/header/user.svg"
    }
  }
];
