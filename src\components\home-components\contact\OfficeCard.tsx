"use client";

import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Phone, Mail, Clock, ArrowRight } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

interface OfficeCardProps {
  title: string;
  address: string;
  phone: string;
  email: string;
  hours: string;
  mapImage: string;
}

export default function OfficeCard({
  title,
  address,
  phone,
  email,
  hours
}: Omit<OfficeCardProps, 'mapImage'>) {
  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300 py-0 border-none shadow-md">
      <div className="relative h-48 bg-gray-200">
        <div className="w-full h-full bg-gradient-to-br from-[#1E4841]/10 to-[#1E4841]/20 flex items-center justify-center">
          <div className="text-center">
            <MapPin className="w-12 h-12 text-[#1E4841] mx-auto mb-2" />
            <p className="text-sm text-[#1E4841] font-medium">Office Location</p>
            <p className="text-xs text-gray-600 mt-1">Interactive Map</p>
          </div>
        </div>
      </div>

      <CardContent className="p-6 pt-0">
        <h3 className="text-xl font-semibold text-[#1E4841] mb-4">
          {title}
        </h3>

        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <MapPin className="w-5 h-5 text-gray-500 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-gray-600 whitespace-pre-line">
              {address}
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Phone className="w-5 h-5 text-gray-500 flex-shrink-0" />
            <Link
              href={`tel:${phone}`}
              className="text-sm text-[#1E4841] hover:underline"
            >
              {phone}
            </Link>
          </div>

          <div className="flex items-center gap-3">
            <Mail className="w-5 h-5 text-gray-500 flex-shrink-0" />
            <Link
              href={`mailto:${email}`}
              className="text-sm text-[#1E4841] hover:underline"
            >
              {email}
            </Link>
          </div>

          <div className="flex items-center gap-3">
            <Clock className="w-5 h-5 text-gray-500 flex-shrink-0" />
            <p className="text-sm text-gray-600">
              {hours}
            </p>
          </div>
        </div>
        
        <Button
          variant="ghost"
          className="mt-4 flex items-center gap-2 hover:gap-4 hover:bg-transparent transition-all duration-300"
        >
          <Link href="" className="flex items-center gap-2 hover:gap-4 transition-all duration-300">
            Get Directions <ArrowRight />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}