import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { DataService } from '@/lib/db/dataService';
import { emailService } from '@/lib/email/emailService';
import connectDB from '@/lib/db/mongodb';
import logger from '@/lib/utils/logger';
import crypto from 'crypto';

// Validation schema for forgot password request
const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Validate the request body
    const validationResult = forgotPasswordSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid email address',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { email } = validationResult.data;

    // Check if user exists
    const user = await DataService.getUserByEmail(email);
    
    // Always return success to prevent email enumeration attacks
    // But only send email if user actually exists
    if (user) {
      try {
        // Generate secure reset token
        const resetToken = crypto.randomBytes(32).toString('hex');
        const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now
        
        // Save reset token to user
        await DataService.updateUser(user._id.toString(), {
          passwordResetToken: resetToken,
          passwordResetExpires: resetTokenExpiry
        });

        // Send password reset email
        const emailSent = await emailService.sendPasswordResetEmail(email, resetToken);
        
        if (emailSent) {
          logger.info('Password reset email sent', { email });
        } else {
          logger.error('Failed to send password reset email', { email });
        }
        
      } catch (error) {
        logger.error('Error processing password reset request:', error);
        // Still return success to prevent information disclosure
      }
    } else {
      logger.info('Password reset requested for non-existent email', { email });
    }

    // Always return success response
    return NextResponse.json({
      success: true,
      message: 'If an account with that email exists, you will receive a password reset link shortly.'
    });

  } catch (error) {
    logger.error('Forgot password API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An error occurred while processing your request. Please try again.' 
      },
      { status: 500 }
    );
  }
}
