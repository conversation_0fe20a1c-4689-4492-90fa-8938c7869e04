// Product-specific mock data
import { 
  ProductFeature, 
  ProductStep, 
  ProductSecurity, 
  ProductTestimonial,
  ProductResponsibility,
  ProductUser,
  ProductCase,
  ProductInsight,
  ProductSpace,
  ProductReport
} from "@/lib/types";
import { 
  Circle, 
  ShieldCheck, 
  MessageSquareLock, 
  LockKeyhole, 
  FileUp, 
  Timer, 
  Users, 
  PanelTopOpen} from "lucide-react";

// Whistleblower Portal Data
export const whistleblowerFeatures: ProductFeature[] = [
  {
    icon: Circle,
    title: "Anonymous or Profile-Based Reporting",
    description: "Choose to stay anonymous or create a secure profile for follow-up — your identity stays in your control."
  },
  {
    icon: ShieldCheck,
    title: "Encrypted & Secure by Design",
    description: "Full end-to-end encryption, no IP tracking, GDPR-ready infrastructure."
  },
  {
    icon: MessageSquareLock,
    title: "Two-Way Anonymous Communication",
    description: "Communicate back and forth with investigators via secure threads."
  },
  {
    icon: LockKeyhole,
    title: "Personal Dashboard for Reporters",
    description: "Check your report status and chat with investigators securely using a token or profile login — no personal data required."
  }
];

export const whistleblowerSteps: ProductStep[] = [
  {
    icon: 1,
    title: "Start Your Report",
    description: {
      p1: `Choose: "Anonymous" or "Login to Identify"`,
      p2: "Optional login via Email, Google, or Microsoft",
      p3: "Private by design – no personal tracking unless you choose to log in."
    },
    image: "/desktop/products/whistleblower/step-card1.svg"
  },
  {
    icon: 2,
    title: "Fill Out the Secure Form",
    description: {
      p1: "Upload documents/images",
      p2: "Describe what happened",
      p3: "Choose category: Harassment, Fraud, etc."
    },
    image: "/desktop/products/whistleblower/step-card2.svg"
  },
  {
    icon: 3,
    title: "Submit and Receive Your Case ID",
    description: {
      p1: "Get instant reference number",
      p2: "Save or email case ID for future login",
      p3: "Confirmation of successful submission"
    },
    image: "/desktop/products/whistleblower/step-card3.svg"
  },
  {
    icon: 4,
    title: "Access Your Private Dashboard",
    description: {
      p1: "View status updates",
      p2: "Add new information",
      p3: "Chat securely with assigned investigator"
    },
    image: "/desktop/products/whistleblower/step-card4.svg"
  }
];

export const whistleblowerSpaces: ProductSpace[] = [
  {
    icon: ShieldCheck,
    title: "Secure Access via Case ID",
    description: "Return anytime using your unique case identifier — no personal details required."
  },
  {
    icon: FileUp,
    title: "Add Supporting Evidence",
    description: "Upload additional documents, images, or information as your case progresses."
  },
  {
    icon: Timer,
    title: "Complete Timeline View",
    description: "See all actions, updates, and communications in chronological order."
  }
];

export const whistleblowerReports: ProductReport[] = [
  {
    icon: PanelTopOpen,
    title: "Harassment or Discrimination",
    description: "Report inappropriate behavior safely and discreetly, with options for anonymous reporting."
  },
  {
    icon: ShieldCheck,
    title: "Financial or Ethical Misconduct",
    description: "Alert leadership about fraud, embezzlement, or policy violations with supporting evidence."
  },
  {
    icon: Users,
    title: "Vendor or Third-Party Compliance",
    description: "Empower partners to raise flags within your supply chain about compliance issues."
  },
  {
    icon: MessageSquareLock,
    title: "Unsafe Working Conditions",
    description: "Let workers speak up about unsafe practices or environmental concerns without fear."
  }
];

export const whistleblowerSecurities: ProductSecurity[] = [
  {
    icon: MessageSquareLock,
    title: "GDPR, ISO 27001, HIPAA-ready",
    description: "Fully compliant with global data protection regulations and security standards."
  },
  {
    icon: MessageSquareLock,
    title: "No IP Logging for Anonymous Reports",
    description: "We never track, store, or monitor IP addresses for anonymous reporting sessions."
  },
  {
    icon: MessageSquareLock,
    title: "End-to-End Encryption",
    description: "All data is encrypted in transit and at rest using industry-leading encryption standards."
  },
  {
    icon: MessageSquareLock,
    title: "Secure Data Storage",
    description: "Data is stored in SOC 2 Type II certified data centers with regular security audits."
  }
];

// Investigator Portal Data
export const investigatorSteps: ProductStep[] = [
  {
    icon: "/desktop/products/investigator/icons/case.svg",
    alt: "Case Icon",
    width: 23,
    height: 24,
    title: "Receive Assigned Case",
    description: "Get notified of new cases assigned to you with priority indicators.",
    features: {
      p1: "Real-time notifications via email and in-app",
      p2: "Automatic risk assessment scoring",
      p3: "Case categorization and tagging"
    }
  },
  {
    icon: "/desktop/products/investigator/icons/review.svg",
    alt: "Review Icon",
    width: 24,
    height: 25,
    title: "Review Submitted Information",
    description: "Access all submitted evidence, documents, and initial report details.",
    features: {
      p1: "Secure document viewer with annotation",
      p2: "Chronological timeline of submissions",
      p3: "AI-assisted summary of key points"
    }
  },
  {
    icon: "/desktop/products/investigator/icons/engage.svg",
    alt: "Engage Icon",
    width: 25,
    height: 22,
    title: "Engage with Whistleblower",
    description: "Communicate securely while maintaining anonymity when required.",
    features: {
      p1: "End-to-end encrypted messaging",
      p2: "File request capabilities",
      p3: "Template responses for common scenarios"
    }
  },
  {
    icon: "/desktop/products/investigator/icons/notes.svg",
    alt: "Notes Icon",
    width: 24,
    height: 24,
    title: "Add Notes & Evidence",
    description: "Document your investigation with secure, time-stamped notes and files.",
    features: {
      p1: "View status updates",
      p2: "Add new information",
      p3: "Chat securely with assigned investigator"
    }
  },
  {
    icon: "/desktop/products/investigator/icons/escalate.svg",
    alt: "Escalate Icon",
    width: 24,
    height: 24,
    title: "Close or Escalate Case",
    description: "Complete the investigation with detailed resolution or escalate to appropriate teams.",
    features: {
      p1: "View status updates",
      p2: "Add new information",
      p3: "Chat securely with assigned investigator"
    }
  }
];

export const investigatorFeatures: ProductFeature[] = [
  {
    icon: "/desktop/products/investigator/icons/anonymous.svg",
    alt: "Anonymous Icon",
    width: 25,
    height: 25,
    title: "Anonymous 2-Way Messaging",
    description: "Secure, encrypted communication channel that maintains whistleblower anonymity while allowing for detailed follow-up questions and information gathering."
  },
  {
    icon: "/desktop/products/investigator/icons/rbac.svg",
    alt: "RBAC Icon",
    width: 25,
    height: 25,
    title: "Role-Based Access Control",
    description: "Granular permissions ensure investigators only see cases and information relevant to their role, with configurable approval workflows for sensitive actions."
  },
  {
    icon: "/desktop/products/investigator/icons/evidence.svg",
    alt: "Evidence Icon",
    width: 23,
    height: 25,
    title: "Evidence Management",
    description: "Comprehensive system for organizing, tagging, and tracking all case evidence with version control and chain of custody documentation."
  },
  {
    icon: "/desktop/products/investigator/icons/status.svg",
    alt: "Status Icon",
    width: 25,
    height: 25,
    title: "Status Tracking & Timelines",
    description: "Visual timeline of all case activities with configurable status updates, deadlines, and automatic reminders for time-sensitive actions."
  },
  {
    icon: "/desktop/products/investigator/icons/realtime.svg",
    alt: "Real-Time Icon",
    width: 25,
    height: 25,
    title: "Real-Time Escalation",
    description: "Immediate notification system for critical cases with configurable escalation paths to legal, compliance, and executive teams when needed."
  },
  {
    icon: "/desktop/products/investigator/icons/collaboration.svg",
    alt: "Collaboration Icon",
    width: 57,
    height: 56,
    title: "Collaboration Tools",
    description: "Internal discussion threads, shared notes, and task assignment features that keep investigation teams coordinated while maintaining appropriate information barriers."
  }
];

export const investigatorSecurities: ProductSecurity[] = [
  {
    icon: "/desktop/products/investigator/icons/encryption.svg",
    alt: "Encryption Icon",
    width: 19,
    height: 22,
    title: "End-to-End Encryption",
    description: "All communications with whistleblowers are fully encrypted, ensuring complete privacy and security."
  },
  {
    icon: "/desktop/products/investigator/icons/rba.svg",
    alt: "RBA Icon",
    width: 19,
    height: 22,
    title: "Role-Based Access",
    description: "Strict role-based access with 2FA/MFA for all investigators to prevent unauthorized access."
  },
  {
    icon: "/desktop/products/investigator/icons/storage.svg",
    alt: "Storage Icon",
    width: 19,
    height: 20,
    title: "Internal-Only Storage",
    description: "All data remains within your organization's control with no third-party storage dependencies."
  },
  {
    icon: "/desktop/products/investigator/icons/logs.svg",
    alt: "Logs Icon",
    width: 19,
    height: 20,
    title: "Comprehensive Audit Logs",
    description: "Case-level audit logs for every edit, message, or action to maintain complete accountability."
  },
  {
    icon: "/desktop/products/investigator/icons/compliance.svg",
    alt: "Compliance Icon",
    width: 19,
    height: 20,
    title: "GDPR Compliance",
    description: "GDPR-ready with exportable data packages and breach flags to maintain regulatory compliance."
  }
];

export const investigatorTestimonials: ProductTestimonial[] = [
  {
    quote: "The Investigator Portal gave us the clarity and accountability we needed to act fast and protect both our people and our business.",
    role: "Senior Risk & Compliance Officer",
    company: "Global Tech Group",
    avatar: "/desktop/products/investigator/icons/user.svg",
    width: 50,
    height: 50
  },
  {
    quote: "The platform's intuitive design and robust features have made managing whistleblower cases a breeze. The real-time escalation and collaboration tools are game-changers.",
    role: "Investigator",
    company: "Global Tech Group",
    avatar: "/desktop/products/investigator/icons/user.svg",
    width: 50,
    height: 50
  },
  {
    quote: "The Investigator Portal has transformed our internal investigations. The platform's security and compliance features give us peace of mind while managing sensitive whistleblower cases.",
    role: "Compliance Officer",
    company: "Global Tech Group",
    avatar: "/desktop/products/investigator/icons/user.svg",
    width: 50,
    height: 50
  }
];

// Admin Portal Data
export const adminResponsibilities: ProductResponsibility[] = [
  {
    icon: "/desktop/products/admin/icons/user.svg",
    alt: "User Icon",
    width: 20,
    height: 23,
    title: "User & Role Management",
    description: "Assign and manage user roles with granular permission controls. Define custom access levels for investigators, reviewers, and administrators."
  },
  {
    icon: "/desktop/products/admin/icons/routing.svg",
    alt: "Routing Icon",
    width: 22,
    height: 18,
    title: "Case Routing & Assignment",
    description: "Intelligently route cases based on category, severity, or department. Set up automatic assignments and escalation paths for timely resolution."
  },
  {
    icon: "/desktop/products/admin/icons/trail.svg",
    alt: "Trail Icon",
    width: 18,
    height: 21,
    title: "Audit Trails & Reporting",
    description: "Maintain comprehensive audit logs of all system activities. Generate detailed reports for regulatory compliance and program effectiveness."
  },
  {
    icon: "/desktop/products/admin/icons/config.svg",
    alt: "Config Icon",
    width: 20,
    height: 21,
    title: "Compliance Configuration",
    description: "Customize platform settings to match your organization's policies. Configure anonymity options, data retention rules, and legal disclaimers."
  }
];

export const adminUsers: ProductUser[] = [
  {
    icon: ShieldCheck,
    title: "Custom Role Creation",
    description: "Create custom roles with specific permissions tailored to your organization's structure and needs."
  },
  {
    icon: FileUp,
    title: "Team Hierarchy",
    description: "Organize users into teams with designated team leaders and customized access permissions."
  },
  {
    icon: Timer,
    title: "Access Control",
    description: "Set granular permissions for viewing, editing, and managing cases based on sensitivity and department."
  }
];

export const adminCases: ProductCase[] = [
  {
    icon: ShieldCheck,
    title: "Manual Assignment Workflow",
    description: "Assign cases directly to investigators using admin discretion, ensuring the right fit based on skills or team availability."
  },
  {
    icon: FileUp,
    title: "Team-Based Matching",
    description: "Admins can filter and assign cases by department, role, or specialization to streamline operations."
  },
  {
    icon: Timer,
    title: "Balanced Workload Oversight",
    description: "Easily view each investigator's workload to make informed decisions and prevent overloading any team member."
  }
];

export const adminInsights: ProductInsight[] = [
  {
    icon: ShieldCheck,
    title: "Real-time Metrics",
    description: "Monitor case volume, resolution times, and team performance with live dashboards and customizable KPIs."
  },
  {
    icon: FileUp,
    title: "Team Workload Management",
    description: "Balance investigator workloads, track case assignments, and ensure timely follow-ups on all reports."
  },
  {
    icon: Timer,
    title: "Alert Configuration",
    description: "Set up custom alerts for high-priority cases, approaching deadlines, or unusual reporting patterns."
  }
];

export const adminSecurities: ProductSecurity[] = [
  {
    icon: "/desktop/products/admin/icons/encryption.svg",
    alt: "Encryption Icon",
    width: 18,
    height: 20,
    title: "End-to-End Encryption",
    description: "All data is encrypted in transit and at rest using industry-standard encryption protocols to ensure maximum security of sensitive information."
  },
  {
    icon: "/desktop/products/admin/icons/mfa.svg",
    alt: "MFA Icon",
    width: 18,
    height: 22,
    title: "Access Control with MFA",
    description: "Secure access with multi-factor authentication and role-based permissions to ensure only authorized personnel can access sensitive information."
  },
  {
    icon: "/desktop/products/admin/icons/clock.svg",
    alt: "Clock Icon",
    width: 18,
    height: 20,
    title: "Custom Data Retention Policies",
    description: "Configure data retention periods to comply with regulatory requirements and organizational policies for handling sensitive information."
  },
  {
    icon: "/desktop/products/admin/icons/audit.svg",
    alt: "Audit Icon",
    width: 20,
    height: 20,
    title: "Full Audit Logs",
    description: "Comprehensive time-stamped audit logs of all system activities for complete transparency and accountability in case management."
  }
];

export const adminTestimonials: ProductTestimonial[] = [
  {
    quote: `"The Admin Portal has transformed how we manage our whistleblowing program. The centralized controls and comprehensive oversight capabilities have significantly improved our response times and case management efficiency. It's become an essential tool for our compliance team."`,
    name: "Elizabeth Richardson",
    role: "Chief Compliance Officer, Global Financial Services Inc.",
    avatar: "/desktop/products/admin/testimonial1.svg",
    width: 97,
    height: 96
  },
  {
    quote: `"The Admin Portal has transformed how we manage our whistleblowing program. The centralized controls and comprehensive oversight capabilities have significantly improved our response times and case management efficiency. It's become an essential tool for our compliance team."`,
    name: "Elizabeth Richardson",
    role: "Chief Compliance Officer, Global Financial Services Inc.",
    avatar: "/desktop/products/admin/testimonial1.svg",
    width: 97,
    height: 96
  },
  {
    quote: `"The Admin Portal has transformed how we manage our whistleblowing program. The centralized controls and comprehensive oversight capabilities have significantly improved our response times and case management efficiency. It's become an essential tool for our compliance team."`,
    name: "Elizabeth Richardson",
    role: "Chief Compliance Officer, Global Financial Services Inc.",
    avatar: "/desktop/products/admin/testimonial1.svg",
    width: 97,
    height: 96
  }
];
