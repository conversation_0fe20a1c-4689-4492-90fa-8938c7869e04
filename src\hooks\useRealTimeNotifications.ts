"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { Notification } from '@/lib/types';
import { apiClient } from '@/lib/api/client';

interface UseRealTimeNotificationsReturn {
    notifications: Notification[];
    unreadCount: number;
    isLoading: boolean;
    error: string | null;
    markAsRead: (notificationId: string) => Promise<void>;
    markAllAsRead: () => Promise<void>;
    refresh: () => void;
    createWelcomeNotification: () => Promise<void>;
}

export function useRealTimeNotifications(): UseRealTimeNotificationsReturn {
    const { user } = useAuth();
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchNotifications = useCallback(async () => {
        if (!user?.id) {
            setNotifications([]);
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);
            setError(null);

            const data = await apiClient.get(`/api/notifications?userId=${user.id}&limit=20`) as { 
                success: boolean; 
                data: Notification[]; 
                error?: string;
                unreadCount?: number;
            };

            if (data.success) {
                setNotifications(data.data || []);
            } else {
                setError(data.error || 'Failed to fetch notifications');
                setNotifications([]);
            }
        } catch (error) {
            console.error('Error fetching notifications:', error);
            setError(error instanceof Error ? error.message : 'Failed to fetch notifications');
            setNotifications([]);
        } finally {
            setIsLoading(false);
        }
    }, [user?.id]);

    // Initial fetch and periodic refresh
    useEffect(() => {
        fetchNotifications();
        
        // Refresh notifications every 30 seconds for real-time updates
        const interval = setInterval(fetchNotifications, 30000);
        
        return () => clearInterval(interval);
    }, [fetchNotifications]);

    // Listen for custom events that might trigger new notifications
    useEffect(() => {
        const handleReportSubmitted = (event) => {
            console.log('🔔 Report submitted event received:', event.detail);
            // Refresh notifications when a report is submitted
            setTimeout(fetchNotifications, 1000); // Small delay to ensure notification is created
        };

        const handleMessageSent = (event) => {
            console.log('🔔 Message sent event received:', event.detail);
            // Refresh notifications when a message is sent
            setTimeout(fetchNotifications, 1000);
        };

        const handleStatusUpdate = (event) => {
            console.log('🔔 Status update event received:', event.detail);
            // Refresh notifications when report status is updated
            setTimeout(fetchNotifications, 1000);
        };

        const handleRefreshNotifications = () => {
            console.log('🔔 Manual notification refresh requested');
            fetchNotifications();
        };

        // Listen for custom events
        window.addEventListener('report-submitted', handleReportSubmitted);
        window.addEventListener('message-sent', handleMessageSent);
        window.addEventListener('status-updated', handleStatusUpdate);
        window.addEventListener('refresh-notifications', handleRefreshNotifications);

        return () => {
            window.removeEventListener('report-submitted', handleReportSubmitted);
            window.removeEventListener('message-sent', handleMessageSent);
            window.removeEventListener('status-updated', handleStatusUpdate);
            window.removeEventListener('refresh-notifications', handleRefreshNotifications);
        };
    }, [fetchNotifications]);

    const markAsRead = async (notificationId: string) => {
        try {
            await apiClient.put(`/api/notifications/${notificationId}/mark-read`) as { success: boolean };

            setNotifications(prev => 
                prev.map(notification => 
                    notification._id === notificationId 
                        ? { ...notification, status: 'read' as const }
                        : notification
                )
            );
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    };

    const markAllAsRead = async () => {
        try {
            await apiClient.put(`/api/notifications/mark-all-read`, { userId: user?.id }) as { success: boolean };

            setNotifications(prev => 
                prev.map(notification => ({ ...notification, status: 'read' as const }))
            );
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
        }
    };

    const createWelcomeNotification = async () => {
        try {
            await apiClient.post('/api/notifications/welcome') as { success: boolean };
            // Refresh notifications to show the new welcome notification
            setTimeout(fetchNotifications, 500);
        } catch (error) {
            console.error('Error creating welcome notification:', error);
        }
    };

    const refresh = () => {
        fetchNotifications();
    };

    const unreadCount = notifications.filter(n => n.status === 'unread').length;

    return {
        notifications,
        unreadCount,
        isLoading,
        error,
        markAsRead,
        markAllAsRead,
        refresh,
        createWelcomeNotification
    };
}