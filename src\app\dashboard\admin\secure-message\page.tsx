"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Home, Timer, LogOut, X, LockKeyhole, Archive, Trash2, MoreVertical, Search, Send, Paperclip, Eye, CheckCheck, Users, Shield, ChevronLeft, ArrowDownToLine } from "lucide-react";
import Header from "@/components/dashboard-components/Header";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import LexicalEditor from "@/components/ui/lexical-editor";
import { EditorState } from 'lexical';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { markConversationAsRead } from "@/lib/utils/messageIndicators";
import { getMessagesByConversationId } from "@/lib/mockData/conversationData";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import logger from "@/lib/utils/logger";

interface ApiConversation {
    _id: string;
    reportId?: {
        _id: string;
        reportId: string;
        title: string;
    };
    participants?: Array<{
        _id: string;
        firstName?: string;
        lastName?: string;
        role: string;
    }>;
    status: string;
    lastMessageAt?: string;
    isEncrypted: boolean;
    priority?: string;
    category?: string;
    readBy?: Array<{
        userId: string;
        readAt: Date;
    }>;
}

interface ConversationData {
    id: string;
    _id?: string;
    name: string;
    caseId: string;
    reportId?: {
        _id: string;
        reportId: string;
        title: string;
    };
    participants?: Array<{
        _id: string;
        firstName?: string;
        lastName?: string;
        role: string;
    }>;
    lastMessage: string;
    time: string;
    isUnread: boolean;
    isOnline: boolean;
    isTyping: boolean;
    avatarBg?: string;
    status?: 'active' | 'closed' | 'archived';
    lastMessageAt?: Date;
    isEncrypted?: boolean;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    category?: string;
}

const ConversationItem = ({
    conversation,
    isActive,
    onClick,
    onArchive,
    onDelete,
    onPriorityChange
}: {
    conversation: ConversationData;
    isActive: boolean;
    onClick: () => void;
    onArchive: () => void;
    onDelete: () => void;
    onPriorityChange?: (priority: 'low' | 'medium' | 'high' | 'urgent') => void;
}) => {
    const baseClasses = "flex flex-col justify-between group cursor-pointer transition-all duration-200 p-3 border-b border-gray-100 hover:bg-gray-50";
    const activeClasses = isActive ? "bg-[#ECF4E9] border-l-4 border-l-[#1E4841]" : "bg-white";
    const unreadClasses = conversation.isUnread ? "font-medium" : "";
    const priorityClasses = conversation.priority === 'urgent' ? "border-l-4 border-l-red-500" : 
                           conversation.priority === 'high' ? "border-l-4 border-l-orange-500" : "";
    const classNames = `${baseClasses} ${activeClasses} ${unreadClasses} ${priorityClasses}`.trim();
    
    const avatarBg = conversation.avatarBg || "bg-[#BBF49C]";
    const priorityColor = conversation.priority === 'urgent' ? 'bg-red-100 text-red-700' :
                         conversation.priority === 'high' ? 'bg-orange-100 text-orange-700' :
                         conversation.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                         'bg-gray-100 text-gray-700';

    return (
        <div
            className={classNames}
            onClick={onClick}
            role="button"
            tabIndex={0}
            aria-label={`Conversation with ${conversation.name} about ${conversation.caseId}`}
            onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onClick();
                }
            }}
        >
            <div className="flex items-center gap-3 mb-2">
                <div className="relative">
                    <div className={`h-10 w-10 rounded-full flex items-center justify-center ${avatarBg}`}>
                        <span className="text-sm font-medium text-[#1E4841]">
                            {conversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                        </span>
                    </div>
                    {conversation.isOnline && (
                        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                    )}
                </div>
                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <p className="text-sm font-medium text-[#111827] truncate">
                                {conversation.name}
                            </p>
                            {conversation.isOnline && (
                                <span className="text-xs text-green-600 font-medium">Online</span>
                            )}
                        </div>
                        <div className="flex items-center gap-2">
                            <p className="text-xs text-[#6B7280]">{conversation.time}</p>
                            {conversation.isUnread && (
                                <div className="w-2.5 h-2.5 bg-[#EF4444] rounded-full"></div>
                            )}
                        </div>
                    </div>
                    <p className="text-xs text-[#6B7280] mb-1">{conversation.caseId}</p>
                    <div className="flex items-center gap-2">
                        {conversation.priority && (
                            <Badge variant="outline" className={`text-xs ${priorityColor}`}>
                                {conversation.priority}
                            </Badge>
                        )}
                        {conversation.category && (
                            <Badge variant="outline" className="text-xs">
                                {conversation.category}
                            </Badge>
                        )}
                    </div>
                </div>
            </div>
            <div className="flex items-center justify-between">
                <p className="text-sm text-[#4B5563] truncate flex-1 mr-2">
                    {conversation.isTyping ? (
                        <span className="text-green-600 italic">Typing...</span>
                    ) : (
                        conversation.lastMessage
                    )}
                </p>
                <div className="flex items-center gap-1">
                    <LockKeyhole className="w-3 h-3 text-[#1E4841]" />
                    <Shield className="w-3 h-3 text-blue-600" />
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100">
                                <MoreVertical className="w-3 h-3" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem onClick={() => onPriorityChange?.('urgent')}>
                                <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                Mark Urgent
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onPriorityChange?.('high')}>
                                <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                                Mark High Priority
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={onArchive}>
                                <Archive className="w-4 h-4 mr-2" />
                                Archive
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={onDelete} className="text-red-600">
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
        </div>
    );
};

export default function SecureMessage() {
    const router = useRouter();
    const { user, isLoading } = useAuth();
    const [conversations, setConversations] = useState<ConversationData[]>([]);
    const [isLoadingConversations, setIsLoadingConversations] = useState(true);
    const [activeConversationId, setActiveConversationId] = useState<string>("");
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [newMessage, setNewMessage] = useState<string>("");
    const [isLoadingMessage, setIsLoadingMessage] = useState<boolean>(false);
    const [autoLogoutTime, setAutoLogoutTime] = useState<number>(30 * 60); // 30 minutes for admin
    const [showAutoLogout, setShowAutoLogout] = useState<boolean>(false);
    const [showTypingIndicator, setShowTypingIndicator] = useState<boolean>(false);
    const [refreshTrigger, setRefreshTrigger] = useState(0);
    const [filterPriority, setFilterPriority] = useState<string>('all');
    const [filterStatus, setFilterStatus] = useState<string>('all');
    const [sentMessages, setSentMessages] = useState<Record<string, Array<{id: string, content: string, timestamp: string, html?: string, attachments?: File[]}>>>({});
    const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
    const [clearTrigger, setClearTrigger] = useState(0);
    const [showArchiveDialog, setShowArchiveDialog] = useState(false);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [selectedConversationId, setSelectedConversationId] = useState<string>("");
    const messagesContainerRef = useRef<HTMLDivElement>(null);
    
    const resetAutoLogoutTimer = useCallback(() => {
        setAutoLogoutTime(30 * 60); // Extended for admin users
        setShowAutoLogout(false);
    }, []);

    // Auto-logout functionality (extended for admin)
    useEffect(() => {
        let inactivityTimeout: NodeJS.Timeout;

        const handleUserActivity = () => {
            resetAutoLogoutTimer();

            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }

            inactivityTimeout = setTimeout(() => {
                setShowAutoLogout(true);
            }, 5 * 60 * 1000); // 5 minutes warning for admin
        };

        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

        events.forEach(event => {
            document.addEventListener(event, handleUserActivity, true);
        });

        handleUserActivity();

        return () => {
            events.forEach(event => {
                document.removeEventListener(event, handleUserActivity, true);
            });
            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }
        };
    }, [resetAutoLogoutTimer]);

    useEffect(() => {
        if (!showAutoLogout) return;

        const timer = setInterval(() => {
            setAutoLogoutTime(prev => {
                if (prev <= 1) {
                    router.push('/logout');
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [router, showAutoLogout]);

    const formatAutoLogoutTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    // Load conversations on component mount
    useEffect(() => {
        const loadConversations = async () => {
            if (!user?.id) return;
            
            try {
                setIsLoadingConversations(true);
                const response = await fetch(`/api/conversations?userId=${user.id}`);
                const data = await response.json();
                
                if (data.success) {
                    // Transform API data to match ConversationData interface
                    const transformedConversations = data.data.map((conv: ApiConversation) => ({
                        id: conv._id,
                        _id: conv._id,
                        name: conv.participants
                            ?.filter((p) => p._id !== user.id)
                            .map((p) => `${p.firstName} ${p.lastName}`)
                            .join(', ') || 'Unknown',
                        caseId: conv.reportId?.reportId || 'No Case ID',
                        reportId: conv.reportId,
                        participants: conv.participants,
                        lastMessage: 'Start a secure conversation',
                        time: conv.lastMessageAt ? new Date(conv.lastMessageAt).toLocaleTimeString('en-US', {
                            hour: 'numeric',
                            minute: '2-digit',
                            hour12: true
                        }) : 'Now',
                        isUnread: conv.readBy ?
                            !conv.readBy.some((read: { userId: string; readAt: Date }) =>
                                read.userId.toString() === user.id &&
                                (!conv.lastMessageAt || new Date(conv.lastMessageAt) <= new Date(read.readAt))
                            ) : true,
                        isOnline: false, // TODO: Implement real-time online status
                        isTyping: false,
                        status: conv.status,
                        lastMessageAt: conv.lastMessageAt,
                        isEncrypted: conv.isEncrypted,
                        priority: conv.priority || 'medium',
                        category: conv.category || 'General'
                    }));
                    setConversations(transformedConversations);
                }
            } catch (error) {
                console.error('Error loading conversations:', error);
            } finally {
                setIsLoadingConversations(false);
            }
        };

        loadConversations();
    }, [user?.id, refreshTrigger]);

    // Refresh conversation data from persistent source
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (!document.hidden) {
                setRefreshTrigger(prev => prev + 1);
            }
        };

        const handleFocus = () => {
            setRefreshTrigger(prev => prev + 1);
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        window.addEventListener('focus', handleFocus);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('focus', handleFocus);
        };
    }, []);

    const handleConversationSelect = async (conversationId: string) => {
        setActiveConversationId(conversationId);

        // Mark conversation as read and update the sidebar counter
        try {
            await markConversationAsRead(conversationId);
            // Refresh the conversation data to reflect the persistent changes
            setRefreshTrigger(prev => prev + 1);
        } catch (error) {
            logger.error('Error marking conversation as read:', error);
        }
    };

    const handleArchiveConversation = (conversationId: string) => {
        setSelectedConversationId(conversationId);
        setShowArchiveDialog(true);
    };

    const handleDeleteConversation = (conversationId: string) => {
        setSelectedConversationId(conversationId);
        setShowDeleteDialog(true);
    };

    const confirmArchive = () => {
        const conversation = conversations.find(conv => conv.id === selectedConversationId);
        if (conversation) {
            setConversations(prev => prev.filter(conv => conv.id !== selectedConversationId));
            if (activeConversationId === selectedConversationId) {
                setActiveConversationId("");
            }
            logger.info(`Admin archived conversation ${selectedConversationId}`);
        }
        setSelectedConversationId("");
    };

    const confirmDelete = () => {
        const conversation = conversations.find(conv => conv.id === selectedConversationId);
        if (conversation) {
            setConversations(prev => prev.filter(conv => conv.id !== selectedConversationId));
            if (activeConversationId === selectedConversationId) {
                setActiveConversationId("");
            }
            logger.info(`Admin deleted conversation ${selectedConversationId}`);
        }
        setSelectedConversationId("");
    };

    const handlePriorityChange = (conversationId: string, priority: 'low' | 'medium' | 'high' | 'urgent') => {
        setConversations(prev => prev.map(conv => 
            conv.id === conversationId ? { ...conv, priority } : conv
        ));
        logger.info(`Admin changed priority of conversation ${conversationId} to ${priority}`);
    };

    const scrollToBottom = useCallback(() => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
    }, []);

    const handleSendMessage = async () => {
        if (!newMessage.trim()) return;

        const messageContent = newMessage.trim();
        const timestamp = new Date().toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

        setIsLoadingMessage(true);
        setShowTypingIndicator(false);

        const newSentMessage = {
            id: `sent_${Date.now()}`,
            content: messageContent,
            timestamp: timestamp,
            attachments: attachedFiles.length > 0 ? [...attachedFiles] : undefined
        };
        setSentMessages(prev => ({
            ...prev,
            [activeConversationId]: [...(prev[activeConversationId] || []), newSentMessage]
        }));

        setNewMessage("");
        setAttachedFiles([]);
        handleClearEditor();

        await new Promise(resolve => setTimeout(resolve, 1000));

        setShowTypingIndicator(true);
        setTimeout(() => {
            setShowTypingIndicator(false);
        }, 3000);

        setIsLoadingMessage(false);
        resetAutoLogoutTimer();

        setTimeout(() => {
            scrollToBottom();
        }, 100);
    };

    const handleEditorChange = (newEditorState: EditorState) => {
        newEditorState.read(() => {
            const root = newEditorState._nodeMap.get('root');
            if (root) {
                setNewMessage(root.getTextContent());
            }
        });

        resetAutoLogoutTimer();
    };

    const handleEditorKeyDown = (event: KeyboardEvent): boolean => {
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            event.stopPropagation();
            handleSendMessage();
            return true;
        }
        return false;
    };

    const handleClearEditor = () => {
        setClearTrigger(prev => prev + 1);
    };

    const removeAttachedFile = (index: number) => {
        setAttachedFiles(prev => prev.filter((_, i) => i !== index));
    };

    // Auto-scroll to bottom when messages change
    useEffect(() => {
        scrollToBottom();
    }, [sentMessages, showTypingIndicator, activeConversationId, scrollToBottom]);

    const filteredConversations = conversations.filter(conv => {
        const matchesSearch = conv.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                             conv.caseId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                             conv.lastMessage.toLowerCase().includes(searchQuery.toLowerCase());
        
        const matchesPriority = filterPriority === 'all' || conv.priority === filterPriority;
        const matchesStatus = filterStatus === 'all' || conv.status === filterStatus;
        
        return matchesSearch && matchesPriority && matchesStatus;
    });

    const urgentCount = conversations.filter(conv => conv.priority === 'urgent').length;
    const highPriorityCount = conversations.filter(conv => conv.priority === 'high').length;

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
            </div>
        );
    }

    if (!user) {
        router.push('/login');
        return null;
    }

    return (
        <div className="flex flex-col h-screen bg-gray-50">
            <Header />
            
            {/* Auto-logout banner */}
            {showAutoLogout && (
                <div className="bg-red-600 text-white px-4 py-2 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Timer className="w-4 h-4" />
                        <span className="text-sm">
                            Admin session expires in {formatAutoLogoutTime(autoLogoutTime)} due to inactivity
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-red-700"
                            onClick={resetAutoLogoutTimer}
                        >
                            Stay Logged In
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-red-700"
                            onClick={() => router.push('/logout')}
                        >
                            <LogOut className="w-4 h-4 mr-1" />
                            Logout Now
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-red-700 p-1"
                            onClick={() => setShowAutoLogout(false)}
                        >
                            <X className="w-4 h-4" />
                        </Button>
                    </div>
                </div>
            )}
            
            <div className="flex-1 flex flex-col p-6">
                {/* Breadcrumb */}
                <div className="mb-6">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard/admin" className="flex items-center gap-2">
                                    <Home className="w-4 h-4" />
                                    Dashboard
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Secure Messages</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>

                {/* Page Header with Admin Stats */}
                <div className="mb-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Secure Messages</h1>
                            <p className="text-gray-600 mt-1">
                                Manage secure communications with whistleblowers and investigators.
                            </p>
                        </div>
                        <div className="flex items-center gap-4">
                            {urgentCount > 0 && (
                                <Badge variant="destructive" className="flex items-center gap-1">
                                    <span className="w-2 h-2 bg-white rounded-full animate-pulse"></span>
                                    {urgentCount} Urgent
                                </Badge>
                            )}
                            {highPriorityCount > 0 && (
                                <Badge variant="outline" className="text-orange-600 border-orange-300">
                                    {highPriorityCount} High Priority
                                </Badge>
                            )}
                            <Badge variant="outline" className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                {conversations.length} Total
                            </Badge>
                        </div>
                    </div>
                </div>

                {/* Enhanced Messaging Interface */}
                <div className="flex-1 min-h-0">
                    {isLoadingConversations ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4" />
                                <p className="text-gray-600">Loading conversations...</p>
                            </div>
                        </div>
                    ) : (
                        <div className="flex h-full bg-white rounded-lg shadow-lg overflow-hidden">
                            {/* Enhanced Admin Conversations Sidebar */}
                            <div className="w-1/3 border-r border-gray-200 flex flex-col">
                                {/* Search and Filters */}
                                <div className="p-4 border-b border-gray-200 space-y-3">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                        <Input
                                            placeholder="Search conversations..."
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                    <div className="flex gap-2">
                                        <select
                                            value={filterPriority}
                                            onChange={(e) => setFilterPriority(e.target.value)}
                                            className="flex-1 px-3 py-1 border border-gray-300 rounded-md text-sm"
                                        >
                                            <option value="all">All Priority</option>
                                            <option value="urgent">Urgent</option>
                                            <option value="high">High</option>
                                            <option value="medium">Medium</option>
                                            <option value="low">Low</option>
                                        </select>
                                        <select
                                            value={filterStatus}
                                            onChange={(e) => setFilterStatus(e.target.value)}
                                            className="flex-1 px-3 py-1 border border-gray-300 rounded-md text-sm"
                                        >
                                            <option value="all">All Status</option>
                                            <option value="active">Active</option>
                                            <option value="closed">Closed</option>
                                            <option value="archived">Archived</option>
                                        </select>
                                    </div>
                                </div>

                                {/* Conversations List */}
                                <div className="flex-1 overflow-y-auto">
                                    {filteredConversations.length === 0 ? (
                                        <div className="flex items-center justify-center h-32 text-gray-500">
                                            <div className="text-center">
                                                <p>No conversations found</p>
                                                <p className="text-sm">Adjust filters or search terms</p>
                                            </div>
                                        </div>
                                    ) : (
                                        filteredConversations.map((conversation) => (
                                            <ConversationItem
                                                key={conversation.id}
                                                conversation={conversation}
                                                isActive={conversation.id === activeConversationId}
                                                onClick={() => handleConversationSelect(conversation.id)}
                                                onArchive={() => handleArchiveConversation(conversation.id)}
                                                onDelete={() => handleDeleteConversation(conversation.id)}
                                                onPriorityChange={(priority) => handlePriorityChange(conversation.id, priority)}
                                            />
                                        ))
                                    )}
                                </div>
                            </div>

                            {/* Admin Messaging Interface */}
                            <div className="flex-1 flex flex-col">
                                {activeConversationId ? (
                                    <>
                                        {/* Chat Header */}
                                        <div className="p-4 border-b border-gray-200 bg-white">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-3">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => setActiveConversationId("")}
                                                        className="lg:hidden"
                                                    >
                                                        <ChevronLeft className="w-4 h-4" />
                                                    </Button>
                                                    <div className="flex items-center gap-3">
                                                        <div className={`h-10 w-10 rounded-full flex items-center justify-center ${conversations.find(c => c.id === activeConversationId)?.avatarBg || "bg-[#BBF49C]"}`}>
                                                            <span className="text-sm font-medium text-[#1E4841]">
                                                                {conversations.find(c => c.id === activeConversationId)?.name?.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <h3 className="font-medium text-gray-900">
                                                                {conversations.find(c => c.id === activeConversationId)?.name}
                                                            </h3>
                                                            <p className="text-sm text-gray-500">
                                                                {conversations.find(c => c.id === activeConversationId)?.caseId}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Badge variant="outline" className="text-xs">
                                                        Admin View
                                                    </Badge>
                                                    <LockKeyhole className="w-4 h-4 text-[#1E4841]" />
                                                    <Shield className="w-4 h-4 text-blue-600" />
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" size="sm">
                                                                <MoreVertical className="w-4 h-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent>
                                                            <DropdownMenuItem>
                                                                <Eye className="w-4 h-4 mr-2" />
                                                                View Case Details
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem>
                                                                <ArrowDownToLine className="w-4 h-4 mr-2" />
                                                                Export Conversation
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem onClick={() => handleArchiveConversation(activeConversationId)}>
                                                                <Archive className="w-4 h-4 mr-2" />
                                                                Archive
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Messages Area */}
                                        <div className="flex-1 overflow-y-auto p-4 space-y-4" ref={messagesContainerRef}>
                                            {/* Existing messages */}
                                            {getMessagesByConversationId(activeConversationId).map((message) => (
                                                <div key={message.id} className={`flex ${message.senderId === 'user' ? 'justify-end' : 'justify-start'}`}>
                                                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${message.senderId === 'user' ? 'bg-[#1E4841] text-white' : 'bg-gray-100 text-gray-900'}`}>
                                                        <p className="text-sm">{message.content}</p>
                                                        <p className="text-xs mt-1 opacity-70">{message.timestamp}</p>
                                                    </div>
                                                </div>
                                            ))}

                                            {/* Sent messages */}
                                            {sentMessages[activeConversationId]?.map((message) => (
                                                <div key={message.id} className="flex justify-end">
                                                    <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-[#1E4841] text-white">
                                                        <p className="text-sm">{message.content}</p>
                                                        {message.attachments && message.attachments.length > 0 && (
                                                            <div className="mt-2 space-y-1">
                                                                {message.attachments.map((file, index) => (
                                                                    <div key={index} className="flex items-center gap-2 text-xs">
                                                                        <Paperclip className="w-3 h-3" />
                                                                        <span>{file.name}</span>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        )}
                                                        <div className="flex items-center justify-between mt-1">
                                                            <p className="text-xs opacity-70">{message.timestamp}</p>
                                                            <CheckCheck className="w-3 h-3" />
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}

                                            {/* Typing indicator */}
                                            {showTypingIndicator && (
                                                <div className="flex justify-start">
                                                    <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100">
                                                        <div className="flex items-center gap-1">
                                                            <div className="flex gap-1">
                                                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                                            </div>
                                                            <span className="text-xs text-gray-500 ml-2">Typing...</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>

                                        {/* Message Composer */}
                                        <div className="p-4 border-t border-gray-200 bg-white">
                                            {/* File attachments preview */}
                                            {attachedFiles.length > 0 && (
                                                <div className="mb-3 flex flex-wrap gap-2">
                                                    {attachedFiles.map((file, index) => (
                                                        <div key={index} className="flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-lg text-sm">
                                                            <Paperclip className="w-3 h-3" />
                                                            <span className="truncate max-w-32">{file.name}</span>
                                                            <button
                                                                onClick={() => removeAttachedFile(index)}
                                                                className="text-red-500 hover:text-red-700"
                                                            >
                                                                <X className="w-3 h-3" />
                                                            </button>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}

                                            <div className="flex items-end gap-2">
                                                <div className="flex-1">
                                                    <LexicalEditor
                                                        placeholder=""
                                                        onChange={handleEditorChange}
                                                        onKeyDown={handleEditorKeyDown}
                                                        clearTrigger={clearTrigger}
                                                        className="min-h-[60px] max-h-32 border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-[#1E4841] focus:border-transparent"
                                                    />
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        onClick={handleSendMessage}
                                                        disabled={!newMessage.trim() || isLoadingMessage}
                                                        className="bg-[#1E4841] hover:bg-[#2a5d54] text-white px-4 py-2"
                                                    >
                                                        {isLoadingMessage ? (
                                                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                        ) : (
                                                            <Send className="w-4 h-4" />
                                                        )}
                                                    </Button>
                                                </div>
                                            </div>
                                            <p className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                                <LockKeyhole className="w-3 h-3" />
                                                This conversation is end-to-end encrypted
                                            </p>
                                        </div>
                                    </>
                                ) : (
                                    <div className="flex-1 flex items-center justify-center bg-gray-50">
                                        <div className="text-center">
                                            <div className="w-16 h-16 bg-[#ECF4E9] rounded-full flex items-center justify-center mx-auto mb-4">
                                                <LockKeyhole className="w-8 h-8 text-[#1E4841]" />
                                            </div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                Secure Admin Messaging
                                            </h3>
                                            <p className="text-gray-600 max-w-sm">
                                                Select a conversation to start secure messaging with whistleblowers and investigators.
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Confirmation Dialogs */}
            <ConfirmationDialog
                open={showArchiveDialog}
                onOpenChange={setShowArchiveDialog}
                title="Archive Conversation"
                description={`Are you sure you want to archive this conversation? It will be moved to archived conversations.`}
                confirmText="Archive"
                cancelText="Cancel"
                onConfirm={confirmArchive}
                variant="default"
            />

            <ConfirmationDialog
                open={showDeleteDialog}
                onOpenChange={setShowDeleteDialog}
                title="Delete Conversation"
                description={`Are you sure you want to delete this conversation? This action cannot be undone and will remove all messages.`}
                confirmText="Delete"
                cancelText="Cancel"
                onConfirm={confirmDelete}
                variant="destructive"
            />
        </div>
    );
}