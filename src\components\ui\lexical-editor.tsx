"use client";

import { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { $getRoot, $getSelection, EditorState } from 'lexical';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin';
import {
  UNORDERED_LIST,
  ORDERED_LIST} from '@lexical/markdown';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';
import { LinkNode, AutoLinkNode } from '@lexical/link';
import { MarkNode } from '@lexical/mark';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $isRangeSelection, FORMAT_TEXT_COMMAND, INDENT_CONTENT_COMMAND, OUTDENT_CONTENT_COMMAND } from 'lexical';
import { INSERT_UNORDERED_LIST_COMMAND, INSERT_ORDERED_LIST_COMMAND } from '@lexical/list';
import { $generateHtmlFromNodes } from '@lexical/html';
import { CircleHelp } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface LexicalEditorProps {
  placeholder?: string;
  onChange?: (editorState: EditorState) => void;
  onKeyDown?: (event: KeyboardEvent) => boolean;
  className?: string;
  autoFocus?: boolean;
  onAttachmentClick?: () => void;
  attachedFilesCount?: number;
  showToolbar?: boolean;
  clearTrigger?: number;
}

export interface LexicalEditorRef {
  getHTML: () => string;
  clear: () => void;
  focus: () => void;
}

// Theme configuration matching our design
const theme = {
  text: {
    bold: 'font-bold',
    italic: 'italic',
    underline: 'underline',
  },
  list: {
    nested: {
      listitem: 'list-none',
    },
    ol: 'list-decimal list-outside ml-5 my-2',
    ul: 'list-disc list-outside ml-5 my-2',
    listitem: 'my-1',
  },
  paragraph: 'my-1',
};

// Initial editor configuration
const initialConfig = {
  namespace: 'SecureMessageEditor',
  theme,
  onError: (error: Error) => {
    console.error('Lexical Editor Error:', error);
  },
  nodes: [
    HeadingNode,
    ListNode,
    ListItemNode,
    QuoteNode,
    LinkNode,
    AutoLinkNode,
    MarkNode,
  ],
};

// Plugin to handle keyboard shortcuts
function KeyboardShortcutsPlugin({ onKeyDown }: { onKeyDown?: (event: KeyboardEvent) => boolean }) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const { ctrlKey, metaKey, key } = event;

      // Handle Ctrl+Enter first (let parent handle it) - must prevent default here
      if ((ctrlKey || metaKey) && key === 'Enter') {
        event.preventDefault();
        event.stopPropagation();
        if (onKeyDown && onKeyDown(event)) {
          return;
        }
      }

      // Handle formatting shortcuts
      if (ctrlKey || metaKey) {
        let handled = false;
        switch (key.toLowerCase()) {
          case 'b':
            event.preventDefault();
            event.stopPropagation();
            editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold');
            handled = true;
            break;
          case 'i':
            event.preventDefault();
            event.stopPropagation();
            editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic');
            handled = true;
            break;
          case 'u':
            event.preventDefault();
            event.stopPropagation();
            editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline');
            handled = true;
            break;
        }

        if (handled) {
          return;
        }
      }

      // Handle Tab for list indentation
      if (key === 'Tab') {
        event.preventDefault();
        if (event.shiftKey) {
          editor.dispatchCommand(OUTDENT_CONTENT_COMMAND, undefined);
        } else {
          editor.dispatchCommand(INDENT_CONTENT_COMMAND, undefined);
        }
      }
    };

    // Use keydown event on the editor root element
    return editor.registerRootListener((rootElement, prevRootElement) => {
      if (prevRootElement !== null) {
        prevRootElement.removeEventListener('keydown', handleKeyDown, true);
      }
      if (rootElement !== null) {
        rootElement.addEventListener('keydown', handleKeyDown, true);
      }
    });
  }, [editor, onKeyDown]);

  return null;
}

// Plugin to get editor state changes
function OnChangePlugin({ onChange }: { onChange?: (editorState: EditorState) => void }) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      onChange?.(editorState);
    });
  }, [editor, onChange]);

  return null;
}

// Plugin to get current formatting state
function FormattingStatePlugin({ onFormattingChange }: { onFormattingChange?: (formats: string[]) => void }) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return editor.registerUpdateListener(() => {
      editor.getEditorState().read(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const formats: string[] = [];
          if (selection.hasFormat('bold')) formats.push('bold');
          if (selection.hasFormat('italic')) formats.push('italic');
          if (selection.hasFormat('underline')) formats.push('underline');
          onFormattingChange?.(formats);
        }
      });
    });
  }, [editor, onFormattingChange]);

  return null;
}

// Toolbar component that works within Lexical context
function ToolbarPlugin({ onAttachmentClick, attachedFilesCount }: { onAttachmentClick?: () => void; attachedFilesCount?: number }) {
  const [editor] = useLexicalComposerContext();
  const [activeFormats, setActiveFormats] = useState<string[]>([]);

  // Update formatting state
  useEffect(() => {
    return editor.registerUpdateListener(() => {
      editor.getEditorState().read(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          const formats: string[] = [];
          if (selection.hasFormat('bold')) formats.push('bold');
          if (selection.hasFormat('italic')) formats.push('italic');
          if (selection.hasFormat('underline')) formats.push('underline');
          setActiveFormats(formats);
        }
      });
    });
  }, [editor]);

  const handleFormatting = (format: string) => {
    switch (format) {
      case 'bold':
        editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold');
        break;
      case 'italic':
        editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic');
        break;
      case 'underline':
        editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline');
        break;
      case 'list':
        editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
        break;
      case 'ordered-list':
        editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
        break;
      case 'attachment':
        onAttachmentClick?.();
        break;
    }
  };

  return (
    <div className="flex justify-between items-center mb-2">
      <div className="flex items-center gap-1">
        <button
          type="button"
          onClick={() => handleFormatting('bold')}
          onMouseDown={(e) => e.preventDefault()}
          className={`p-2 rounded transition-colors ${
            activeFormats.includes('bold')
              ? 'bg-[#1E4841] text-white'
              : 'text-[#374151] hover:bg-[#ECF4E9] hover:text-[#1E4841]'
          }`}
          aria-label="Toggle bold (Ctrl+B)"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6V4zm0 8h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6v-8zm2-6v4h6a2 2 0 0 0 2-2 2 2 0 0 0-2-2H8zm0 6v4h6a2 2 0 0 0 2-2 2 2 0 0 0-2-2H8z"/>
          </svg>
        </button>
        <button
          type="button"
          onClick={() => handleFormatting('italic')}
          onMouseDown={(e) => e.preventDefault()}
          className={`p-2 rounded transition-colors ${
            activeFormats.includes('italic')
              ? 'bg-[#1E4841] text-white'
              : 'text-[#374151] hover:bg-[#ECF4E9] hover:text-[#1E4841]'
          }`}
          aria-label="Toggle italic (Ctrl+I)"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4h-8z"/>
          </svg>
        </button>
        <button
          type="button"
          onClick={() => handleFormatting('underline')}
          onMouseDown={(e) => e.preventDefault()}
          className={`p-2 rounded transition-colors ${
            activeFormats.includes('underline')
              ? 'bg-[#1E4841] text-white'
              : 'text-[#374151] hover:bg-[#ECF4E9] hover:text-[#1E4841]'
          }`}
          aria-label="Toggle underline (Ctrl+U)"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M5 21h14v-2H5v2zM12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6z"/>
          </svg>
        </button>
        <div className="h-6 mx-2 border border-[#D1D5DB]" />
        <button
          type="button"
          onClick={() => handleFormatting('list')}
          onMouseDown={(e) => e.preventDefault()}
          className="p-2 rounded text-[#374151] hover:bg-[#ECF4E9] hover:text-[#1E4841] transition-colors"
          aria-label="Toggle bullet list"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M4 10.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zm0-6c-.83 0-1.5.67-1.5 1.5S3.17 7.5 4 7.5 5.5 6.83 5.5 6 4.83 4.5 4 4.5zm0 12c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5zM7 19h14v-2H7v2zm0-6h14v-2H7v2zm0-8v2h14V5H7z"/>
          </svg>
        </button>
        <button
          type="button"
          onClick={() => handleFormatting('ordered-list')}
          onMouseDown={(e) => e.preventDefault()}
          className="p-2 rounded text-[#374151] hover:bg-[#ECF4E9] hover:text-[#1E4841] transition-colors"
          aria-label="Toggle numbered list"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M2 17h2v.5H3v1h1v.5H2v1h3v-4H2v1zm1-9h1V4H2v1h1v3zm-1 3h1.8L2 13.1v.9h3v-1H3.2L5 10.9V10H2v1zm5-6v2h14V5H7zm0 14h14v-2H7v2zm0-6h14v-2H7v2z"/>
          </svg>
        </button>
        <div className="h-6 mx-2 border border-[#D1D5DB]" />
        <button
          type="button"
          onClick={() => handleFormatting('attachment')}
          onMouseDown={(e) => e.preventDefault()}
          className={`p-2 rounded transition-colors ${
            (attachedFilesCount || 0) > 0
              ? 'bg-[#1E4841] text-white'
              : 'text-[#374151] hover:bg-[#ECF4E9] hover:text-[#1E4841]'
          }`}
          aria-label="Attach file"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5a2.5 2.5 0 0 1 5 0v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5a2.5 2.5 0 0 0 5 0V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.31 2.69 6 6 6s6-2.69 6-6V6h-2.5z"/>
          </svg>
        </button>
      </div>

      {/* Keyboard Shortcuts Help - Hidden on mobile/tablet */}
      <div className="hidden md:block">
        <Popover>
          <PopoverTrigger asChild>
            <button
              type="button"
              className="p-2 rounded text-[#6B7280] hover:bg-[#ECF4E9] hover:text-[#1E4841] transition-colors"
              aria-label="Keyboard shortcuts help"
            >
              <CircleHelp className="w-4 h-4" />
            </button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-4" align="end">
            <div className="space-y-3">
              <h4 className="font-medium text-sm text-[#111827] mb-3">Keyboard Shortcuts</h4>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between items-center">
                  <span className="text-[#6B7280]">Bold</span>
                  <kbd className="px-2 py-1 bg-[#F3F4F6] text-[#374151] rounded text-xs font-mono">Ctrl+B</kbd>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#6B7280]">Italic</span>
                  <kbd className="px-2 py-1 bg-[#F3F4F6] text-[#374151] rounded text-xs font-mono">Ctrl+I</kbd>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#6B7280]">Underline</span>
                  <kbd className="px-2 py-1 bg-[#F3F4F6] text-[#374151] rounded text-xs font-mono">Ctrl+U</kbd>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#6B7280]">Send Message</span>
                  <kbd className="px-2 py-1 bg-[#F3F4F6] text-[#374151] rounded text-xs font-mono">Ctrl+Enter</kbd>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#6B7280]">Indent List</span>
                  <kbd className="px-2 py-1 bg-[#F3F4F6] text-[#374151] rounded text-xs font-mono">Tab</kbd>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-[#6B7280]">Outdent List</span>
                  <kbd className="px-2 py-1 bg-[#F3F4F6] text-[#374151] rounded text-xs font-mono">Shift+Tab</kbd>
                </div>
              </div>
              <div className="pt-2 border-t border-[#E5E7EB]">
                <p className="text-xs text-[#6B7280]">
                  💡 Tip: Select text and use multiple shortcuts to apply multiple formats at once.
                </p>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}

// Clear content plugin
function ClearContentPlugin({ clearTrigger }: { clearTrigger?: number }) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (clearTrigger && clearTrigger > 0) {
      editor.update(() => {
        const root = $getRoot();
        root.clear();
      });
    }
  }, [editor, clearTrigger]);

  return null;
}

// Plugin to get editor reference
function EditorRefPlugin({ setEditor }: { setEditor: (editor: any) => void }) { // eslint-disable-line @typescript-eslint/no-explicit-any
  const [editor] = useLexicalComposerContext();
  
  useEffect(() => {
    setEditor(editor);
  }, [editor, setEditor]);

  return null;
}

const LexicalEditor = forwardRef<LexicalEditorRef, LexicalEditorProps>(({
  placeholder = "Type your message...",
  onChange,
  onKeyDown,
  className = "",
  autoFocus = false,
  onAttachmentClick,
  attachedFilesCount = 0,
  showToolbar = true,
  clearTrigger
}, ref) => {
  const [editor, setEditor] = useState<any>(null); // eslint-disable-line @typescript-eslint/no-explicit-any

  useImperativeHandle(ref, () => ({
    getHTML: () => {
      if (!editor) return '';
      
      let htmlString = '';
      editor.getEditorState().read(() => {
        htmlString = $generateHtmlFromNodes(editor, null);
      });
      return htmlString;
    },
    clear: () => {
      if (editor) {
        editor.update(() => {
          const root = $getRoot();
          root.clear();
        });
      }
    },
    focus: () => {
      if (editor) {
        editor.focus();
      }
    }
  }), [editor]);

  return (
    <LexicalComposer initialConfig={initialConfig}>
      <EditorRefPlugin setEditor={setEditor} />
      <div className={`relative ${className}`}>
        {showToolbar && (
          <ToolbarPlugin
            onAttachmentClick={onAttachmentClick}
            attachedFilesCount={attachedFilesCount}
          />
        )}
        <RichTextPlugin
          contentEditable={
            <ContentEditable
              className="min-h-[80px] p-3 border-2 border-[#D1D5DB] rounded-md focus:border-[#1E4841] focus:outline-none focus:ring-2 focus:ring-[#1E4841] focus:ring-opacity-20 overflow-y-auto max-h-[200px] resize-none"
              style={{
                wordBreak: 'break-word',
                whiteSpace: 'pre-wrap'
              }}
            />
          }
          placeholder={
            <div className="absolute top-3 left-3 text-[#9CA3AF] pointer-events-none select-none">
              {placeholder}
            </div>
          }
          ErrorBoundary={LexicalErrorBoundary}
        />
        <HistoryPlugin />
        <ListPlugin />
        <MarkdownShortcutPlugin transformers={[UNORDERED_LIST, ORDERED_LIST]} />
        <KeyboardShortcutsPlugin onKeyDown={onKeyDown} />
        <OnChangePlugin onChange={onChange} />
        <ClearContentPlugin clearTrigger={clearTrigger} />
        {autoFocus && <AutoFocusPlugin />}
      </div>
    </LexicalComposer>
  );
});

LexicalEditor.displayName = 'LexicalEditor';

export default LexicalEditor;

// Export additional components for formatting controls
export { FormattingStatePlugin };
