"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ConversationActionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  action: 'delete' | 'archive';
  conversationName: string;
  onConfirm: () => void;
  isLoading?: boolean;
}

export function ConversationActionDialog({
  open,
  onOpenChange,
  action,
  conversationName,
  onConfirm,
  isLoading = false
}: ConversationActionDialogProps) {
  const isDelete = action === 'delete';
  
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isDelete ? 'Delete' : 'Archive'} Conversation
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to {action} the conversation with{' '}
            <span className="font-medium">{conversationName}</span>?
            {isDelete && ' This action cannot be undone.'}
            {!isDelete && ' You can restore it later from archived conversations.'}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isLoading}
            className={isDelete ? 'bg-red-600 hover:bg-red-700' : 'bg-orange-600 hover:bg-orange-700'}
          >
            {isLoading ? 'Processing...' : (isDelete ? 'Delete' : 'Archive')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}