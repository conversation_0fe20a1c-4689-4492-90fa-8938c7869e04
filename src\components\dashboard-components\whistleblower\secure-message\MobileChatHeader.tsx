"use client";

import { ChevronLeft, LockKeyhole } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ConversationData } from "@/lib/types";

interface MobileChatHeaderProps {
  conversation: ConversationData;
  onBack: () => void;
}

export default function MobileChatHeader({ conversation, onBack }: MobileChatHeaderProps) {
  return (
    <div className="p-4 border-b flex items-center gap-3 bg-gray-50">
      <Button
        variant="ghost"
        size="sm"
        onClick={onBack}
        className="p-1"
        aria-label="Back to conversations"
      >
        <ChevronLeft className="w-5 h-5 text-[#6B7271]" />
      </Button>
      <div className="relative">
        <div className={`h-10 w-10 rounded-full ${conversation.avatarBg} flex items-center justify-center`}>
          <span className="text-sm font-medium text-[#1E4841]">
            {conversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
          </span>
        </div>
        {conversation.isOnline && (
          <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
        )}
      </div>
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <p className="text-base font-medium text-[#111827]">{conversation.name}</p>
          {conversation.isOnline && (
            <span className="text-xs text-green-600 font-medium">Online</span>
          )}
        </div>
        <div className="flex items-center gap-2 text-xs text-[#6B7280]">
          <span>{conversation.caseId}</span>
          <span className="text-xs">•</span>
          <LockKeyhole className="w-3 h-3" />
          <span>Encrypted</span>
        </div>
      </div>
    </div>
  );
}