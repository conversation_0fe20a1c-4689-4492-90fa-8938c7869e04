"use client";
import Link from "next/link";
import { memo } from "react";
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { NavItem, UI_CONSTANTS } from "@/lib";

interface DropdownContentProps {
    items: NavItem[];
    onItemClick?: () => void;
}

const DropdownContent = memo(({ items, onItemClick }: DropdownContentProps) => (
    <div className="grid gap-1 p-1">
        {items.map(({ href, title, description }) => (
            <DropdownMenuItem key={href} asChild>
                <Link href={href} onClick={onItemClick} className="flex flex-col items-start">
                    <div className="font-semibold" style={{ color: UI_CONSTANTS.colors.primary }}>{title}</div>
                    {description && <div className="text-sm opacity-70" style={{ color: UI_CONSTANTS.colors.primary }}>{description}</div>}
                </Link>
            </DropdownMenuItem>
        ))}
    </div>
));

DropdownContent.displayName = 'DropdownContent';

export default DropdownContent;