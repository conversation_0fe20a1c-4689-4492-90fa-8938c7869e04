import mongoose, { Schema } from 'mongoose';

const AuthorSchema = new Schema({
  image: { type: String, required: true },
  name: { type: String, required: true },
  initials: { type: String, required: true }
});

const BlogSchema = new Schema({
  image: { type: String, required: true },
  category: { type: String, required: true },
  date: { type: String, required: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  author: { type: AuthorSchema, required: true },
  readTime: { type: String, required: true },
  slug: { type: String, required: true, unique: true },
  featured: { type: Boolean, default: false },
  content: { type: String },
  tags: [{ type: String }]
}, {
  timestamps: true
});

const Blog = mongoose.models?.Blog || mongoose.model('Blog', BlogSchema);
export default Blog;