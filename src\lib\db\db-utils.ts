import mongoose from 'mongoose';
import { createHash, randomBytes } from 'crypto';

/**
 * Utility functions for database operations
 */
export class DbUtils {
  /**
   * Hash a password using SHA-256
   * @param password - The password to hash
   * @returns The hashed password
   */
  static hashPassword(password: string): string {
    return createHash('sha256').update(password).digest('hex');
  }

  /**
   * Generate a secure random token
   * @returns A random token
   */
  static generateToken(): string {
    return createHash('sha256').update(Date.now().toString() + randomBytes(32).toString('hex')).digest('hex');
  }

  /**
   * Generate a verification code for two-factor authentication
   * @returns A 6-digit verification code
   */
  static generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Check if a string is a valid MongoDB ObjectId
   * @param id - The ID to check
   * @returns True if the ID is a valid ObjectId, false otherwise
   */
  static isValidObjectId(id: string): boolean {
    return mongoose.Types.ObjectId.isValid(id);
  }

  /**
   * Convert a string to a MongoDB ObjectId
   * @param id - The ID to convert
   * @returns A MongoDB ObjectId
   */
  static toObjectId(id: string): mongoose.Types.ObjectId {
    return new mongoose.Types.ObjectId(id);
  }

  /**
   * Generate a unique report ID
   * @param prefix - The prefix for the report ID
   * @param count - The current count of reports
   * @returns A unique report ID
   */
  static generateReportId(prefix: string, count: number): string {
    const year = new Date().getFullYear();
    const paddedCount = (count + 1).toString().padStart(4, '0');
    return `${prefix}-${year}-${paddedCount}`;
  }

  /**
   * Generate a slug from a string
   * @param text - The text to convert to a slug
   * @returns A URL-friendly slug
   */
  static generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w ]+/g, '')
      .replace(/ +/g, '-');
  }

  /**
   * Format a date as a string
   * @param date - The date to format
   * @param format - The format to use (default: 'YYYY-MM-DD')
   * @returns A formatted date string
   */
  static formatDate(date: Date, format = 'YYYY-MM-DD'): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return format
      .replace('YYYY', year.toString())
      .replace('MM', month)
      .replace('DD', day);
  }
}