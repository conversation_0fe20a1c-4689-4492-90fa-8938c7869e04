/**
 * Notification Event System
 * Triggers custom events that the notification system can listen to
 */

export class NotificationEvents {
  /**
   * Trigger event when a report is submitted
   */
  static reportSubmitted(reportId: string, reportTitle: string) {
    const event = new CustomEvent('report-submitted', {
      detail: { reportId, reportTitle }
    });
    window.dispatchEvent(event);
  }

  /**
   * Trigger event when a message is sent
   */
  static messageSent(conversationId: string, messageContent: string, recipientId?: string) {
    const event = new CustomEvent('message-sent', {
      detail: { conversationId, messageContent, recipientId }
    });
    window.dispatchEvent(event);
  }

  /**
   * Trigger event when report status is updated
   */
  static statusUpdated(reportId: string, oldStatus: string, newStatus: string) {
    const event = new CustomEvent('status-updated', {
      detail: { reportId, oldStatus, newStatus }
    });
    window.dispatchEvent(event);
  }

  /**
   * Trigger event when an investigator is assigned
   */
  static investigatorAssigned(reportId: string, investigatorId: string, investigatorName: string) {
    const event = new CustomEvent('investigator-assigned', {
      detail: { reportId, investigatorId, investigatorName }
    });
    window.dispatchEvent(event);
  }

  /**
   * Trigger event when a user logs in (for welcome notifications)
   */
  static userLoggedIn(userId: string, userRole: string) {
    const event = new CustomEvent('user-logged-in', {
      detail: { userId, userRole }
    });
    window.dispatchEvent(event);
  }

  /**
   * Trigger event when a notification needs to be refreshed
   */
  static refreshNotifications() {
    const event = new CustomEvent('refresh-notifications');
    window.dispatchEvent(event);
  }
}