import { NextRequest, NextResponse } from 'next/server';
import { socketManager } from '@/lib/socket/server';

export const runtime = 'nodejs';

// This will be called when the API route is accessed
export async function GET() {
  try {
    // In a real production environment, you might want to initialize the socket server
    // in a different way, but for Next.js, we'll handle it in the custom server
    return NextResponse.json({ 
      success: true, 
      message: 'Socket.IO server endpoint',
      connectedUsers: socketManager.getConnectedUsers().length
    });
  } catch (error) {
    console.error('Socket API error:', error);
    return NextResponse.json(
      { success: false, error: 'Socket server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, data } = await request.json();
    
    switch (action) {
      case 'get_online_users':
        return NextResponse.json({
          success: true,
          users: socketManager.getConnectedUsers()
        });
        
      case 'check_user_online':
        const isOnline = socketManager.isUserOnline(data.userId);
        return NextResponse.json({
          success: true,
          isOnline
        });
        
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Socket API POST error:', error);
    return NextResponse.json(
      { success: false, error: 'Socket server error' },
      { status: 500 }
    );
  }
}