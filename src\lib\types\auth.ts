export interface AuthenticatedUser {
  id: string;
  email: string;
  role: 'admin' | 'investigator' | 'whistleblower';
  companyId?: string;
  firstName?: string;
  lastName?: string;
}

export interface AuthResult {
  success: boolean;
  user?: AuthenticatedUser;
  error?: string;
}

export type UserRole = 'admin' | 'investigator' | 'whistleblower';

export interface JWTPayload {
  userId: string;
  id: string;
  role: string;
  companyId?: string;
  email?: string;
  iat: number;
  exp: number;
}