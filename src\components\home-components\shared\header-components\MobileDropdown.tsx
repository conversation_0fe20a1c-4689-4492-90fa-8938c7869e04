"use client";
import Link from "next/link";
import { memo, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { NavItem, ProductItem } from "@/lib/types";

interface MobileDropdownProps {
    title: string;
    items: NavItem[] | ProductItem[];
    isOpen: boolean;
    onToggle: () => void;
    onItemClick: () => void;
}

const MobileDropdown = memo(({ title, items, isOpen, onToggle, onItemClick }: MobileDropdownProps) => {
    const handleToggle = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        onToggle();
    }, [onToggle]);

    return (
        <div className="border-b border-green-200 py-1">
            <Button
                variant="ghost"
                onClick={handleToggle}
                className="w-full flex justify-between items-center px-4 py-3 text-[#1E4841] hover:bg-green-100 transition-colors duration-300"
            >
                <span className="font-medium ml-1">{title}</span>
                <ChevronDown className={`transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`} size={16} />
            </Button>
            {isOpen && (
                <div className="bg-green-50 px-4 pb-2 animate-in slide-in-from-top-1 duration-200">
                    {items.map((item) => (
                        <Link
                            key={item.href}
                            href={item.href}
                            className="block px-3 py-2 rounded-md hover:bg-green-100 transition-colors duration-300"
                            onClick={onItemClick}
                        >
                            <div className="font-semibold text-[#1E4841]">{item.title}</div>
                            {item.description && <div className="text-sm text-[#1E4841]">{item.description}</div>}
                        </Link>
                    ))}
                </div>
            )}
        </div>
    );
});

MobileDropdown.displayName = 'MobileDropdown';

export default MobileDropdown;