"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  Help<PERSON>ircle,
  FileText,
  Route,
  ShieldCheck,
  BookOpen
} from "lucide-react";

export default function HelpResources() {
  const router = useRouter();
  return (
    <Card className="h-full w-full xl:w-1/3">
      <CardHeader className="py-0 flex items-center px-4">
        <CardTitle className="p-0 py-1 text-sm sm:text-base text-[#242E2C] font-semibold">Help & Resources</CardTitle>
      </CardHeader>
      <Separator className="bg-[#F3F4F6]" />
      <CardContent className="space-y-3 sm:space-y-4 px-4">
        <div className="flex gap-2">
          <HelpCircle className="w-8 h-fit m-0 p-1 sm:p-1.5 bg-[#ECF4E9] text-[#1E4841] rounded-full flex-shrink-0" />
          <div className="flex flex-col gap-2">
            <p className="text-[#111827] font-medium text-base">Need assistance?</p>
            <p className="text-[#4B5563] font-normal text-sm">Our support team is available 24/7 to help with any questions about your reports.</p>
            <Link href={"/dashboard/wwhistleblower/help"} className="text-[#1E4841] font-medium text-sm">Contact Support</Link>
          </div>
        </div>

        <Separator className="bg-[#F3F4F6] mt-6" />
        
        <div className="flex flex-col gap-2">
          <p className="text-[#111827] font-medium text-base">Quick Links</p>
          <Link href="/policies/whistleblower" target="_blank" className="flex gap-2 items-center hover:bg-gray-50 p-1 rounded transition-colors">
            <FileText className="w-3 h-3 text-[#1E4841]" />
            <p className="font-normal text-sm text-[#1E4841] hover:underline">Whistleblower Policy</p>
          </Link>
          <Link href="/guidelines/reporting" target="_blank" className="flex gap-2 items-center hover:bg-gray-50 p-1 rounded transition-colors">
            <Route className="w-3 h-3 text-[#1E4841]" />
            <p className="font-normal text-sm text-[#1E4841] hover:underline">Reporting Guidelines</p>
          </Link>
          <button
            className="flex gap-2 items-center hover:bg-gray-50 p-1 rounded transition-colors"
            onClick={() => router.push('/dashboard/privacy')}
          >
            <ShieldCheck className="w-3 h-3 text-[#1E4841]" />
            <p className="font-normal text-sm text-[#1E4841] hover:underline">Privacy & Security</p>
          </button>
          <button
            className="flex gap-2 items-center hover:bg-gray-50 p-1 rounded transition-colors"
            onClick={() => router.push('/dashboard/faq')}
          >
            <BookOpen className="w-3 h-3 text-[#1E4841]" />
            <p className="font-normal text-sm text-[#1E4841] hover:underline">FAQs</p>
          </button>
        </div>
        
        <div className="mt-3 sm:mt-5 p-3 sm:p-4 bg-[#ECF4E9] rounded-lg border border-[#ECF4E9]">
          <div className="flex gap-3 sm:gap-4">
            <ShieldCheck className="w-8 h-fit m-0 p-1 sm:p-1.5 bg-[#BBF49C] text-[#1E4841] rounded-full flex-shrink-0" />
            <div className="flex flex-col">
              <p className="font-medium text-sm sm:text-base text-[#1E4841] mb-1">Your Privacy Matters</p>
              <p className="text-xs sm:text-sm font-normal text-[#1E4841] leading-relaxed">
                All communications are encrypted and your identity is protected according to our whistleblower protection policy.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}