import Header from "@/components/home-components/shared/Header";
import Footer from "@/components/home-components/shared/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function StartTrialFree() {
  return (
    <div>
      <Header />
      <main id="main-content" className="pt-20 min-h-screen">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-2xl mx-auto text-center mb-12">
            <h1 className="h1-bold mb-6">Start Your Free Trial</h1>
            <p className="body-16-regular">
              Experience our secure whistleblowing platform with a 30-day free trial. No credit card required.
            </p>
          </div>
          
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Get Started Today</CardTitle>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <Input placeholder="Company Name" required />
                <Input type="email" placeholder="Work Email" required />
                <Input placeholder="Full Name" required />
                <Input type="tel" placeholder="Phone Number" />
                <Button type="submit" className="w-full bg-[#1E4841] text-white hover:bg-green-900">
                  Start Free Trial
                </Button>
              </form>
              <p className="text-xs text-center mt-4 text-muted-foreground">
                By signing up, you agree to our Terms of Service and Privacy Policy.
              </p>
            </CardContent>
          </Card>
        </div>
      </main>
      <Footer />
    </div>
  );
}