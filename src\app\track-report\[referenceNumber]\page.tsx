"use client";

import React, { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ArrowLeft, Calendar, MapPin, User, AlertCircle } from 'lucide-react';

interface ReportTrackingData {
  referenceNumber: string;
  title: string;
  status: string;
  priority: string;
  dateSubmitted: string;
  lastUpdated: string;
  category: string;
  isAnonymous: boolean;
  estimatedCompletion?: string;
  progress: number;
  company?: {
    name: string;
  };
  assignedInvestigator?: string;
  statusHistory?: Array<{
    status: string;
    date: string;
    note?: string;
  }>;
}

export default function TrackReportPage() {
  const params = useParams();
  const referenceNumber = params.referenceNumber as string;
  const [reportData, setReportData] = useState<ReportTrackingData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReportData = async () => {
      if (!referenceNumber) return;

      try {
        setIsLoading(true);
        const response = await fetch(`/api/reports/track/${encodeURIComponent(referenceNumber)}`);
        const result = await response.json();

        if (result.success) {
          setReportData(result.data);
        } else {
          setError(result.error || 'Report not found');
        }
      } catch (err) {
        console.error('Error fetching report data:', err);
        setError('Failed to load report data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchReportData();
  }, [referenceNumber]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'under review': return 'bg-yellow-100 text-yellow-800';
      case 'awaiting response': return 'bg-orange-100 text-orange-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1E4841] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading report information...</p>
        </div>
      </div>
    );
  }

  if (error || !reportData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Report Not Found</h2>
            <p className="text-gray-600 mb-4">
              {error || 'The report with this reference number could not be found.'}
            </p>
            <Link href="/login/whistleblower">
              <Button className="bg-[#1E4841] hover:bg-[#1E4841]/90">
                Back to Login
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <Link href="/login/whistleblower" className="inline-flex items-center text-[#1E4841] hover:underline mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Login
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">Report Tracking</h1>
          <p className="text-gray-600 mt-2">Reference Number: {reportData.referenceNumber}</p>
        </div>

        {/* Report Overview */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{reportData.title}</span>
              <div className="flex gap-2">
                <Badge className={getStatusColor(reportData.status)}>
                  {reportData.status}
                </Badge>
                <Badge className={getPriorityColor(reportData.priority)}>
                  {reportData.priority} Priority
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm">
                  Submitted: {new Date(reportData.dateSubmitted).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm">
                  Last Updated: {new Date(reportData.lastUpdated).toLocaleDateString()}
                </span>
              </div>
              {reportData.company && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">Company: {reportData.company.name}</span>
                </div>
              )}
              {reportData.assignedInvestigator && !reportData.isAnonymous && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">Investigator: {reportData.assignedInvestigator}</span>
                </div>
              )}
            </div>

            {/* Progress */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Investigation Progress</span>
                <span className="text-sm text-gray-600">{reportData.progress}%</span>
              </div>
              <Progress value={reportData.progress} className="h-2" />
            </div>

            {reportData.estimatedCompletion && (
              <p className="text-sm text-gray-600">
                Estimated Completion: {new Date(reportData.estimatedCompletion).toLocaleDateString()}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Anonymous Notice */}
        {reportData.isAnonymous && (
          <Card className="mb-6 border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-yellow-800">Anonymous Report</h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    This report was submitted anonymously. Limited information is available for tracking purposes.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle>Need Help?</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              If you have questions about your report or need to provide additional information, 
              please contact our compliance team.
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button variant="outline">
                Contact Support
              </Button>
              <Link href="/login/whistleblower">
                <Button className="bg-[#1E4841] hover:bg-[#1E4841]/90">
                  Submit Another Report
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
