import { NextResponse, NextRequest } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { NotificationService } from '@/lib/services/notificationService';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    await connectDB();
    
    const { status, assignedInvestigator } = await request.json();
    const resolvedParams = await params;
    const reportId = resolvedParams.id;
    
    // Get the current report to check old status
    const currentReport = await DataService.getReportById(reportId);
    if (!currentReport) {
      return NextResponse.json(
        { success: false, error: 'Report not found' },
        { status: 404 }
      );
    }

    const oldStatus = currentReport.status || 'New';
    
    // Update the report status
    const updatedReport = await DataService.updateReport(reportId, { 
      status,
      ...(assignedInvestigator && { assignedInvestigator })
    });

    // Create notification for status update
    try {
      await NotificationService.createReportStatusUpdateNotification(
        currentReport.userId.toString(),
        reportId,
        currentReport.title || 'Untitled Report',
        oldStatus,
        status
      );

      // If an investigator was assigned, create assignment notifications
      if (assignedInvestigator && assignedInvestigator !== currentReport.assignedInvestigator) {
        const investigator = await DataService.getUserById(assignedInvestigator);
        if (investigator) {
          await NotificationService.createInvestigatorAssignedNotification(
            currentReport.userId.toString(),
            assignedInvestigator,
            reportId,
            currentReport.title || 'Untitled Report',
            `${investigator.firstName} ${investigator.lastName}`
          );
        }
      }
    } catch (notificationError) {
      console.error('Failed to create status update notification:', notificationError);
      // Don't fail the status update if notification fails
    }
    
    return NextResponse.json({
      success: true,
      data: updatedReport,
      message: 'Report status updated successfully'
    });
  } catch (error) {
    console.error('Report status update API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}