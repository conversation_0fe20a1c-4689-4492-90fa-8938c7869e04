// Dashboard-specific mock data - Database-ready format
import {
  Report,
  DashboardStats,
  User,
  Notification,
  Message,
  Conversation,
  TableConfig
} from "@/lib/types";

// Mock user data for database consistency
export const mockUsers: User[] = [
  {
    _id: "user_001",
    email: "<EMAIL>",
    firstName: "<PERSON>",
    lastName: "Doe",
    role: "whistleblower",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2025-01-15"),
    lastLogin: new Date("2025-01-15"),
    preferences: {
      language: "en",
      notifications: {
        email: true,
        push: true,
        sms: false
      },
      theme: "light"
    }
  },
  {
    _id: "investigator_001",
    email: "<EMAIL>",
    firstName: "Alexandra",
    lastName: "Smith",
    role: "investigator",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2025-01-14")
  },
  {
    _id: "investigator_002",
    email: "micha<PERSON>@company.com",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    role: "investigator",
    isActive: true,
    createdAt: new Date("2024-01-01"),
    updatedAt: new Date("2025-01-12")
  }
];

// Database-ready reports data
export const mockReports: Report[] = [
  {
    _id: "report_001",
    reportId: "WB-2025-0012",
    userId: "user_001",
    title: "Potential accounting irregularities in Q1 reports",
    description: "Observed discrepancies in financial reporting that may indicate fraudulent activity.",
    category: "Financial",
    priority: "High",
    status: "Awaiting Response",
    isAnonymous: false,
    progress: 65,
    assignedInvestigator: "investigator_001",
    createdAt: new Date("2025-05-10"),
    updatedAt: new Date("2025-05-14"),
    incidentDate: new Date("2025-05-08"),
    location: "Accounting Department, Floor 3",
    tags: ["financial", "fraud", "urgent"],
    metadata: {
      submissionMethod: "web",
      ipAddress: "*************"
    }
  },
  {
    _id: "report_002",
    reportId: "WB-2025-0011",
    userId: "user_001",
    title: "Workplace safety concerns in manufacturing plant B",
    description: "Multiple safety violations observed that could lead to serious injuries.",
    category: "Workplace Safety",
    priority: "Critical",
    status: "Under Review",
    isAnonymous: false,
    progress: 45,
    assignedInvestigator: "investigator_002",
    createdAt: new Date("2025-05-03"),
    updatedAt: new Date("2025-05-12"),
    incidentDate: new Date("2025-05-01"),
    location: "Manufacturing Plant B",
    tags: ["safety", "manufacturing", "critical"]
  },
  {
    _id: "report_003",
    reportId: "WB-2025-0010",
    userId: "user_001",
    title: "Inappropriate conduct by senior manager",
    description: "Reported inappropriate behavior and harassment by senior management.",
    category: "Harassment",
    priority: "High",
    status: "Under Review",
    isAnonymous: false,
    progress: 35,
    assignedInvestigator: "investigator_001",
    createdAt: new Date("2025-04-28"),
    updatedAt: new Date("2025-05-05"),
    incidentDate: new Date("2025-04-25"),
    location: "Corporate Office, Floor 5",
    tags: ["harassment", "management", "urgent"]
  },
  {
    _id: "report_004",
    reportId: "WB-2025-0009",
    userId: "user_001",
    title: "Potential data privacy breach in customer databases",
    description: "Suspected unauthorized access to customer personal information.",
    category: "Data Privacy",
    priority: "Critical",
    status: "Under Review",
    isAnonymous: false,
    progress: 50,
    assignedInvestigator: "investigator_002",
    createdAt: new Date("2025-04-15"),
    updatedAt: new Date("2025-05-08"),
    incidentDate: new Date("2025-04-10"),
    location: "IT Department",
    tags: ["data-privacy", "security", "critical"]
  },
  {
    _id: "report_005",
    reportId: "WB-2025-0008",
    userId: "user_001",
    title: "Conflict of interest in vendor selection process",
    description: "Observed potential conflicts of interest in procurement decisions.",
    category: "Ethics Violation",
    priority: "Medium",
    status: "Resolved",
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: "investigator_001",
    createdAt: new Date("2025-04-02"),
    updatedAt: new Date("2025-04-30"),
    incidentDate: new Date("2025-03-28"),
    location: "Procurement Department",
    tags: ["ethics", "procurement", "resolved"]
  },
  {
    _id: "report_006",
    reportId: "WB-2025-0007",
    userId: "user_001",
    title: "Suspected misuse of company resources by department head",
    description: "Department head using company resources for personal benefit.",
    category: "Ethics Violation",
    priority: "Medium",
    status: "Resolved",
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: "investigator_002",
    createdAt: new Date("2025-03-18"),
    updatedAt: new Date("2025-04-22"),
    incidentDate: new Date("2025-03-15"),
    location: "Marketing Department",
    tags: ["ethics", "resources", "resolved"]
  },
  {
    _id: "report_007",
    reportId: "WB-2025-0006",
    userId: "user_001",
    title: "Environmental compliance concerns at production facility",
    description: "Potential environmental violations at manufacturing site.",
    category: "Workplace Safety",
    priority: "Medium",
    status: "Resolved",
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: "investigator_001",
    createdAt: new Date("2025-03-05"),
    updatedAt: new Date("2025-04-10"),
    incidentDate: new Date("2025-03-01"),
    location: "Production Facility A",
    tags: ["environment", "compliance", "resolved"]
  },
  {
    _id: "report_008",
    reportId: "WB-2025-0005",
    userId: "user_001",
    title: "Fraudulent expense claims by department head",
    description: "Suspicious expense claims that appear to be fraudulent.",
    category: "Financial",
    priority: "High",
    status: "Resolved",
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: "investigator_002",
    createdAt: new Date("2025-02-28"),
    updatedAt: new Date("2025-04-10"),
    incidentDate: new Date("2025-02-20"),
    location: "Finance Department",
    tags: ["financial", "fraud", "resolved"]
  },
  {
    _id: "report_009",
    reportId: "WB-2025-0004",
    userId: "user_001",
    title: "Misuse of company resources for personal gain",
    description: "Employee using company equipment and time for personal projects.",
    category: "Ethics Violation",
    priority: "Medium",
    status: "New",
    isAnonymous: false,
    progress: 0,
    createdAt: new Date("2025-05-15"),
    updatedAt: new Date("2025-05-15"),
    incidentDate: new Date("2025-05-12"),
    location: "Engineering Department",
    tags: ["ethics", "resources", "new"]
  },
  {
    _id: "report_010",
    reportId: "WB-2025-0003",
    userId: "user_001",
    title: "Violation of customer data protection policies",
    description: "Improper handling and storage of customer personal data.",
    category: "Data Privacy",
    priority: "Critical",
    status: "Awaiting Response",
    isAnonymous: false,
    progress: 75,
    assignedInvestigator: "investigator_001",
    createdAt: new Date("2025-05-08"),
    updatedAt: new Date("2025-05-13"),
    incidentDate: new Date("2025-05-05"),
    location: "Customer Service Department",
    tags: ["data-privacy", "customer", "urgent"]
  },
  {
    _id: "report_011",
    reportId: "WB-2025-0002",
    userId: "user_001",
    title: "Unsafe working conditions in warehouse facility",
    description: "Multiple safety hazards observed in warehouse operations.",
    category: "Workplace Safety",
    priority: "High",
    status: "New",
    isAnonymous: false,
    progress: 10,
    createdAt: new Date("2025-04-22"),
    updatedAt: new Date("2025-05-06"),
    incidentDate: new Date("2025-04-20"),
    location: "Warehouse B",
    tags: ["safety", "warehouse", "hazards"]
  },
  {
    _id: "report_012",
    reportId: "WB-2025-0001",
    userId: "user_001",
    title: "Bribery and corruption in procurement process",
    description: "Evidence of bribery in vendor selection and contract awards.",
    category: "Ethics Violation",
    priority: "Critical",
    status: "Resolved",
    isAnonymous: false,
    progress: 100,
    assignedInvestigator: "investigator_002",
    createdAt: new Date("2025-01-15"),
    updatedAt: new Date("2025-03-30"),
    incidentDate: new Date("2025-01-10"),
    location: "Procurement Department",
    tags: ["corruption", "bribery", "resolved"]
  }
];

// Database-ready dashboard statistics
export const mockDashboardStats: DashboardStats = {
  userId: "user_001",
  totalReports: 12,
  newReports: 2,
  underReviewReports: 3,
  awaitingResponseReports: 1,
  resolvedReports: 8,
  lastCalculated: new Date("2025-01-15"),
  periodComparison: {
    totalReportsChange: 2,
    newReportsChange: 1,
    resolvedReportsChange: 3,
    period: "month"
  }
};

// Database-ready notifications
export const mockNotifications: Notification[] = [
  {
    _id: "notif_001",
    userId: "user_001",
    type: "message",
    title: "New Message from Investigator",
    message: "Investigator Alexandra has responded to your report WB-2025-0012",
    status: "unread",
    priority: "high",
    actionUrl: "/dashboard/reports/WB-2025-0012",
    reportId: "report_001",
    createdAt: new Date("2025-01-15T09:22:00Z"),
    updatedAt: new Date("2025-01-15T09:22:00Z"),
    metadata: {
      source: "investigator_response",
      category: "report_update"
    }
  }
];

// Database-ready conversations and messages
export const mockConversations: Conversation[] = [
  {
    _id: "conv_001",
    reportId: "report_001",
    participants: ["user_001", "investigator_001"],
    status: "active",
    isEncrypted: true,
    createdAt: new Date("2025-05-10"),
    updatedAt: new Date("2025-05-14"),
    lastMessageAt: new Date("2025-05-14T09:22:00Z")
  }
];

export const mockMessages: Message[] = [
  {
    _id: "msg_001",
    conversationId: "conv_001",
    senderId: "investigator_001",
    content: "We need additional information regarding the accounting irregularities you reported. Could you please provide specific examples or documents that support your concerns?",
    messageType: "text",
    isEncrypted: true,
    readBy: [],
    createdAt: new Date("2025-05-14T09:22:00Z"),
    updatedAt: new Date("2025-05-14T09:22:00Z")
  }
];

// Table Configuration for Reports
export const reportsTableConfig: TableConfig = {
  columns: [
    {
      key: "select",
      label: "",
      width: "w-12",
      type: "checkbox",
      className: "text-left py-3 px-4 font-bold text-[#6B7271]"
    },
    {
      key: "reportId",
      label: "Report ID",
      width: "w-32",
      type: "text",
      sortable: true,
      className: "text-left py-3 px-4 font-bold text-[#6B7271]"
    },
    {
      key: "title",
      label: "Title",
      width: "min-w-[280px] max-w-[320px]",
      type: "text",
      sortable: true,
      className: "text-left py-3 px-4 font-bold text-[#6B7271]"
    },
    {
      key: "category",
      label: "Category",
      width: "w-28",
      type: "text",
      sortable: true,
      className: "text-left py-3 px-4 font-bold text-[#6B7271]"
    },
    {
      key: "status",
      label: "Status",
      width: "w-32",
      type: "badge",
      sortable: true,
      className: "text-left py-3 px-4 font-bold text-[#6B7271]"
    },
    {
      key: "progress",
      label: "Progress",
      width: "w-36",
      type: "progress",
      sortable: true,
      className: "text-left py-3 px-4 font-bold text-[#6B7271]"
    },
    {
      key: "dateSubmitted",
      label: "Date Submitted",
      width: "w-28",
      type: "date",
      sortable: true,
      className: "text-left py-3 px-4 font-bold text-[#6B7271]"
    },
    {
      key: "lastUpdated",
      label: "Last Updated",
      width: "w-28",
      type: "date",
      sortable: true,
      className: "text-left py-3 px-4 font-bold text-[#6B7271]"
    },
    {
      key: "actions",
      label: "Actions",
      width: "w-36",
      type: "actions",
      className: "text-left py-3 px-4 font-bold text-[#6B7271]"
    }
  ],
  sortable: true,
  selectable: true,
  actions: true
};

// Table Configuration for Dashboard Reports (simplified version)
export const dashboardTableConfig: TableConfig = {
  columns: [
    {
      key: "reportId",
      label: "Report ID",
      type: "text",
      sortable: true,
      className: "font-bold text-xs text-[#6B7271] py-3 px-2 sm:px-4"
    },
    {
      key: "title",
      label: "Title",
      type: "text",
      sortable: true,
      className: "font-bold text-xs text-[#6B7271] py-3 px-2 sm:px-4"
    },
    {
      key: "status",
      label: "Status",
      type: "badge",
      sortable: true,
      className: "font-bold text-xs text-[#6B7271] py-3 px-2 sm:px-4"
    },
    {
      key: "dateSubmitted",
      label: "Date Submitted",
      type: "date",
      sortable: true,
      className: "font-bold text-xs text-[#6B7271] py-3 px-2 sm:px-4 hidden sm:table-cell"
    },
    {
      key: "lastUpdated",
      label: "Last Updated",
      type: "date",
      sortable: true,
      className: "font-bold text-xs text-[#6B7271] py-3 px-2 sm:px-4 hidden md:table-cell"
    },
    {
      key: "actions",
      label: "Action",
      type: "actions",
      className: "font-bold text-xs text-[#6B7271] py-3 px-2 sm:px-4"
    }
  ],
  sortable: true,
  selectable: false,
  actions: true
};
