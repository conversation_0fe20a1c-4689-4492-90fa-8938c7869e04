"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Image from 'next/image';
import { Building2, LockKeyhole } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import LoginForm, { LoginFormData } from '@/components/auth-components/login/shared/LoginForm';
import OAuthButtons from '@/components/auth-components/login/shared/OAuthButtons';
import ForgotPasswordModal from '@/components/auth-components/login/shared/ForgotPasswordModal';
import { useAuth } from '@/hooks/useAuth';

export default function WhistleblowerLoginPage() {
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState("login");
    const [showTrackReport, setShowTrackReport] = useState(false);
    const [companyName, setCompanyName] = useState("");
    const [referenceNumber, setReferenceNumber] = useState("");
    const [isAnonymousLoading, setIsAnonymousLoading] = useState(false);
    const [isTrackingLoading, setIsTrackingLoading] = useState(false);
    const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);
    const { login } = useAuth();

    const handleLogin = async (data: LoginFormData) => {
        setIsLoading(true);
        try {
            const success = await login(data.email, data.password, 'whistleblower');

            if (success) {
                toast({
                    title: "Login Successful",
                    description: "Welcome back! Redirecting to whistleblower dashboard..."
                });
                // Redirect to dashboard after successful login
                window.location.href = '/dashboard/whistleblower';
            } else {
                throw new Error('Invalid credentials');
            }
        } catch (error) {
            console.error('Login error:', error);
            toast({
                title: "Login Failed",
                description: error instanceof Error ? error.message : "Invalid credentials",
                variant: "destructive"
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleAnonymousAccess = async () => {
        if (!companyName.trim()) {
            toast({
                title: "Company Name Required",
                description: "Please enter your company name to continue",
                variant: "destructive"
            });
            return;
        }

        setIsAnonymousLoading(true);
        try {
            const response = await fetch('/api/auth/anonymous', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ companyName: companyName.trim() }),
            });

            const result = await response.json();

            if (result.success) {
                toast({
                    title: "Anonymous Access Granted",
                    description: "Redirecting to anonymous reporting portal..."
                });
                // Store anonymous session info
                localStorage.setItem('anonymousSession', JSON.stringify({
                    companyName: companyName.trim(),
                    sessionId: result.sessionId,
                    expiresAt: result.expiresAt
                }));
                window.location.href = '/dashboard/anonymous';
            } else {
                throw new Error(result.error || 'Failed to create anonymous session');
            }
        } catch (error) {
            console.error('Anonymous access error:', error);
            toast({
                title: "Anonymous Access Failed",
                description: error instanceof Error ? error.message : "Failed to create anonymous session",
                variant: "destructive"
            });
        } finally {
            setIsAnonymousLoading(false);
        }
    };

    const handleTrackReport = async () => {
        if (!referenceNumber.trim()) {
            toast({
                title: "Reference Number Required",
                description: "Please enter your report reference number",
                variant: "destructive"
            });
            return;
        }

        setIsTrackingLoading(true);
        try {
            const response = await fetch(`/api/reports/track/${encodeURIComponent(referenceNumber.trim())}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            const result = await response.json();

            if (result.success) {
                toast({
                    title: "Report Found",
                    description: "Redirecting to report tracking page..."
                });
                window.location.href = `/track-report/${encodeURIComponent(referenceNumber.trim())}`;
            } else {
                throw new Error(result.error || 'Report not found');
            }
        } catch (error) {
            console.error('Report tracking error:', error);
            toast({
                title: "Report Not Found",
                description: error instanceof Error ? error.message : "Unable to find report with this reference number",
                variant: "destructive"
            });
        } finally {
            setIsTrackingLoading(false);
        }
    };



    return (
        <div className="min-h-screen flex items-center justify-center lg:h-screen">
            <div className="flex flex-col lg:flex-row w-full h-full overflow-hidden">
                {/* Left side */}
                <div className="w-full lg:w-1/2 xl:w-1/2 px-4 sm:px-6 md:px-8 lg:px-16 xl:px-24 2xl:px-32 py-8 lg:py-0 flex flex-col justify-center">
                    <div className="flex items-center mb-6 lg:mb-8">
                        <Link href={"/"}>
                            <Image src="/logo.svg" alt="IRIS Logo" width={40} height={40} className="h-8 w-auto sm:h-10 lg:h-12 mr-3" />
                        </Link>
                    </div>
                    <h2 className="text-xl sm:text-2xl lg:text-3xl xl:text-2xl font-semibold mb-2">Welcome Back</h2>
                    <p className="text-gray-500 mb-4 sm:mb-6 text-sm sm:text-base">Log in to your account to Continue</p>
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-4 sm:mb-6">
                        <TabsList className="flex border-b w-full bg-transparent p-0 h-auto relative">
                            <TabsTrigger
                                value="login"
                                className="flex-1 py-2 sm:py-3 text-xs sm:text-sm lg:text-base data-[state=active]:border-b-2 data-[state=active]:text-[#1E4841] data-[state=active]:shadow-none data-[state=active]:bg-transparent data-[state=active]:font-semibold data-[state=inactive]:text-gray-400 font-medium rounded-none bg-transparent transition-colors duration-300"
                            >
                                Login
                            </TabsTrigger>
                            <TabsTrigger
                                value="anonymous"
                                className="flex-1 py-2 sm:py-3 text-xs sm:text-sm lg:text-base data-[state=active]:border-b-2 data-[state=active]:text-[#1E4841] data-[state=active]:shadow-none data-[state=active]:bg-transparent data-[state=active]:font-semibold data-[state=inactive]:text-gray-400 font-medium rounded-none bg-transparent transition-colors duration-300"
                            >
                                <span className="hidden sm:inline">Anonymous Access</span>
                                <span className="sm:hidden">Anonymous</span>
                            </TabsTrigger>
                            <div
                                className="absolute bottom-0 h-0.5 bg-[#1E4841] transition-all duration-300 ease-in-out"
                                style={{
                                    left: activeTab === "login" ? "0" : "50%",
                                    width: "50%",
                                }}
                            />
                        </TabsList>
                        <TabsContent value="login" className="transition-all duration-300 data-[state=inactive]:opacity-0 data-[state=active]:opacity-100">
                            {/* <div className="text-center text-xs sm:text-sm text-gray-600 p-3 sm:p-4 bg-blue-50 rounded-lg mb-3 sm:mb-4">
                                <p className="font-semibold mb-2">🔑 Employee Test Accounts:</p>
                                <div className="text-xs space-y-2">
                                    <div className="break-words">
                                        <strong className="text-blue-700">TechCorp Industries:</strong><br />
                                        📧 <EMAIL> / employee123
                                    </div>
                                    <div className="break-words">
                                        <strong className="text-green-700">Global Manufacturing Ltd:</strong><br />
                                        📧 <EMAIL> / employee123
                                    </div>
                                </div>
                            </div> */}

                            {/* OAuth Buttons */}
                            <OAuthButtons
                                callbackUrl="/dashboard/whistleblower"
                                role="whistleblower"
                            />

                            <div className="relative my-6">
                                <div className="relative">
                                    <div className="absolute inset-0 flex items-center">
                                        <span className="w-full border-t" />
                                    </div>
                                    <div className="relative flex justify-center text-xs uppercase">
                                        <span className="bg-white px-2 text-gray-500">OR</span>
                                    </div>
                                </div>
                            </div>

                            <LoginForm
                                onSubmit={handleLogin}
                                isLoading={isLoading}
                                buttonText="Log in"
                                onForgotPassword={() => setShowForgotPasswordModal(true)}
                            />

                            <div className="text-center mt-4 text-black">
                                Don&apos;t have an account yet?{" "}
                                <Link
                                    href="/signup/whistleblower"
                                    className="text-[#1E4841] hover:underline text-sm font-semibold"
                                >
                                    Sign Up
                                </Link>
                            </div>
                        </TabsContent>
                        <TabsContent value="anonymous" className="transition-all duration-300 data-[state=inactive]:opacity-0 data-[state=active]:opacity-100">
                            <div className="space-y-4 sm:space-y-6">
                                <p className="font-medium text-sm sm:text-base mt-3 sm:mt-4">
                                    Access our Secure reporting system without providing your identity.
                                    <br className="hidden sm:block" />
                                    <span className="block sm:inline">Your Privacy is our priority</span>
                                </p>

                                <div className="space-y-4 sm:space-y-6">
                                    <div className='space-y-2'>
                                        <Label className="text-xs sm:text-sm font-bold text-gray-700">Company Name</Label>
                                        <div className="relative">
                                            <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                                                <Building2 className='h-4 w-4 text-gray-400' />
                                            </span>
                                            <Input
                                                type="text"
                                                value={companyName}
                                                onChange={(e) => setCompanyName(e.target.value)}
                                                className="pl-10 h-10 sm:h-12 w-full text-sm sm:text-base"
                                                placeholder="Enter your company name"
                                            />
                                        </div>
                                    </div>

                                    <Button
                                        type="button"
                                        size="lg"
                                        onClick={handleAnonymousAccess}
                                        disabled={isAnonymousLoading}
                                        className="w-full bg-[#1E4841] hover:bg-[#1E4841E0] text-white font-semibold flex items-center justify-center gap-2 h-10 sm:h-12 text-sm sm:text-base"
                                    >
                                        <LockKeyhole className="h-4 w-4 sm:h-5 sm:w-5" />
                                        {isAnonymousLoading ? "Connecting..." : "Continue as Anonymous"}
                                    </Button>
                                </div>

                                <div className="space-y-4 sm:space-y-6">
                                    <div className="space-y-1 flex flex-col justify-center items-center">
                                        <p className="text-gray-600 text-sm sm:text-base">Already Submitted a report?</p>
                                        <Label
                                            onClick={() => setShowTrackReport(!showTrackReport)}
                                            className="text-[#1E4841] hover:underline font-medium text-base sm:text-lg cursor-pointer">
                                            Track Your Report
                                        </Label>
                                    </div>

                                    <div className={`space-y-4 sm:space-y-6 overflow-hidden transition-all duration-300 ${showTrackReport ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                                        <div className="space-y-2">
                                            <Label className="text-xs sm:text-sm font-medium text-gray-700">Reference Number</Label>
                                            <Input
                                                type="text"
                                                value={referenceNumber}
                                                onChange={(e) => setReferenceNumber(e.target.value)}
                                                className="w-full h-10 sm:h-12 text-sm sm:text-base"
                                                placeholder="Enter your reference number (e.g., RPT-XXXXX-XXXXX)"
                                            />
                                        </div>

                                        <Button
                                            type="button"
                                            variant="outline"
                                            size="lg"
                                            onClick={handleTrackReport}
                                            disabled={isTrackingLoading}
                                            className="w-full bg-[#ECF4E9] text-[#1E4841] hover:bg-[#ECF4E9B3] font-medium h-10 sm:h-12 text-sm sm:text-base"
                                        >
                                            {isTrackingLoading ? "Searching..." : "Track Status"}
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </TabsContent>
                    </Tabs>
                </div>
                {/* Right side */}
                <div className="hidden lg:block lg:w-1/2 xl:w-1/2 relative">
                    <Image
                        src="/(auth)/login/office.png"
                        alt="Office"
                        fill
                        className="object-cover w-auto h-full"
                        style={{ objectFit: 'cover' }}
                        sizes="(max-width: 1024px) 0vw, 50vw"
                    />
                    <div className="absolute inset-0 bg-[#1E48419E] rounded-3xl h-3/5 w-4/5 z-10 m-auto" />
                </div>
            </div>

            <ForgotPasswordModal
                isOpen={showForgotPasswordModal}
                onClose={() => setShowForgotPasswordModal(false)}
            />
        </div>
    );
}
