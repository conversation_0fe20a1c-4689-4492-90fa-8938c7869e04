import React from "react";
import Image from "next/image";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Check } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Roboto } from 'next/font/google';
import {
  ProductFeature,
  ProductStep,
  ProductSecurity,
  ProductTestimonial,
  ProductSpace,
  ProductReport
} from "@/lib/types";

const roboto = Roboto({
  weight: ['400', '700'],
  subsets: ['latin'],
});

interface StepDescription {
  p1: string;
  p2: string;
  p3: string;
}

// Feature Card Component
export const FeatureCard: React.FC<ProductFeature> = ({ icon: Icon, title, description, alt, width, height }) => (
  <Card className="w-full max-w-4xl flex p-2 shadow-none border-none hover:border hover:shadow-md transition-shadow duration-300 gap-0">
    <CardHeader className="flex flex-col items-start justify-between gap-2 p-2">
      <div className="w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 rounded-full bg-[#ECF4E9] flex items-center justify-center">
        {typeof Icon === 'string' ? (
          <Image src={Icon} alt={alt || title} width={width || 24} height={height || 24} />
        ) : (
          <Icon className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7" />
        )}
      </div>
      <CardTitle className="font-semibold text-left text-lg sm:text-xl text-[#242E2C]">{title}</CardTitle>
      <CardDescription className="text-sm sm:text-base text-left font-normal text-[#6B7271]">{description}</CardDescription>
    </CardHeader>
  </Card>
);

// Step Card Component for Whistleblower
export const WhistleblowerStepCard: React.FC<{ step: ProductStep }> = ({ step }) => {
  const { icon, title, description, image } = step;
  const stepDescription = description as StepDescription;
  
  return (
    <Card className="w-full max-w-4xl flex shadow-none border-none hover:border hover:shadow-md transition-shadow duration-300 gap-0">
      <CardHeader className="flex flex-col items-center justify-between h-full gap-2">
        <div className={`w-10 h-10 sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-[#1E4841] p-3 sm:p-4 rounded-full flex items-center justify-center text-white text-bold text-lg sm:text-xl md:text-2xl lg:text-3xl ${roboto.className}`}>
          {typeof icon === 'string' || typeof icon === 'number' ? icon : React.createElement(icon as React.ComponentType)}
        </div>
        <CardTitle className="font-semibold text-center text-lg sm:text-xl text-[#242E2C]">{title}</CardTitle>
        <CardDescription className="flex flex-col justify-start h-full text-xs sm:text-sm text-left font-semibond text-[#4B5563] gap-1.5 sm:gap-2">
          <div className="flex items-start gap-2">
            <Check className="mt-0.5 sm:mt-1 min-w-3 min-h-3 w-3 h-3 sm:min-w-4 sm:min-h-4 sm:w-4 sm:h-4" />
            <span>{stepDescription.p1}</span>
          </div>
          <div className="flex items-start gap-2">
            <Check className="mt-0.5 sm:mt-1 min-w-3 min-h-3 w-3 h-3 sm:min-w-4 sm:min-h-4 sm:w-4 sm:h-4" />
            <span>{stepDescription.p2}</span>
          </div>
          <div className="flex items-start gap-2">
            <Check className="mt-0.5 sm:mt-1 min-w-3 min-h-3 w-3 h-3 sm:min-w-4 sm:min-h-4 sm:w-4 sm:h-4" />
            <span>{stepDescription.p3}</span>
          </div>
        </CardDescription>
        {image && (
          <Image
            src={image}
            alt="Step Card"
            width={100}
            height={100}
            className="w-full h-auto mt-2"
          />
        )}
      </CardHeader>
    </Card>
  );
};

// Step Card Component for Investigator
export const InvestigatorStepCard: React.FC<{ step: ProductStep }> = ({ step }) => {
  const { icon, alt, width, height, title, description, features } = step;
  
  return (
    <Card className="w-full max-w-4xl flex flex-col sm:justify-between h-full p-2 shadow-none border-none hover:border hover:shadow-md transition-shadow duration-300 gap-0">
      <CardHeader className="flex flex-col items-center justify-between h-full gap-2 p-3 sm:p-4 md:p-6">
        <div className={`z-2 w-10 h-10 sm:w-12 sm:h-12 md:w-12 md:h-12 bg-[#ECF4E9] p-2 rounded-full flex items-center justify-center text-[#1E4841] text-bold text-xl sm:text-2xl md:text-3xl ${roboto.className}`}>
          {typeof icon === 'string' ? (
            <Image src={icon} alt={alt || title} width={width || 24} height={height || 24} />
          ) : typeof icon === 'number' ? (
            icon
          ) : (
            React.createElement(icon as React.ComponentType)
          )}
        </div>
        <CardTitle className="font-semibold text-center text-base sm:text-lg text-[#242E2C]">{title}</CardTitle>
        <CardDescription className="font-medium text-sm sm:text-base text-center text-[#6B7271]">
          {typeof description === 'string' ? description : description.p1}
        </CardDescription>
      </CardHeader>
      {features && (
        <CardContent className="flex flex-col h-full justify-end text-xs sm:text-sm text-left font-medium text-[#6B7271] gap-2 mt-4 sm:mt-8 px-3 sm:px-6">
          <div className="flex items-start gap-2">
            <Check className="mt-0.5 md:mt-1 min-w-3 min-h-3 w-3 h-3 sm:min-w-4 sm:min-h-4 sm:w-4 sm:h-4" />
            <span>{features.p1}</span>
          </div>
          <div className="flex items-start gap-2">
            <Check className="mt-0.5 md:mt-1 min-w-3 min-h-3 w-3 h-3 sm:min-w-4 sm:min-h-4 sm:w-4 sm:h-4" />
            <span>{features.p2}</span>
          </div>
          <div className="flex items-start gap-2">
            <Check className="mt-0.5 md:mt-1 min-w-3 min-h-3 w-3 h-3 sm:min-w-4 sm:min-h-4 sm:w-4 sm:h-4" />
            <span>{features.p3}</span>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

// Space Card Component
export const SpaceCard: React.FC<ProductSpace> = ({ icon: Icon, title, description }) => (
  <Card className="w-full max-w-4xl flex p-2 shadow-none border-none hover:border hover:shadow-md transition-shadow duration-300 gap-0">
    <CardHeader className="flex items-start gap-2 p-2">
      <div className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-[#ECF4E9] p-2 sm:p-3 flex items-center justify-center">
        <Icon className="w-3 h-3 sm:w-4 sm:h-4" />
      </div>
      <div className="flex flex-col gap-1.5 sm:gap-2 mt-1 sm:mt-1.5">
        <CardTitle className="font-semibold text-left text-lg sm:text-xl text-[#242E2C]">{title}</CardTitle>
        <CardDescription className="text-sm sm:text-base text-left font-normal text-[#6B7271]">{description}</CardDescription>
      </div>
    </CardHeader>
  </Card>
);

// Report Card Component
export const ReportCard: React.FC<ProductReport> = ({ icon: Icon, title, description }) => (
  <Card className="w-full max-w-4xl flex p-2 border-none hover:border shadow-md hover:shadow-lg transition-shadow duration-300 gap-0">
    <CardHeader className="flex flex-col items-start justify-between gap-2 p-2">
      <div className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full bg-[#ECF4E9] flex items-center justify-center">
        <Icon className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8" />
      </div>
      <CardTitle className="font-semibold text-left text-lg sm:text-xl text-[#242E2C]">{title}</CardTitle>
      <CardDescription className="text-sm sm:text-base text-left font-normal text-[#6B7271]">{description}</CardDescription>
    </CardHeader>
  </Card>
);

// Security Card Component
export const SecurityCard: React.FC<ProductSecurity> = ({ icon: Icon, title, description, alt, width, height }) => (
  <Card className="w-full max-w-4xl bg-[#10262520] backdrop-blur-sm flex p-3 sm:p-4 md:p-5 border border-white/20 rounded-2xl shadow-md hover:shadow-lg transition-shadow duration-300 gap-0">
    <CardHeader className="flex flex-col items-start justify-between gap-2 p-1 sm:p-2">
      <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-[#ECF4E9] mb-1 sm:mb-2 flex items-center justify-center">
        {typeof Icon === 'string' ? (
          <Image src={Icon} alt={alt || title} width={width || 24} height={height || 24} className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
        ) : (
          <Icon className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
        )}
      </div>
      <CardTitle className="font-semibold text-left text-base sm:text-lg text-white">{title}</CardTitle>
      <CardDescription className="text-sm sm:text-base text-left font-normal text-[#D1D5DB]">{description}</CardDescription>
    </CardHeader>
  </Card>
);

// Testimonial Card Component
export const TestimonialCard: React.FC<ProductTestimonial> = ({ quote, role, name, company, avatar }) => (
  <div className="flex flex-col items-center gap-4">
    <p className="px-4 sm:px-8 md:px-20 lg:px-40 mb-6 sm:mb-8 md:mb-10 text-lg sm:text-xl md:text-2xl font-normal text-[#374151] text-center italic">{quote}</p>
    <div className="flex items-center gap-3 sm:gap-4">
      <Avatar className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 rounded-none">
        <AvatarImage src={avatar} alt={`Testimonial by ${role}${company ? `, ${company}` : ''}`} />
        <AvatarFallback>User</AvatarFallback>
      </Avatar>
      <div className="flex flex-col">
        {name && <CardTitle className="font-semibold text-left text-sm sm:text-base">{name}</CardTitle>}
        <CardDescription className="text-sm sm:text-base text-left font-normal">{role}{company ? `, ${company}` : ''}</CardDescription>
      </div>
    </div>
  </div>
);