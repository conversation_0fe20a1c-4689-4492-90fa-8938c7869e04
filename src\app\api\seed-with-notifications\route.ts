import { NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function GET() {
  try {
    await connectDB();
    
    return NextResponse.json({
      success: true,
      message: 'Database seeding endpoint available. Use external scripts for seeding.',
      note: 'Run `npm run seed:refined` or `npm run add-test-users` to seed the database'
    });
    
  } catch (error) {
    console.error('Database connection failed:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Database connection failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST() {
  // Allow POST requests as well for flexibility
  return GET();
}