// Admin Dashboard Mock Data
export const adminDashboardStats = {
    totalCases: 248,
    slaViolations: 12,
    investigatorWorkload: 18,
    highRiskReports: 24,
    newCases: 37,
    inProgressCases: 92,
    closedCases: 119,
    slaViolationPercentage: 15,
    activeInvestigators: 58,
    highRiskPercentage: 9.7,
    managementPercentage: 4.2
};

export const adminUser = {
    name: "<PERSON>",
    role: "Admin",
    language: "English",
    lastLogin: "Last 30 days"
};

export const adminRecentCases = [
    {
        id: "WB-2025-089",
        subject: "Potential accounting fraud",
        department: "Finance",
        status: "New",
        priority: "Not Set",
        date: "Jun 3, 2025",
        timeAgo: "10 minutes ago"
    },
    {
        id: "WB-2025-088",
        subject: "Workplace harassment claim",
        department: "HR",
        status: "In Progress",
        priority: "Medium",
        date: "Jun 2, 2025",
        timeAgo: "42 minutes ago"
    },
    {
        id: "WB-2025-087",
        subject: "Data privacy breach",
        department: "IT",
        status: "In Progress",
        priority: "High",
        date: "Jun 1, 2025",
        timeAgo: "1 hour ago"
    },
    {
        id: "WB-2025-086",
        subject: "Conflict of interest",
        department: "Legal",
        status: "Closed",
        priority: "Low",
        date: "May 31, 2025",
        timeAgo: "2 hours ago"
    },
    {
        id: "WB-2025-085",
        subject: "Health & safety violation",
        department: "Operations",
        status: "Escalated",
        priority: "High",
        date: "May 30, 2025",
        timeAgo: "3 hours ago"
    }
];

export const adminRecentActivity = [
    {
        id: 1,
        action: "New case #WB-2025-089 submitted",
        timeAgo: "10 minutes ago",
        type: "case_created"
    },
    {
        id: 2,
        action: "Michael Roberts assigned to case #WB-2025-087",
        timeAgo: "42 minutes ago",
        type: "assignment"
    },
    {
        id: 3,
        action: "New message in case #WB-2025-082",
        timeAgo: "1 hour ago",
        type: "message"
    },
    {
        id: 4,
        action: "SLA breach for case #WB-2025-076",
        timeAgo: "2 hours ago",
        type: "sla_breach"
    },
    {
        id: 5,
        action: "Case #WB-2025-071 closed by Katherine",
        timeAgo: "3 hours ago",
        type: "case_closed"
    }
];

export const adminCaseDistribution = [
    { category: "Fraud", count: 30, percentage: 30 },
    { category: "Harassment", count: 25, percentage: 25 },
    { category: "Ethics", count: 20, percentage: 20 },
    { category: "System", count: 15, percentage: 15 },
    { category: "Other", count: 10, percentage: 10 }
];

export const adminCaseTrends = {
    daily: [
        { period: "Week 1", newCases: 12, closedCases: 8 },
        { period: "Week 2", newCases: 15, closedCases: 10 },
        { period: "Week 3", newCases: 18, closedCases: 14 },
        { period: "Week 4", newCases: 20, closedCases: 16 }
    ],
    weekly: [
        { period: "Week 1", newCases: 45, closedCases: 32 },
        { period: "Week 2", newCases: 52, closedCases: 38 },
        { period: "Week 3", newCases: 48, closedCases: 41 },
        { period: "Week 4", newCases: 55, closedCases: 44 }
    ],
    monthly: [
        { period: "Jan", newCases: 180, closedCases: 165 },
        { period: "Feb", newCases: 195, closedCases: 178 },
        { period: "Mar", newCases: 210, closedCases: 192 },
        { period: "Apr", newCases: 225, closedCases: 208 }
    ]
};

export const adminInvestigators = [
    { initials: "JD", name: "John Doe", active: true },
    { initials: "MR", name: "Michael Roberts", active: true },
    { initials: "KL", name: "Katherine Lee", active: true },
    { initials: "AB", name: "Alice Brown", active: false },
    { initials: "CD", name: "Chris Davis", active: true }
];

export const adminNotifications = {
    total: 4,
    unread: 2,
    items: [
        {
            id: 1,
            title: "SLA Breach Alert",
            message: "Case #WB-2025-076 has exceeded SLA deadline",
            time: "2 hours ago",
            type: "warning",
            read: false
        },
        {
            id: 2,
            title: "High Priority Case",
            message: "New high priority case #WB-2025-087 requires immediate attention",
            time: "1 hour ago",
            type: "urgent",
            read: false
        },
        {
            id: 3,
            title: "Case Assignment",
            message: "Michael Roberts assigned to case #WB-2025-087",
            time: "42 minutes ago",
            type: "info",
            read: true
        },
        {
            id: 4,
            title: "Case Closed",
            message: "Case #WB-2025-071 has been successfully closed",
            time: "3 hours ago",
            type: "success",
            read: true
        }
    ]
};