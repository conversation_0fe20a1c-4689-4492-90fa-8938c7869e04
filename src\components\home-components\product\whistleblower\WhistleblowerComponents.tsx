import React from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  FeatureCard, 
  WhistleblowerStepCard, 
  SpaceCard, 
  ReportCard, 
  SecurityCard 
} from "../shared/ProductCards";
import { 
  whistleblowerFeatures,
  whistleblowerSteps,
  whistleblowerSpaces,
  whistleblowerReports,
  whistleblowerSecurities
} from "@/lib/mockData/productsData";

// Hero Section Component
export const WhistleblowerHero: React.FC = () => (
  <section className="mx-auto px-4 py-12 sm:px-8 sm:py-16 md:px-12 md:py-20 lg:px-24 lg:py-28 xl:px-[180px] xl:py-[140px] bg-gradient-to-r from-[#1E4841]/95 from-90% via-[#1E4841]/90 via-98% to-[#1E4841]/85 relative">
    <Image
      src="/desktop/products/whistleblower/hero.svg"
      alt="Whistleblower background"
      fill
      className="object-cover mix-blend-luminosity opacity-20"
      priority
    />
    <div className="relative z-10 text-center md:text-left">
      <p className="text-3xl leading-tight sm:text-4xl sm:leading-tight md:text-4xl/14 xl:text-5xl/16 font-bold text-white mb-4 sm:mb-5 md:mb-6 w-full sm:w-3/4 md:w-2/3 xl:w-1/2">Speak Up Safely. Stay Anonymous.</p>
      <p className="text-lg sm:text-xl text-white mb-6 sm:mb-7 md:mb-8 w-full sm:w-4/5 md:w-3/4 xl:w-2/3">Empower your people to report sensitive concerns — anonymously or by identity — through a secure, easy-to-use reporting platform.</p>
      <Button
        variant="default"
        className="px-5 py-3 sm:px-6 sm:py-4 md:px-7 md:py-5 lg:px-8 lg:py-6 xl:px-9 xl:py-7 text-base sm:text-lg md:text-lg xl:text-xl text-[#1E4841] bg-[#BBF49C] border border-[#BBF49C] hover:border-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 transition-all duration-300 shadow-sm font-semibold"
        aria-label="Try a test report"
      >
        Try a Test Report
      </Button>
    </div>
  </section>
);

// Features Section Component
export const WhistleblowerFeatures: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841] mb-2 text-center">A Powerful, Confidential Whistleblowing Channel</p>
    <p className="px-2 sm:px-4 md:px-8 lg:px-16 xl:px-55 text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563] mb-6 text-center">Whistleblower is a modern reporting solution built to protect employees, vendors, and partners while giving companies the tools to respond transparently and in full compliance with global regulations. Users can file reports anonymously or through verified logins, ensuring flexibility and safety for every voice.</p>
    <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 md:gap-12 lg:gap-16 xl:gap-20 w-full px-2 sm:px-4 md:px-8 lg:px-12 xl:px-15">
      {whistleblowerFeatures.map((feature, index) => (
        <FeatureCard key={index} {...feature} />
      ))}
    </div>
  </section>
);

// Steps Section Component
export const WhistleblowerSteps: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841] mb-12 sm:mb-16 md:mb-18 xl:mb-20 text-center">How Reporting Works</p>
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-4 sm:gap-6 md:gap-6 lg:gap-7 xl:gap-8 w-full">
      {whistleblowerSteps.map((step, index) => (
        <WhistleblowerStepCard key={index} step={step} />
      ))}
    </div>
  </section>
);

// Private Space Section Component
export const WhistleblowerPrivateSpace: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841] mb-2 text-center">Your Private Follow-Up Space</p>
    <p className="px-2 sm:px-4 md:px-8 lg:px-16 xl:px-55 text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563] mb-12 sm:mb-14 md:mb-16 text-center">Even anonymous reporters get their own space to track progress. For those who identify, additional options like email updates and history logs are available.</p>
    <div className="flex flex-col lg:flex-row justify-between items-center gap-0 xl:gap-8 w-full">
      <Image
        src="/desktop/products/whistleblower/space.svg"
        alt="Private Space"
        width={625}
        height={600}
        className="h-auto w-full max-w-sm sm:max-w-md lg:max-w-md xl:max-w-3xl lg:w-400"
      />
      <div className="grid grid-rows-1 sm:grid-rows-2 md:grid-rows-3 lg:grid-rows-4 xl:grid-rows-4 w-full lg:mt-10 xl:mt-6 px-2 sm:px-4 lg:px-4 xl:px-4 gap-2">
        {whistleblowerSpaces.map((space, index) => (
          <SpaceCard key={index} {...space} />
        ))}
        <div className="flex justify-center lg:justify-start">
          <Button variant="outline" className="w-fit bg-[#1E4841] text-white hover:bg-green-900 hover:text-gray-100 xl:mt-2 py-4 sm:py-5 px-4 sm:px-6 transition-all duration-300 font-medium flex items-center gap-2 text-sm sm:text-base">
            Try Demo Platform
          </Button>
        </div>
      </div>
    </div>
  </section>
);

// Reports Section Component
export const WhistleblowerReports: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841] mb-2 text-center">What Can Be Reported?</p>
    <p className="px-2 sm:px-4 md:px-8 lg:px-16 xl:px-55 text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563] mb-6 text-center">Whistleblower can be used for a variety of workplace, corporate, and vendor concerns.</p>
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 md:gap-12 lg:gap-16 xl:gap-20 w-full px-2 sm:px-4 md:px-8 lg:px-12 xl:px-15">
      {whistleblowerReports.map((report, index) => (
        <ReportCard key={index} {...report} />
      ))}
    </div>
  </section>
);

// Security Section Component
export const WhistleblowerSecurity: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center bg-[#1E4841]">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-white mb-2 text-center">Enterprise-Grade Security, Guaranteed Privacy</p>
    <p className="px-2 sm:px-4 md:px-8 lg:px-20 xl:px-65 text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#D1D5DB] mb-10 sm:mb-12 md:mb-14 text-center">Our platform is built from the ground up with security and privacy as core principles, not afterthoughts.</p>
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-4 sm:gap-6 md:gap-6 lg:gap-6 xl:gap-6 w-full px-2 sm:px-4 md:px-8 lg:px-12 xl:px-15">
      {whistleblowerSecurities.map((security, index) => (
        <SecurityCard key={index} {...security} />
      ))}
    </div>
    <div className="flex flex-wrap justify-center items-center gap-3 sm:gap-4 md:gap-6 lg:gap-8 xl:gap-8 mt-4 sm:mt-5 md:mt-6 w-full">
      <p className="bg-[#10262520] text-white backdrop-blur-sm p-3 sm:p-4 md:p-5 border border-white/20 shadow-md hover:shadow-lg rounded-2xl transition-shadow duration-300 text-xs sm:text-sm md:text-base">GDPR Complaint</p>
      <p className="bg-[#10262520] text-white backdrop-blur-sm p-3 sm:p-4 md:p-5 border border-white/20 shadow-md hover:shadow-lg rounded-2xl transition-shadow duration-300 text-xs sm:text-sm md:text-base">SOC 2 Type II</p>
      <p className="bg-[#10262520] text-white backdrop-blur-sm p-3 sm:p-4 md:p-5 border border-white/20 shadow-md hover:shadow-lg rounded-2xl transition-shadow duration-300 text-xs sm:text-sm md:text-base">ISO 27001</p>
      <p className="bg-[#10262520] text-white backdrop-blur-sm p-3 sm:p-4 md:p-5 border border-white/20 shadow-md hover:shadow-lg rounded-2xl transition-shadow duration-300 text-xs sm:text-sm md:text-base">HIPAA Ready</p>
      <p className="bg-[#10262520] text-white backdrop-blur-sm p-3 sm:p-4 md:p-5 border border-white/20 shadow-md hover:shadow-lg rounded-2xl transition-shadow duration-300 text-xs sm:text-sm md:text-base">CCPA Compliant</p>
    </div>
  </section>
);

// CTA Section Component
export const WhistleblowerCTA: React.FC = () => (
  <section className="mx-auto px-4 py-16 sm:px-6 sm:py-18 md:px-8 md:py-20 lg:px-16 lg:py-20 xl:px-40 xl:py-20 flex flex-col items-center">
    <p className="text-xl sm:text-2xl md:text-2xl xl:text-[28px] font-bold text-[#1E4841] mb-3 sm:mb-4 text-center">Ready to Empower Your Organization?</p>
    <p className="px-2 sm:px-4 md:px-20 lg:px-16 xl:px-55 text-sm sm:text-base md:text-base xl:text-lg font-normal text-[#4B5563] mb-6 sm:mb-7 md:mb-8 text-center">Join thousands of forward-thinking companies that prioritize ethical workplace culture and compliance.</p>
    <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center w-full max-w-md sm:max-w-none">
      <Button
        variant="outline"
        className="w-fit p-6 text-base md:text-lg text-[#1E4841] bg-[#BBF49C] hover:text-gray-900 border-1 border-[#BBF49C] hover:border-[#BBF49C] hover:bg-lime-200 transition-all duration-300 shadow-sm font-semibold"
        aria-label="Request a demo of the whistleblower platform"
      >
        Request Demo
      </Button>
      <Button
        variant="outline"
        className="w-fit p-6 text-base md:text-lg text-[#1E4841] hover:bg-[#BBF49C] hover:text-gray-900 border-1 border-[#1E4841] transition-all duration-300 shadow-sm font-semibold"
        aria-label="Contact sales for more information"
      >
        Contact Sales
      </Button>
    </div>
  </section>
);