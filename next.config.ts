import type { NextConfig } from "next";

/**
 * Next.js configuration
 * @see https://nextjs.org/docs/app/api-reference/next-config-js
 */
const nextConfig: NextConfig = {
  // Skip ESLint during build if SKIP_LINT is set
  eslint: {
    ignoreDuringBuilds: process.env.SKIP_LINT === 'true',
  },
  
  // Skip TypeScript checking during build
  typescript: {
    ignoreBuildErrors: process.env.SKIP_LINT === 'true',
  },
  // Enable React strict mode for improved error handling
  reactStrictMode: true,

  // Optimize images from these domains
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**",
      },
    ],
  },

  // Configure experimental features
  experimental: {
    serverActions: {
      bodySizeLimit: "2mb",
    },
    optimisticClientCache: true
  },
  
  // Runtime configuration
  serverRuntimeConfig: {
    // Will only be available on the server side
    // Default to Node.js runtime for server components and API routes
    runtime: 'nodejs'
  },

  // Improve logging during build
  logging: {
    fetches: {
      fullUrl: true,
    },
  },

  // Optimize for production builds
  compiler: {
    removeConsole: process.env.NODE_ENV === "production" ? {
      exclude: ["error", "warn"],
    } : false,
  },

  // Transpile specific modules if needed
  transpilePackages: [],

  // Configure redirects as needed
  redirects: async () => {
    return [];
  },

  // Configure headers for security
  headers: async () => {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
        ],
      },
    ];
  },

  // Enable turbopack (now stable)
  turbopack: {},
  
  // External packages configuration
  serverExternalPackages: [],

  // Turbopack is enabled, so webpack config is removed to avoid conflicts
};

export default nextConfig;