// Real-time notification system
import { Notification } from '@/lib/types';

type NotificationListener = (notifications: Notification[]) => void;
const listeners: NotificationListener[] = [];
let notifications: Notification[] = [];

export const notificationSystem = {
  // Subscribe to notification updates
  subscribe: (listener: NotificationListener) => {
    listeners.push(listener);
    return () => {
      const index = listeners.indexOf(listener);
      if (index > -1) listeners.splice(index, 1);
    };
  },

  // Update notifications and notify all listeners
  updateNotifications: (newNotifications: Notification[]) => {
    notifications = newNotifications;
    listeners.forEach(listener => listener(notifications));
  },

  // Get current notifications
  getNotifications: () => notifications,

  // Get unread count
  getUnreadCount: () => notifications.filter(n => n.status === 'unread').length,

  // Mark as read
  markAsRead: (notificationId: string) => {
    notifications = notifications.map(n => 
      n._id === notificationId ? { ...n, status: 'read' as const } : n
    );
    listeners.forEach(listener => listener(notifications));
  },

  // Add new notification
  addNotification: (notification: Notification) => {
    notifications = [notification, ...notifications];
    listeners.forEach(listener => listener(notifications));
  }
};