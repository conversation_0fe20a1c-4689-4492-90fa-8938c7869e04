import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    role: string;
    companyId?: string;
    email: string;
  };
}

export function withCompanyIsolation(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest) => {
    try {
      // Get token from Authorization header
      const authHeader = request.headers.get('authorization');

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        console.log('Auth header missing or invalid format for', request.method, request.url);
        return NextResponse.json(
          { success: false, error: 'No valid authorization header found' },
          { status: 401 }
        );
      }

      const token = authHeader.substring(7);

      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
        id?: string;
        userId?: string;
        role: string;
        companyId: string;
        email: string;
      };

      // Add user info to request
      const authenticatedRequest = request as AuthenticatedRequest;
      authenticatedRequest.user = {
        id: decoded.id || decoded.userId,
        role: decoded.role,
        companyId: decoded.companyId,
        email: decoded.email
      };

      return await handler(authenticatedRequest);
    } catch (error) {
      console.error('Authentication error for', request.method, request.url, ':', error instanceof Error ? error.message : error);
      return NextResponse.json(
        { success: false, error: 'Invalid or expired token' },
        { status: 401 }
      );
    }
  };
}