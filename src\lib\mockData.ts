// Re-export all mock data from organized files
export * from './mockData/contentData';
export * from './mockData/navigationData';
export * from './mockData/pricingData';
export * from './mockData/productsData';
export * from './mockData/uiData';
export * from './mockData/notificationData';
export { mockMessages as dashboardMessages, mockUsers, mockReports, mockDashboardStats, mockNotifications, mockConversations, reportsTableConfig, dashboardTableConfig } from './mockData/dashboardMockData';
export { secureMessages as secureMessagesData, secureConversations, mockInvestigators, conversationsWithDetails } from './mockData/secureMessagesData';
export * from './mockData/conversationData';
export * from './mockData/blogData';
export * from './mockData/contactData';
export * from './mockData/trackingData';
export * from './mockData/reportsMockData';
export * from './mockData/adminMockData';

// Re-export constants and configuration
export * from './constants';

// Re-export validation schemas
export * from './schemas';
