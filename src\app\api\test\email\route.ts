import { NextRequest, NextResponse } from 'next/server';
import { emailService } from '@/lib/email/emailService';
import logger from '@/lib/utils/logger';

export async function POST(request: NextRequest) {
  try {
    const { to, subject = 'Test Email', message = 'This is a test email from the Whistleblower System.' } = await request.json();

    if (!to) {
      return NextResponse.json(
        { success: false, error: 'Email address is required' },
        { status: 400 }
      );
    }

    // Test email configuration
    const testResult = await emailService.testConnection();
    if (!testResult) {
      return NextResponse.json(
        { success: false, error: 'Email service connection failed' },
        { status: 500 }
      );
    }

    // Send test email
    const emailSent = await emailService.sendEmail({
      to,
      subject,
      text: message,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #1E4841;">Email Service Test</h2>
          <p>${message}</p>
          <p><strong>Configuration:</strong></p>
          <ul>
            <li>Host: ${process.env.EMAIL_SERVER_HOST}</li>
            <li>Port: ${process.env.EMAIL_SERVER_PORT}</li>
            <li>User: ${process.env.EMAIL_SERVER_USER}</li>
            <li>From: ${process.env.EMAIL_FROM}</li>
          </ul>
          <p style="color: #666; font-size: 12px;">
            This is a test email sent at ${new Date().toISOString()}
          </p>
        </div>
      `
    });

    if (emailSent) {
      logger.info('Test email sent successfully', { to, subject });
      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
        config: {
          host: process.env.EMAIL_SERVER_HOST,
          port: process.env.EMAIL_SERVER_PORT,
          user: process.env.EMAIL_SERVER_USER,
          from: process.env.EMAIL_FROM
        }
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send test email' },
        { status: 500 }
      );
    }

  } catch (error) {
    logger.error('Test email error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
