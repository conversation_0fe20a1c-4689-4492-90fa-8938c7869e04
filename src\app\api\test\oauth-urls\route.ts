import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3002';
  
  const oauthUrls = {
    nextAuthUrl: baseUrl,
    googleSignIn: `${baseUrl}/api/auth/signin/google`,
    googleCallback: `${baseUrl}/api/auth/callback/google`,
    microsoftSignIn: `${baseUrl}/api/auth/signin/azure-ad`,
    microsoftCallback: `${baseUrl}/api/auth/callback/azure-ad`,
    currentRequest: request.url,
    headers: {
      host: request.headers.get('host'),
      origin: request.headers.get('origin'),
      referer: request.headers.get('referer')
    }
  };

  return NextResponse.json({
    message: 'OAuth URL Configuration',
    urls: oauthUrls,
    instructions: {
      google: {
        console: 'https://console.cloud.google.com/apis/credentials',
        redirectUri: oauthUrls.googleCallback,
        steps: [
          '1. Go to Google Cloud Console',
          '2. Navigate to APIs & Services → Credentials',
          '3. Edit your OAuth 2.0 Client ID',
          '4. Add the redirect URI above to "Authorized redirect URIs"',
          '5. Save changes and wait 1-2 minutes'
        ]
      },
      microsoft: {
        portal: 'https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationsListBlade',
        redirectUri: oauthUrls.microsoftCallback,
        steps: [
          '1. Go to Azure Portal',
          '2. Navigate to Azure Active Directory → App registrations',
          '3. Select your app',
          '4. Go to Authentication',
          '5. Add the redirect URI above',
          '6. Save changes'
        ]
      }
    }
  });
}
