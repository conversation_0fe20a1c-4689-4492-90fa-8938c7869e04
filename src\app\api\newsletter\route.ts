import { NextRequest, NextResponse } from 'next/server';
import { User } from '@/lib/db/models';
import connectDB from '@/lib/db/mongodb';
import { emailService } from '@/lib/email/emailService';
import logger from '@/lib/utils/logger';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }
    
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json({
        success: true,
        message: 'Already subscribed'
      });
    }

    // Create newsletter subscriber
    await User.create({
      email,
      role: 'whistleblower',
      isActive: true,
      isNewsletterSubscriber: true
    });

    // Send welcome email
    try {
      await emailService.sendNewsletterWelcomeEmail(email);
      logger.info('Newsletter welcome email sent', { email });
    } catch (error) {
      logger.error('Failed to send newsletter welcome email', { email, error });
      // Don't fail the subscription if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Subscribed successfully! Check your email for a welcome message.'
    }, { status: 201 });
  } catch (error) {
    logger.error('Newsletter API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to subscribe to newsletter. Please try again.' },
      { status: 500 }
    );
  }
}